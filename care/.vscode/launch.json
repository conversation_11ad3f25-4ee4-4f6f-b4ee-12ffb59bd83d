{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Docker: Django",
      "type": "python",
      "request": "attach",
      "pathMappings": [
        {
          "localRoot": "${workspaceFolder}",
          "remoteRoot": "/app"
        }
      ],
      "port": 9876,
      "host": "127.0.0.1",
    },
    {
      "name": "Python: Django",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/manage.py",
      "args": [
        "runserver_plus",
        "9000",
        "--print-sql"
      ],
      "env": {
        "DATABASE_URL": "postgres://postgres:postgres@localhost:5433/care"
      },
      "django": true,
      "justMyCode": false
    },
    {
      "name": "Python: Django Make Migrations",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/manage.py",
      "args": [
        "makemigrations"
      ],
      "env": {
        "DATABASE_URL": "postgres://postgres:postgres@localhost:5433/care"
      },
      "django": true,
      "justMyCode": false
    },
    {
      "name": "Python: Django Migrate",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/manage.py",
      "args": [
        "migrate"
      ],
      "env": {
        "DATABASE_URL": "postgres://postgres:postgres@localhost:5433/care"
      },
      "django": true,
      "justMyCode": false
    },
    {
      "name": "Python: Django test",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/manage.py",
      "env": {
        "DATABASE_URL": "postgres://postgres:postgres@localhost:5433/care"
      },
      "args": [
        "test",
        "--keepdb"
      ],
      "django": true,
      "justMyCode": false
    },
  ]
}
