{"[html]": {"editor.formatOnSave": false}, "[python]": {"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit"}, "editor.defaultFormatter": "charliermarsh.ruff"}, "files.associations": {"*.envrc": "shellscript", "*.env": "shellscript"}, "files.insertFinalNewline": true, "files.readonlyInclude": {".venv/**": true}, "files.trimFinalNewlines": true, "files.trimTrailingWhitespace": true, "githubPullRequests.ignoredPullRequestBranches": ["develop", "staging"], "python.languageServer": "<PERSON><PERSON><PERSON>", "cSpell.words": ["Typst"]}