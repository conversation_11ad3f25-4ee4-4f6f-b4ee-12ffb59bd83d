Django Commands
===============

All Django commands must be executed by invoking :code:`python manage.py`, so for example, if the command is :code:`do_something` then the correct way to call the command is :code:`python manage.py do_something`. Make sure that the command is run in the root directory of the project and any virtual environment present must be activated before running the command.

+---------------------+---------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
|     command         |                           Arguments                           |                                                                                                                                                           Description                                                                                                                                                            |
+=====================+===============================================================+==================================================================================================================================================================================================================================================================================================================================+
| createsuperuser     | None                                                          | This command is used to create a superuser in the care application, Superusers are able to log in through the Django Admin panel and Access Scopes do not apply to them. Please take caution when creating them, apply strict password policies for accounts created as superusers. ideally, there should only be one superuser. |
|                     |                                                               |                                                                                                                                                                                                                                                                                                                                  |
|                     |                                                               | Example Invocation: :code:`python manage.py createsuperuser`                                                                                                                                                                                                                                                                     |
+---------------------+---------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| load_dummy_data     | None                                                          | This command is used to load dummy data for testing and development purposes only. User "devdistrictadmin" with password "Coronasafe@123" is populated, invokes :code:`load_data kerala` and :code:`seed_data`. This command will intentionally fail to run in staging or production environment.                                |
|                     |                                                               |                                                                                                                                                                                                                                                                                                                                  |
|                     |                                                               | Example Invocation: :code:`python manage.py load_dummy_data`                                                                                                                                                                                                                                                                     |
+---------------------+---------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| load_data           | state_name                                                    | This command is used to load all the District/Lsg/Ward Level data for a given state, The data that is imported is scraped from various sources, The admin can change this data at any point through the admin panel, If the state name is given as "all" then all available data is imported into care.                          |
|                     |                                                               |                                                                                                                                                                                                                                                                                                                                  |
|                     |                                                               | Example Invocation: :code:`python manage.py load_data kerala`                                                                                                                                                                                                                                                                    |
+---------------------+---------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| seed_data           | None                                                          | This command loads all static data that the application needs to run, this includes inventory seed data for Oxygen, Medical Tests data, etc. All this data can be changed by the superuser from the admin panel.                                                                                                                 |
|                     |                                                               |                                                                                                                                                                                                                                                                                                                                  |
|                     |                                                               | Example Invocation: :code:`python manage.py seed_data`                                                                                                                                                                                                                                                                           |
+---------------------+---------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| load_skill_data     | None                                                          | This command loads default skills                                                                                                                                                                                                                                                                                                |
|                     |                                                               |                                                                                                                                                                                                                                                                                                                                  |
|                     |                                                               | Example Invocation: :code:`python manage.py load_skill_data`                                                                                                                                                                                                                                                                     |
+---------------------+---------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| summarize           | None                                                          | The summary jobs are run by Celery every 10 mins, but we can force Django to summarize the current data and update the summary table with this command                                                                                                                                                                           |
|                     |                                                               |                                                                                                                                                                                                                                                                                                                                  |
|                     |                                                               | Example Invocation: :code:`python manage.py summarize`                                                                                                                                                                                                                                                                           |
+---------------------+---------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
