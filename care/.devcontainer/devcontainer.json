// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/python
{
  "name": "Care",
  "hostRequirements": {
    "cpus": 4
  },
  "image": "mcr.microsoft.com/devcontainers/python:1-3.13-bookworm",
  "features": {
    "ghcr.io/devcontainers/features/docker-in-docker:2": {},
    "ghcr.io/devcontainers/features/node:1": {},
    "ghcr.io/devcontainers/features/git:1": {},
    "ghcr.io/devcontainers/features/github-cli:1": {},
    "ghcr.io/devcontainers-contrib/features/pipenv:2": {},
    "ghcr.io/devcontainers-contrib/features/direnv:1": {},
    "ghcr.io/devcontainers-contrib/features/apt-get-packages:1": {
      "packages": "build-essential,libjpeg-dev,zlib1g-dev,libpq-dev,gettext,wget,curl,gnupg",
      "preserve_apt_list": false
    }
  },
  "postCreateCommand": "echo 'eval \"$(direnv hook bash)\"' >> ~/.bashrc && cp .env.example .env",
  "postStartCommand": "make up",
  "forwardPorts": [4566, 8000, 9000, 4000]
}
