[{"model": "facility.facility", "pk": 1, "fields": {"external_id": "81092ced-8720-44cb-b4c5-3f0ad0540153", "created_date": "2022-09-27T06:59:15.929Z", "modified_date": "2023-12-03T09:26:02.089Z", "deleted": false, "name": "Dummy Facility 40", "is_active": true, "verified": false, "facility_type": 2, "kasp_empanelled": false, "features": "[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\"]", "longitude": null, "latitude": null, "pincode": 670000, "address": "127.0.0.1", "ward": 15162, "local_body": 6, "district": 7, "state": 1, "oxygen_capacity": 0, "type_b_cylinders": 0, "type_c_cylinders": 0, "type_d_cylinders": 0, "expected_oxygen_requirement": 0, "expected_type_b_cylinders": 0, "expected_type_c_cylinders": 0, "expected_type_d_cylinders": 0, "phone_number": "+919999999888", "corona_testing": false, "created_by": 2, "cover_image_url": null, "middleware_address": null}}, {"model": "facility.facility", "pk": 2, "fields": {"external_id": "fa33079d-727d-4295-b0fd-19153b36b2db", "created_date": "2022-09-27T07:15:51.075Z", "modified_date": "2022-09-27T07:15:51.075Z", "deleted": false, "name": "Dummy Shifting Center", "is_active": true, "verified": false, "facility_type": 1300, "kasp_empanelled": false, "features": "[\"1\", \"6\"]", "longitude": null, "latitude": null, "pincode": 670112, "address": "***********", "ward": 15162, "local_body": 6, "district": 7, "state": 1, "oxygen_capacity": 0, "type_b_cylinders": 0, "type_c_cylinders": 0, "type_d_cylinders": 0, "expected_oxygen_requirement": 0, "expected_type_b_cylinders": 0, "expected_type_c_cylinders": 0, "expected_type_d_cylinders": 0, "phone_number": "+919876665987", "corona_testing": false, "created_by": 2, "cover_image_url": null, "middleware_address": null}}, {"model": "facility.facility", "pk": 3, "fields": {"external_id": "4c293ecd-1aae-4ebc-9b5b-b53497dffac9", "created_date": "2023-09-15T06:11:14.166Z", "modified_date": "2023-12-03T09:26:10.570Z", "deleted": false, "name": "Dummy Request Approving Center", "is_active": true, "verified": false, "facility_type": 1500, "kasp_empanelled": false, "features": "[\"1\", \"4\", \"6\"]", "longitude": "78.6757364624373000", "latitude": "21.4009146842158660", "pincode": 670000, "address": "Dummy Facility Address", "ward": 15162, "local_body": 6, "district": 7, "state": 1, "oxygen_capacity": 0, "type_b_cylinders": 0, "type_c_cylinders": 0, "type_d_cylinders": 0, "expected_oxygen_requirement": 0, "expected_type_b_cylinders": 0, "expected_type_c_cylinders": 0, "expected_type_d_cylinders": 0, "phone_number": "+919999999888", "corona_testing": false, "created_by": 2, "cover_image_url": null, "middleware_address": null}}, {"model": "facility.facility", "pk": 4, "fields": {"external_id": "e70e0b82-7a99-48c2-a735-41cdea3b4076", "created_date": "2023-09-15T06:12:14.266Z", "modified_date": "2023-12-03T09:26:14.909Z", "deleted": false, "name": "Dummy Request Fulfilment Center", "is_active": true, "verified": false, "facility_type": 1510, "kasp_empanelled": false, "features": "[\"1\", \"3\", \"5\"]", "longitude": "75.2139014820876600", "latitude": "18.2774285038890340", "pincode": 670000, "address": "Dummy Facility Address 2", "ward": 15162, "local_body": 6, "district": 7, "state": 1, "oxygen_capacity": 0, "type_b_cylinders": 0, "type_c_cylinders": 0, "type_d_cylinders": 0, "expected_oxygen_requirement": 0, "expected_type_b_cylinders": 0, "expected_type_c_cylinders": 0, "expected_type_d_cylinders": 0, "phone_number": "+918899885588", "corona_testing": false, "created_by": 2, "cover_image_url": null, "middleware_address": null}}, {"model": "facility.facility", "pk": 5, "fields": {"external_id": "4b1de304-3871-43d7-9204-20600d4a11b0", "created_date": "2023-12-06T08:25:22.929Z", "modified_date": "2023-12-06T08:25:22.929Z", "deleted": false, "name": "Dummy Facility 2", "is_active": true, "verified": false, "facility_type": 2, "kasp_empanelled": false, "features": "[]", "longitude": null, "latitude": null, "pincode": 682001, "address": "cypress address", "ward": 6276, "local_body": 20, "district": 7, "state": 1, "oxygen_capacity": 0, "type_b_cylinders": 0, "type_c_cylinders": 0, "type_d_cylinders": 0, "expected_oxygen_requirement": 0, "expected_type_b_cylinders": 0, "expected_type_c_cylinders": 0, "expected_type_d_cylinders": 0, "phone_number": "+919898469865", "corona_testing": false, "created_by": 2, "cover_image_url": null, "middleware_address": null}}, {"model": "facility.facility", "pk": 6, "fields": {"external_id": "dea4c08b-507d-44e4-8055-82d53b803303", "created_date": "2023-12-06T08:25:59.449Z", "modified_date": "2023-12-06T08:25:59.449Z", "deleted": false, "name": "Dummy Facility 3", "is_active": true, "verified": false, "facility_type": 2, "kasp_empanelled": false, "features": "[]", "longitude": null, "latitude": null, "pincode": 682001, "address": "cypress address", "ward": 6276, "local_body": 20, "district": 7, "state": 1, "oxygen_capacity": 0, "type_b_cylinders": 0, "type_c_cylinders": 0, "type_d_cylinders": 0, "expected_oxygen_requirement": 0, "expected_type_b_cylinders": 0, "expected_type_c_cylinders": 0, "expected_type_d_cylinders": 0, "phone_number": "+919898469865", "corona_testing": false, "created_by": 2, "cover_image_url": null, "middleware_address": null}}, {"model": "facility.facility", "pk": 7, "fields": {"external_id": "d4c46db1-a814-49bb-b0ef-1f3954fe8cc0", "created_date": "2023-12-06T08:26:06.142Z", "modified_date": "2023-12-06T08:26:06.142Z", "deleted": false, "name": "Dummy Facility 4", "is_active": true, "verified": false, "facility_type": 2, "kasp_empanelled": false, "features": "[]", "longitude": null, "latitude": null, "pincode": 682001, "address": "cypress address", "ward": 6276, "local_body": 20, "district": 7, "state": 1, "oxygen_capacity": 0, "type_b_cylinders": 0, "type_c_cylinders": 0, "type_d_cylinders": 0, "expected_oxygen_requirement": 0, "expected_type_b_cylinders": 0, "expected_type_c_cylinders": 0, "expected_type_d_cylinders": 0, "phone_number": "+919898469865", "corona_testing": false, "created_by": 2, "cover_image_url": null, "middleware_address": null}}, {"model": "facility.facility", "pk": 8, "fields": {"external_id": "392237e0-9cac-4f36-9380-70b60ba9234a", "created_date": "2023-12-06T08:26:12.931Z", "modified_date": "2023-12-06T08:26:12.931Z", "deleted": false, "name": "Dummy Facility 5", "is_active": true, "verified": false, "facility_type": 2, "kasp_empanelled": false, "features": "[]", "longitude": null, "latitude": null, "pincode": 682001, "address": "cypress address", "ward": 6276, "local_body": 20, "district": 7, "state": 1, "oxygen_capacity": 0, "type_b_cylinders": 0, "type_c_cylinders": 0, "type_d_cylinders": 0, "expected_oxygen_requirement": 0, "expected_type_b_cylinders": 0, "expected_type_c_cylinders": 0, "expected_type_d_cylinders": 0, "phone_number": "+919898469865", "corona_testing": false, "created_by": 2, "cover_image_url": null, "middleware_address": null}}, {"model": "facility.facility", "pk": 9, "fields": {"external_id": "daaf1a34-dc1a-4860-8abe-82ff61c31467", "created_date": "2023-12-06T08:26:19.813Z", "modified_date": "2023-12-06T08:26:19.813Z", "deleted": false, "name": "Dummy Facility 6", "is_active": true, "verified": false, "facility_type": 2, "kasp_empanelled": false, "features": "[]", "longitude": null, "latitude": null, "pincode": 682001, "address": "cypress address", "ward": 6276, "local_body": 20, "district": 7, "state": 1, "oxygen_capacity": 0, "type_b_cylinders": 0, "type_c_cylinders": 0, "type_d_cylinders": 0, "expected_oxygen_requirement": 0, "expected_type_b_cylinders": 0, "expected_type_c_cylinders": 0, "expected_type_d_cylinders": 0, "phone_number": "+919898469865", "corona_testing": false, "created_by": 2, "cover_image_url": null, "middleware_address": null}}, {"model": "facility.facility", "pk": 10, "fields": {"external_id": "6ba079b2-c941-4fab-afda-497a1ffc87bb", "created_date": "2023-12-06T08:26:26.717Z", "modified_date": "2023-12-06T08:26:26.717Z", "deleted": false, "name": "Dummy Facility 7", "is_active": true, "verified": false, "facility_type": 2, "kasp_empanelled": false, "features": "[]", "longitude": null, "latitude": null, "pincode": 682001, "address": "cypress address", "ward": 6276, "local_body": 20, "district": 7, "state": 1, "oxygen_capacity": 0, "type_b_cylinders": 0, "type_c_cylinders": 0, "type_d_cylinders": 0, "expected_oxygen_requirement": 0, "expected_type_b_cylinders": 0, "expected_type_c_cylinders": 0, "expected_type_d_cylinders": 0, "phone_number": "+919898469865", "corona_testing": false, "created_by": 2, "cover_image_url": null, "middleware_address": null}}, {"model": "facility.facility", "pk": 11, "fields": {"external_id": "d742ea33-8707-4e94-becd-05ca8821156b", "created_date": "2023-12-06T08:26:33.743Z", "modified_date": "2023-12-06T08:26:33.743Z", "deleted": false, "name": "Dummy Facility 8", "is_active": true, "verified": false, "facility_type": 2, "kasp_empanelled": false, "features": "[]", "longitude": null, "latitude": null, "pincode": 682001, "address": "cypress address", "ward": 6276, "local_body": 20, "district": 7, "state": 1, "oxygen_capacity": 0, "type_b_cylinders": 0, "type_c_cylinders": 0, "type_d_cylinders": 0, "expected_oxygen_requirement": 0, "expected_type_b_cylinders": 0, "expected_type_c_cylinders": 0, "expected_type_d_cylinders": 0, "phone_number": "+919898469865", "corona_testing": false, "created_by": 2, "cover_image_url": null, "middleware_address": null}}, {"model": "facility.facility", "pk": 12, "fields": {"external_id": "23fb96df-790a-41d0-af6f-164f7c77c680", "created_date": "2023-12-06T08:26:40.606Z", "modified_date": "2023-12-06T08:26:40.606Z", "deleted": false, "name": "Dummy Facility 9", "is_active": true, "verified": false, "facility_type": 2, "kasp_empanelled": false, "features": "[]", "longitude": null, "latitude": null, "pincode": 682001, "address": "cypress address", "ward": 6276, "local_body": 20, "district": 7, "state": 1, "oxygen_capacity": 0, "type_b_cylinders": 0, "type_c_cylinders": 0, "type_d_cylinders": 0, "expected_oxygen_requirement": 0, "expected_type_b_cylinders": 0, "expected_type_c_cylinders": 0, "expected_type_d_cylinders": 0, "phone_number": "+919898469865", "corona_testing": false, "created_by": 2, "cover_image_url": null, "middleware_address": null}}, {"model": "facility.facility", "pk": 13, "fields": {"external_id": "f779a7a2-c7a1-499f-99dd-d8c39113b4d0", "created_date": "2023-12-06T08:26:47.590Z", "modified_date": "2023-12-06T08:26:47.590Z", "deleted": false, "name": "Dummy Facility 10", "is_active": true, "verified": false, "facility_type": 2, "kasp_empanelled": false, "features": "[]", "longitude": null, "latitude": null, "pincode": 682001, "address": "cypress address", "ward": 6276, "local_body": 20, "district": 7, "state": 1, "oxygen_capacity": 0, "type_b_cylinders": 0, "type_c_cylinders": 0, "type_d_cylinders": 0, "expected_oxygen_requirement": 0, "expected_type_b_cylinders": 0, "expected_type_c_cylinders": 0, "expected_type_d_cylinders": 0, "phone_number": "+919898469865", "corona_testing": false, "created_by": 2, "cover_image_url": null, "middleware_address": null}}, {"model": "facility.facility", "pk": 14, "fields": {"external_id": "ca7d4389-fff7-46b4-8d9e-2fdecc78512e", "created_date": "2023-12-06T08:26:54.636Z", "modified_date": "2023-12-06T08:26:54.636Z", "deleted": false, "name": "Dummy Facility 11", "is_active": true, "verified": false, "facility_type": 2, "kasp_empanelled": false, "features": "[]", "longitude": null, "latitude": null, "pincode": 682001, "address": "cypress address", "ward": 6276, "local_body": 20, "district": 7, "state": 1, "oxygen_capacity": 0, "type_b_cylinders": 0, "type_c_cylinders": 0, "type_d_cylinders": 0, "expected_oxygen_requirement": 0, "expected_type_b_cylinders": 0, "expected_type_c_cylinders": 0, "expected_type_d_cylinders": 0, "phone_number": "+919898469865", "corona_testing": false, "created_by": 2, "cover_image_url": null, "middleware_address": null}}, {"model": "facility.facility", "pk": 15, "fields": {"external_id": "7635ecfd-5d3d-4b1b-835e-6c4ecb199b0f", "created_date": "2023-12-06T08:27:01.844Z", "modified_date": "2023-12-06T08:27:01.844Z", "deleted": false, "name": "Dummy Facility 12", "is_active": true, "verified": false, "facility_type": 2, "kasp_empanelled": false, "features": "[]", "longitude": null, "latitude": null, "pincode": 682001, "address": "cypress address", "ward": 6276, "local_body": 20, "district": 7, "state": 1, "oxygen_capacity": 0, "type_b_cylinders": 0, "type_c_cylinders": 0, "type_d_cylinders": 0, "expected_oxygen_requirement": 0, "expected_type_b_cylinders": 0, "expected_type_c_cylinders": 0, "expected_type_d_cylinders": 0, "phone_number": "+919898469865", "corona_testing": false, "created_by": 2, "cover_image_url": null, "middleware_address": null}}, {"model": "facility.facility", "pk": 16, "fields": {"external_id": "14899a2e-2f5e-48ab-98fc-bb0829d7d12f", "created_date": "2023-12-06T08:27:08.679Z", "modified_date": "2023-12-06T08:27:08.679Z", "deleted": false, "name": "Dummy Facility 13", "is_active": true, "verified": false, "facility_type": 2, "kasp_empanelled": false, "features": "[]", "longitude": null, "latitude": null, "pincode": 682001, "address": "cypress address", "ward": 6276, "local_body": 20, "district": 7, "state": 1, "oxygen_capacity": 0, "type_b_cylinders": 0, "type_c_cylinders": 0, "type_d_cylinders": 0, "expected_oxygen_requirement": 0, "expected_type_b_cylinders": 0, "expected_type_c_cylinders": 0, "expected_type_d_cylinders": 0, "phone_number": "+919898469865", "corona_testing": false, "created_by": 2, "cover_image_url": null, "middleware_address": null}}, {"model": "facility.facility", "pk": 17, "fields": {"external_id": "a1d0cdc1-fa7d-47cf-9ca6-40c5b53a48ab", "created_date": "2023-12-06T08:27:15.731Z", "modified_date": "2023-12-06T08:27:15.731Z", "deleted": false, "name": "Dummy Facility 14", "is_active": true, "verified": false, "facility_type": 2, "kasp_empanelled": false, "features": "[]", "longitude": null, "latitude": null, "pincode": 682001, "address": "cypress address", "ward": 6276, "local_body": 20, "district": 7, "state": 1, "oxygen_capacity": 0, "type_b_cylinders": 0, "type_c_cylinders": 0, "type_d_cylinders": 0, "expected_oxygen_requirement": 0, "expected_type_b_cylinders": 0, "expected_type_c_cylinders": 0, "expected_type_d_cylinders": 0, "phone_number": "+919898469865", "corona_testing": false, "created_by": 2, "cover_image_url": null, "middleware_address": null}}, {"model": "facility.facility", "pk": 18, "fields": {"external_id": "bcdd01e2-c1ca-434a-9c95-fc45bb3cba1d", "created_date": "2023-12-06T08:27:22.617Z", "modified_date": "2023-12-06T08:27:22.617Z", "deleted": false, "name": "Dummy Facility 15", "is_active": true, "verified": false, "facility_type": 2, "kasp_empanelled": false, "features": "[]", "longitude": null, "latitude": null, "pincode": 682001, "address": "cypress address", "ward": 6276, "local_body": 20, "district": 7, "state": 1, "oxygen_capacity": 0, "type_b_cylinders": 0, "type_c_cylinders": 0, "type_d_cylinders": 0, "expected_oxygen_requirement": 0, "expected_type_b_cylinders": 0, "expected_type_c_cylinders": 0, "expected_type_d_cylinders": 0, "phone_number": "+919898469865", "corona_testing": false, "created_by": 2, "cover_image_url": null, "middleware_address": null}}, {"model": "facility.facility", "pk": 19, "fields": {"external_id": "6b46d930-a205-4199-a1a7-cd80c9413b93", "created_date": "2023-12-06T08:27:29.431Z", "modified_date": "2023-12-06T08:27:29.431Z", "deleted": false, "name": "Dummy Facility 16", "is_active": true, "verified": false, "facility_type": 2, "kasp_empanelled": false, "features": "[]", "longitude": null, "latitude": null, "pincode": 682001, "address": "cypress address", "ward": 6276, "local_body": 20, "district": 7, "state": 1, "oxygen_capacity": 0, "type_b_cylinders": 0, "type_c_cylinders": 0, "type_d_cylinders": 0, "expected_oxygen_requirement": 0, "expected_type_b_cylinders": 0, "expected_type_c_cylinders": 0, "expected_type_d_cylinders": 0, "phone_number": "+919898469865", "corona_testing": false, "created_by": 2, "cover_image_url": null, "middleware_address": null}}, {"model": "facility.facility", "pk": 20, "fields": {"external_id": "2befba93-373e-4ec8-9673-4f22abed84ec", "created_date": "2023-12-06T08:27:36.491Z", "modified_date": "2023-12-06T08:27:36.491Z", "deleted": false, "name": "Dummy Facility 17", "is_active": true, "verified": false, "facility_type": 2, "kasp_empanelled": false, "features": "[]", "longitude": null, "latitude": null, "pincode": 682001, "address": "cypress address", "ward": 6276, "local_body": 20, "district": 7, "state": 1, "oxygen_capacity": 0, "type_b_cylinders": 0, "type_c_cylinders": 0, "type_d_cylinders": 0, "expected_oxygen_requirement": 0, "expected_type_b_cylinders": 0, "expected_type_c_cylinders": 0, "expected_type_d_cylinders": 0, "phone_number": "+919898469865", "corona_testing": false, "created_by": 2, "cover_image_url": null, "middleware_address": null}}, {"model": "facility.hospitaldoctors", "pk": 1, "fields": {"external_id": "d68776c9-1197-411f-b19d-2069364b17a2", "created_date": "2023-09-15T06:12:36.941Z", "modified_date": "2023-09-15T06:12:36.941Z", "deleted": false, "facility": 4, "area": 2, "count": 5}}, {"model": "facility.hospitaldoctors", "pk": 2, "fields": {"external_id": "df1ef651-4e4f-4a0a-9df7-55e5949468ce", "created_date": "2023-09-15T06:12:54.835Z", "modified_date": "2023-09-15T06:12:54.835Z", "deleted": false, "facility": 3, "area": 3, "count": 4}}, {"model": "facility.facilitycapacity", "pk": 1, "fields": {"external_id": "bfb7a4d8-6bf0-46d6-bf20-d5020850ea55", "created_date": "2022-09-27T07:00:19.399Z", "modified_date": "2022-09-27T07:00:19.399Z", "deleted": false, "facility": 1, "room_type": 300, "total_capacity": 1000, "current_capacity": 20}}, {"model": "facility.facilitycapacity", "pk": 2, "fields": {"external_id": "52bd4e97-9064-4697-8fea-37e0a2b8c87c", "created_date": "2022-09-27T07:16:52.525Z", "modified_date": "2022-09-27T07:16:52.525Z", "deleted": false, "facility": 2, "room_type": 300, "total_capacity": 20, "current_capacity": 1}}, {"model": "facility.facilitycapacity", "pk": 3, "fields": {"external_id": "4fed54e3-672b-412c-998f-a7d8f303016c", "created_date": "2023-09-15T06:12:31.548Z", "modified_date": "2023-09-15T06:12:31.548Z", "deleted": false, "facility": 4, "room_type": 300, "total_capacity": 12, "current_capacity": 2}}, {"model": "facility.facilitycapacity", "pk": 4, "fields": {"external_id": "c7d64165-8097-4dec-a4df-616647e70c31", "created_date": "2023-09-15T06:12:50.165Z", "modified_date": "2023-09-15T06:12:50.165Z", "deleted": false, "facility": 3, "room_type": 20, "total_capacity": 31, "current_capacity": 2}}, {"model": "facility.facilityuser", "pk": 1, "fields": {"facility": 1, "user": 2, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 2, "fields": {"facility": 2, "user": 2, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 3, "fields": {"facility": 1, "user": 21, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 4, "fields": {"facility": 1, "user": 22, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 5, "fields": {"facility": 3, "user": 2, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 6, "fields": {"facility": 4, "user": 2, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 7, "fields": {"facility": 5, "user": 2, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 8, "fields": {"facility": 6, "user": 2, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 9, "fields": {"facility": 7, "user": 2, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 10, "fields": {"facility": 8, "user": 2, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 11, "fields": {"facility": 9, "user": 2, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 12, "fields": {"facility": 10, "user": 2, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 13, "fields": {"facility": 11, "user": 2, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 14, "fields": {"facility": 12, "user": 2, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 15, "fields": {"facility": 13, "user": 2, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 16, "fields": {"facility": 14, "user": 2, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 17, "fields": {"facility": 15, "user": 2, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 18, "fields": {"facility": 16, "user": 2, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 19, "fields": {"facility": 17, "user": 2, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 20, "fields": {"facility": 18, "user": 2, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 21, "fields": {"facility": 19, "user": 2, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 22, "fields": {"facility": 20, "user": 2, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 23, "fields": {"facility": 1, "user": 23, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 24, "fields": {"facility": 1, "user": 24, "created_by": 2}}, {"model": "facility.facilityuser", "pk": 25, "fields": {"facility": 1, "user": 25, "created_by": 2}}, {"model": "facility.assetlocation", "pk": 1, "fields": {"external_id": "c3ab727f-dc3f-4a11-bf8d-2472bd79b5f7", "created_date": "2022-09-27T07:02:07.969Z", "modified_date": "2022-09-27T07:02:07.969Z", "deleted": false, "name": "Camera Locations", "description": "", "location_type": 1, "facility": 1, "middleware_address": null}}, {"model": "facility.assetlocation", "pk": 2, "fields": {"external_id": "a5cd0a56-cd5a-425c-9a87-cf79c0a90887", "created_date": "2023-09-15T06:13:24.614Z", "modified_date": "2023-09-15T06:13:24.614Z", "deleted": false, "name": "Dummy Location 1", "description": "", "location_type": 1, "facility": 1, "middleware_address": null}}, {"model": "facility.asset", "pk": 1, "fields": {"external_id": "ae4290a0-e86a-48d7-9c82-60b7d6514707", "created_date": "2022-09-27T07:03:57.401Z", "modified_date": "2022-09-27T07:03:57.401Z", "deleted": false, "name": "Dummy Camera 1", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 2, "fields": {"external_id": "7c06ec3c-838d-447a-bf36-d20239c8442f", "created_date": "2022-09-27T07:04:30.802Z", "modified_date": "2022-09-27T07:04:30.802Z", "deleted": false, "name": "Dummy Camera 2", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 3, "fields": {"external_id": "46740927-f782-49aa-bf6e-52a1208e8d18", "created_date": "2022-09-27T07:04:35.291Z", "modified_date": "2022-09-27T07:04:35.291Z", "deleted": false, "name": "Dummy Camera 3", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 4, "fields": {"external_id": "d6e9e071-e67e-4fbc-a872-4c3b1b9f749f", "created_date": "2022-09-27T07:04:38.335Z", "modified_date": "2022-09-27T07:04:38.335Z", "deleted": false, "name": "Dummy Camera 4", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 5, "fields": {"external_id": "35a2200d-6c1a-4c53-936c-60ebec1f9d42", "created_date": "2022-09-27T07:04:41.179Z", "modified_date": "2022-09-27T07:04:41.179Z", "deleted": false, "name": "Dummy Camera 5", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 6, "fields": {"external_id": "db1c2f95-5ae9-4e64-b647-30250beb79c7", "created_date": "2022-09-27T07:04:43.869Z", "modified_date": "2022-09-27T07:04:43.870Z", "deleted": false, "name": "<PERSON><PERSON> 6", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 7, "fields": {"external_id": "604ec344-a7d1-4523-8a6f-3e78cd3409ef", "created_date": "2022-09-27T07:04:46.610Z", "modified_date": "2022-09-27T07:04:46.610Z", "deleted": false, "name": "<PERSON>mmy Camera 7", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 8, "fields": {"external_id": "271ace96-6bc6-4d32-916b-6b69eb81e4c3", "created_date": "2022-09-27T07:04:49.732Z", "modified_date": "2022-09-27T07:04:49.732Z", "deleted": false, "name": "Dummy Camera 8", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 9, "fields": {"external_id": "fe2f171f-0c69-4305-be60-0db625d0e38e", "created_date": "2022-09-27T07:04:52.832Z", "modified_date": "2022-09-27T07:04:52.832Z", "deleted": false, "name": "Dummy <PERSON> 9", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 10, "fields": {"external_id": "cf77edb2-e771-46db-a73d-80ac2b446987", "created_date": "2022-09-27T07:04:55.942Z", "modified_date": "2022-09-27T07:04:55.942Z", "deleted": false, "name": "Dummy Camera 10", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 11, "fields": {"external_id": "2a19c4a9-84a2-41c8-ac0d-bbbc35e5f18d", "created_date": "2022-09-27T07:04:58.599Z", "modified_date": "2022-09-27T07:04:58.599Z", "deleted": false, "name": "<PERSON>mmy <PERSON> 11", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 12, "fields": {"external_id": "efb41cef-4d39-477f-b437-7ec9e4406971", "created_date": "2022-09-27T07:05:01.182Z", "modified_date": "2022-09-27T07:05:01.182Z", "deleted": false, "name": "<PERSON>mmy <PERSON> 12", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 13, "fields": {"external_id": "875b2bd3-67a8-4968-a589-d62c656cacb9", "created_date": "2022-09-27T07:05:03.955Z", "modified_date": "2022-09-27T07:05:03.955Z", "deleted": false, "name": "<PERSON>mmy <PERSON> 13", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 14, "fields": {"external_id": "b9ff3001-ed15-4538-bb54-78d0b2be445f", "created_date": "2022-09-27T07:05:07.194Z", "modified_date": "2022-09-27T07:05:07.194Z", "deleted": false, "name": "<PERSON>mmy <PERSON> 14", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 15, "fields": {"external_id": "a9678d31-17a9-4474-be01-c095553e97b5", "created_date": "2022-09-27T07:05:10.384Z", "modified_date": "2022-09-27T07:05:10.384Z", "deleted": false, "name": "Dummy <PERSON> 15", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 16, "fields": {"external_id": "b0e4063e-3f05-4f94-aea0-e15e9e474f96", "created_date": "2022-09-27T07:05:13.857Z", "modified_date": "2022-09-27T07:05:13.857Z", "deleted": false, "name": "Dummy Camera 16", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 17, "fields": {"external_id": "634e1421-f1a7-41d1-bde2-13d1ee7eabf4", "created_date": "2022-09-27T07:05:16.707Z", "modified_date": "2022-09-27T07:05:16.707Z", "deleted": false, "name": "<PERSON>mmy <PERSON> 17", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 18, "fields": {"external_id": "17992979-d18a-46c6-a48f-c7f0f40f85c3", "created_date": "2022-09-27T07:05:19.781Z", "modified_date": "2022-09-27T07:05:19.781Z", "deleted": false, "name": "Dummy Camera 18", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 19, "fields": {"external_id": "c6e2c6c3-ba16-4c69-b1f8-822d28317268", "created_date": "2022-09-27T07:13:12.608Z", "modified_date": "2022-09-27T07:13:12.608Z", "deleted": false, "name": "Dummy <PERSON> 19", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 20, "fields": {"external_id": "cb375e24-a98c-45ed-85c4-bdd340625357", "created_date": "2022-09-27T07:13:16.362Z", "modified_date": "2022-09-27T07:13:16.362Z", "deleted": false, "name": "Dummy Camera 20", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 21, "fields": {"external_id": "36a0d62c-9f2d-465f-ad9f-772ec7a342d0", "created_date": "2022-09-27T07:13:19.599Z", "modified_date": "2022-09-27T07:13:19.599Z", "deleted": false, "name": "Dummy <PERSON> 21", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 22, "fields": {"external_id": "e24d8936-e4f3-4f30-870e-8bc560be66f2", "created_date": "2022-09-27T07:13:22.225Z", "modified_date": "2022-09-27T07:13:22.225Z", "deleted": false, "name": "<PERSON>mmy <PERSON> 22", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 23, "fields": {"external_id": "cc0ebf9e-0fe1-45ff-9954-4b92fc072846", "created_date": "2022-09-27T07:13:25.053Z", "modified_date": "2022-09-27T07:13:25.053Z", "deleted": false, "name": "Dummy <PERSON> 23", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 24, "fields": {"external_id": "b10bed38-4a06-473a-85f8-87004703bb2a", "created_date": "2022-09-27T07:13:27.907Z", "modified_date": "2022-09-27T07:13:27.907Z", "deleted": false, "name": "Dummy <PERSON> 24", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 25, "fields": {"external_id": "51ce3355-1773-47c0-a929-54bb57293940", "created_date": "2022-09-27T07:13:30.944Z", "modified_date": "2022-09-27T07:13:30.944Z", "deleted": false, "name": "Dummy Camera 25", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 26, "fields": {"external_id": "567ddf56-f86a-488e-bdff-9c69daacb654", "created_date": "2022-09-27T07:13:33.581Z", "modified_date": "2022-09-27T07:13:33.581Z", "deleted": false, "name": "<PERSON>mmy <PERSON> 26", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 27, "fields": {"external_id": "234a8559-167b-4e61-9971-15dbc59b9319", "created_date": "2022-09-27T07:13:37.020Z", "modified_date": "2022-09-27T07:13:37.020Z", "deleted": false, "name": "<PERSON>mmy <PERSON> 27", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 28, "fields": {"external_id": "3f203a24-84ae-44c0-897a-1b3ddd42e072", "created_date": "2022-09-27T07:13:39.951Z", "modified_date": "2022-09-27T07:13:39.951Z", "deleted": false, "name": "<PERSON><PERSON> 28", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 29, "fields": {"external_id": "fc4c25cd-de00-494a-ad58-20e581520198", "created_date": "2022-09-27T07:13:43.511Z", "modified_date": "2022-09-27T07:13:43.511Z", "deleted": false, "name": "<PERSON>mmy <PERSON> 29", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.asset", "pk": 30, "fields": {"external_id": "cb6f3bf8-cfea-498a-a4f1-effae723d6e0", "created_date": "2022-09-27T07:13:46.850Z", "modified_date": "2022-09-27T07:13:46.850Z", "deleted": false, "name": "Dummy <PERSON> 30", "description": "This is a dummy camera", "asset_type": 50, "asset_class": "ONVIF", "status": 50, "current_location": 1, "is_working": true, "not_working_reason": "", "serial_number": "", "warranty_details": "", "meta": {}, "vendor_name": "Vendors Inc.", "support_name": "", "support_phone": "+914578889765", "support_email": "", "qr_code_id": null, "manufacturer": null, "warranty_amc_end_of_validity": null, "last_service": null}}, {"model": "facility.patientconsultation", "pk": 1, "fields": {"external_id": "b5217729-3008-4a44-b347-72ba738d5f45", "created_date": "2022-09-27T07:20:40.117Z", "modified_date": "2023-12-06T08:35:19.722Z", "deleted": false, "patient": 1, "patient_no": "***********", "facility": 1, "deprecated_covid_category": null, "category": "Moderate", "examination_details": "", "history_of_present_illness": "", "treatment_plan": "", "consultation_notes": "Transfer", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": 2, "referred_to_external": null, "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2023-12-01T08:35:00Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 0.0, "weight": 0.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 2, "fields": {"external_id": "fd33e6f9-0f69-444e-9630-aa5860f57205", "created_date": "2023-12-06T08:33:23.058Z", "modified_date": "2023-12-06T08:33:23.066Z", "deleted": false, "patient": 2, "patient_no": "IP007", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2023-12-06T08:33:03.700Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 3, "fields": {"external_id": "b90f4cfc-0e64-446b-94d8-0ffc967b0f48", "created_date": "2023-12-06T08:39:48.817Z", "modified_date": "2023-12-06T08:39:48.823Z", "deleted": false, "patient": 3, "patient_no": "IP008", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2023-12-06T08:39:29.394Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 4, "fields": {"external_id": "40a630be-fdfc-4e78-889d-55f36e11443e", "created_date": "2023-12-06T08:42:30.113Z", "modified_date": "2023-12-06T08:42:30.134Z", "deleted": false, "patient": 4, "patient_no": "IP009", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2023-12-06T08:42:10.532Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 5, "fields": {"external_id": "1d83bd7a-971d-4333-a385-a983883bca5a", "created_date": "2023-12-06T08:42:52.646Z", "modified_date": "2023-12-06T08:42:52.652Z", "deleted": false, "patient": 5, "patient_no": "IP017", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2023-12-06T08:42:33.614Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 6, "fields": {"external_id": "57ea2f11-2a5a-4899-9085-8dcbf2c851df", "created_date": "2023-12-06T08:43:14.966Z", "modified_date": "2023-12-06T08:43:14.972Z", "deleted": false, "patient": 6, "patient_no": "IP087", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2023-12-06T08:42:56.180Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 7, "fields": {"external_id": "a54b752b-b2f3-48ea-8024-d18c3611541a", "created_date": "2023-12-06T08:43:38.021Z", "modified_date": "2023-12-06T08:43:38.025Z", "deleted": false, "patient": 7, "patient_no": "IP00527", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2023-12-06T08:43:18.480Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 8, "fields": {"external_id": "15ca37ce-ff08-46ca-9c89-8f54a3997e38", "created_date": "2023-12-06T08:44:01.903Z", "modified_date": "2023-12-06T08:44:01.909Z", "deleted": false, "patient": 8, "patient_no": "IP0KI07", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2023-12-06T08:43:41.540Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 9, "fields": {"external_id": "3fa53a10-7826-44d9-903d-2e9a4b4cb7aa", "created_date": "2023-12-06T08:44:25.102Z", "modified_date": "2023-12-06T08:44:25.107Z", "deleted": false, "patient": 9, "patient_no": "IP00767", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2023-12-06T08:44:05.398Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 10, "fields": {"external_id": "12ce58d7-f620-4fa0-948b-73b4c3768664", "created_date": "2023-12-06T08:44:47.678Z", "modified_date": "2023-12-06T08:44:47.683Z", "deleted": false, "patient": 10, "patient_no": "IP001237", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2023-12-06T08:44:28.550Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 11, "fields": {"external_id": "cef1f33e-a885-4ccc-98c6-c6874dad0211", "created_date": "2023-12-06T08:45:10.163Z", "modified_date": "2023-12-06T08:45:10.167Z", "deleted": false, "patient": 11, "patient_no": "IP007963", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2023-12-06T08:44:51.239Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 12, "fields": {"external_id": "1809ded5-b049-43e5-ab95-44a6f382bfd9", "created_date": "2023-12-06T08:45:34.520Z", "modified_date": "2023-12-06T08:45:34.527Z", "deleted": false, "patient": 12, "patient_no": "IP0001257", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2023-12-06T08:45:13.721Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 13, "fields": {"external_id": "6fe3d7c5-75c3-4380-ae9a-ce2a6f6774b9", "created_date": "2023-12-06T08:45:57.141Z", "modified_date": "2023-12-06T08:45:57.145Z", "deleted": false, "patient": 13, "patient_no": "IP075389007", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2023-12-06T08:45:37.972Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 14, "fields": {"external_id": "ed13c3ec-fb5b-4c91-ab6c-25ca6eef6c57", "created_date": "2023-12-06T08:46:19.948Z", "modified_date": "2023-12-06T08:46:19.952Z", "deleted": false, "patient": 14, "patient_no": "IP099907", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2023-12-06T08:46:00.645Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 15, "fields": {"external_id": "c3c2391c-7391-4b38-bff9-9527eb6065a7", "created_date": "2023-12-06T08:46:42.592Z", "modified_date": "2023-12-06T08:46:42.597Z", "deleted": false, "patient": 15, "patient_no": "IP00700", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2023-12-06T08:46:23.492Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 16, "fields": {"external_id": "edec5285-0faa-4cf3-a638-3c30eccf9d49", "created_date": "2023-12-06T08:47:07.442Z", "modified_date": "2023-12-06T08:47:07.447Z", "deleted": false, "patient": 16, "patient_no": "IP00744", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2023-12-06T08:46:46.028Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 17, "fields": {"external_id": "58eb7dae-b0c8-445f-8d2c-5ff0017679a9", "created_date": "2023-12-06T08:47:30.858Z", "modified_date": "2023-12-06T08:47:30.863Z", "deleted": false, "patient": 17, "patient_no": "IP00117", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2023-12-06T08:47:11.141Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 18, "fields": {"external_id": "40faecc6-6199-48cd-bc2a-dd9e73b920f9", "created_date": "2023-12-06T08:47:53.746Z", "modified_date": "2023-12-06T08:47:53.751Z", "deleted": false, "patient": 18, "patient_no": "IP02507", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2023-12-06T08:47:34.395Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 19, "fields": {"external_id": "40faecd6-6199-48cd-bc2a-dd9e73b920f9", "created_date": "2023-12-07T08:47:53.746Z", "modified_date": "2023-12-07T08:47:53.751Z", "deleted": false, "patient": 18, "patient_no": "IP02578", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2023-12-07T08:47:53.746Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 20, "fields": {"external_id": "41faecc6-6199-48cd-bc2a-dd9e73b920f9", "created_date": "2023-12-15T08:47:53.746Z", "modified_date": "2023-12-15T08:47:53.751Z", "deleted": false, "patient": 18, "patient_no": "IP1009", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2023-12-15T08:47:53.746Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 21, "fields": {"external_id": "40fa5cc6-6199-48cd-bc2a-dd9e73b920f9", "created_date": "2024-01-30T08:47:53.746Z", "modified_date": "2024-01-30T08:47:53.746Z", "deleted": false, "patient": 18, "patient_no": "IP0010", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2024-01-30T08:47:53.746Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 22, "fields": {"external_id": "40faecc6-6199-48cd-bc2a-6d9e73b920f9", "created_date": "2024-02-28T08:47:53.746Z", "modified_date": "2024-02-28T08:47:53.746Z", "deleted": false, "patient": 18, "patient_no": "IP011", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2024-02-28T08:47:53.746Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 23, "fields": {"external_id": "40faecc6-61a9-48cd-bc2a-dd9e73b920f9", "created_date": "2024-04-01T08:47:53.746Z", "modified_date": "2024-04-01T08:47:53.746Z", "deleted": false, "patient": 18, "patient_no": "IP012", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2024-04-01T08:47:53.746Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 24, "fields": {"external_id": "40faecb6-6199-48cd-bc2a-dd9e73b920f9", "created_date": "2022-05-06T08:47:53.746Z", "modified_date": "2022-05-06T08:47:53.751Z", "deleted": false, "patient": 18, "patient_no": "IP013", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2022-05-06T08:47:53.746Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 25, "fields": {"external_id": "400aecc6-6199-48cd-bc2a-dd9e73b920f9", "created_date": "2022-02-06T08:47:53.746Z", "modified_date": "2022-02-06T08:47:53.746Z", "deleted": false, "patient": 18, "patient_no": "IP014", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2022-02-06T08:47:53.746Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.patientconsultation", "pk": 26, "fields": {"external_id": "40faecc6-6599-48cd-bc2a-dd9e73b920f9", "created_date": "2023-08-06T08:47:53.746Z", "modified_date": "2023-08-06T08:47:53.751Z", "deleted": false, "patient": 18, "patient_no": "IP015", "facility": 1, "deprecated_covid_category": null, "category": "Stable", "examination_details": "Examination details and Clinical conditions", "history_of_present_illness": "history", "treatment_plan": "", "consultation_notes": "generalnote", "course_in_facility": null, "investigation": [], "procedure": [], "suggestion": "A", "route_to_facility": 10, "review_interval": -1, "referred_to": null, "referred_to_external": "", "transferred_from_location": null, "referred_from_facility": null, "referred_from_facility_external": "", "referred_by_external": "", "previous_consultation": null, "is_readmission": false, "admitted": true, "encounter_date": "2023-12-06T08:47:34.395Z", "icu_admission_date": null, "discharge_date": null, "discharge_reason": null, "new_discharge_reason": null, "discharge_notes": "", "death_datetime": null, "death_confirmed_doctor": "", "bed_number": null, "is_kasp": false, "kasp_enabled_date": null, "is_telemedicine": false, "last_updated_by_telemedicine": false, "assigned_to": null, "medico_legal_case": false, "deprecated_verified_by": "", "treating_physician": 21, "created_by": 2, "last_edited_by": 2, "last_daily_round": null, "current_bed": null, "height": 70.0, "weight": 170.0, "operation": null, "special_instruction": "", "intubation_history": [], "has_consents": "[]"}}, {"model": "facility.bed", "pk": 1, "fields": {"external_id": "260de825-7ef2-4155-8fd2-ae4d66980734", "created_date": "2023-09-15T06:13:43.199Z", "modified_date": "2023-09-15T06:13:43.199Z", "deleted": false, "name": "Dummy Bed 1", "description": "", "bed_type": 2, "facility": 1, "meta": {}, "location": 2}}, {"model": "facility.bed", "pk": 2, "fields": {"external_id": "8ab99d71-7263-4c60-b6d4-b22e7f8dfecf", "created_date": "2023-09-15T06:13:43.199Z", "modified_date": "2023-09-15T06:13:43.199Z", "deleted": false, "name": "Dummy Bed 2", "description": "", "bed_type": 2, "facility": 1, "meta": {}, "location": 2}}, {"model": "facility.bed", "pk": 3, "fields": {"external_id": "e7a9c643-4841-47f3-9729-4ccdadb9783a", "created_date": "2023-09-15T06:13:43.200Z", "modified_date": "2023-09-15T06:13:43.200Z", "deleted": false, "name": "Dummy Bed 3", "description": "", "bed_type": 2, "facility": 1, "meta": {}, "location": 2}}, {"model": "facility.bed", "pk": 5, "fields": {"external_id": "fe749328-1a6a-43ae-b4c2-fb718b8ca84b", "created_date": "2023-09-15T06:14:13.862Z", "modified_date": "2023-09-15T06:14:13.862Z", "deleted": false, "name": "<PERSON>mmy <PERSON> 5", "description": "", "bed_type": 1, "facility": 1, "meta": {}, "location": 1}}, {"model": "facility.bed", "pk": 7, "fields": {"external_id": "ddd0ce36-c4ff-409c-96d3-ea943ac876e4", "created_date": "2023-09-15T06:14:45.458Z", "modified_date": "2023-09-15T06:14:45.458Z", "deleted": false, "name": "<PERSON><PERSON> 6", "description": "", "bed_type": 6, "facility": 1, "meta": {}, "location": 1}}, {"model": "facility.bed", "pk": 8, "fields": {"external_id": "90a90743-0a95-42c1-bdf2-b7fbf9b9edd1", "created_date": "2023-09-15T06:14:56.105Z", "modified_date": "2023-09-15T06:14:56.105Z", "deleted": false, "name": "Dummy Bed 4", "description": "", "bed_type": 2, "facility": 1, "meta": {}, "location": 1}}, {"model": "facility.bed", "pk": 9, "fields": {"external_id": "a9ea05d2-4f58-4fc2-bc24-2979a3502dc4", "created_date": "2024-10-14T07:56:52.641Z", "modified_date": "2024-10-14T07:56:52.641Z", "deleted": false, "name": "<PERSON>mmy <PERSON> 7", "description": "", "bed_type": 2, "facility": 1, "meta": {}, "location": 2}}, {"model": "facility.bed", "pk": 10, "fields": {"external_id": "aff5a088-c278-4075-9a4c-64453f50f216", "created_date": "2024-10-14T07:56:59.896Z", "modified_date": "2024-10-14T07:56:59.896Z", "deleted": false, "name": "Dummy Bed 8", "description": "", "bed_type": 6, "facility": 1, "meta": {}, "location": 2}}, {"model": "facility.bed", "pk": 11, "fields": {"external_id": "882ce4de-4e2c-4afa-b3e6-29fceec30730", "created_date": "2024-10-14T07:57:06.701Z", "modified_date": "2024-10-14T07:57:06.701Z", "deleted": false, "name": "Dummy <PERSON> 9", "description": "", "bed_type": 2, "facility": 1, "meta": {}, "location": 2}}, {"model": "facility.facilityinventoryitemtag", "pk": 1, "fields": {"name": "Safety"}}, {"model": "facility.facilityinventoryitemtag", "pk": 2, "fields": {"name": "Medical"}}, {"model": "facility.facilityinventoryitemtag", "pk": 3, "fields": {"name": "Food"}}, {"model": "facility.facilityinventoryunit", "pk": 1, "fields": {"name": "Items"}}, {"model": "facility.facilityinventoryunit", "pk": 2, "fields": {"name": "<PERSON><PERSON>"}}, {"model": "facility.facilityinventoryunit", "pk": 3, "fields": {"name": "<PERSON><PERSON>"}}, {"model": "facility.facilityinventoryunit", "pk": 4, "fields": {"name": "Cylinders"}}, {"model": "facility.facilityinventoryunit", "pk": 5, "fields": {"name": "kg"}}, {"model": "facility.facilityinventoryunit", "pk": 6, "fields": {"name": "gram"}}, {"model": "facility.facilityinventoryunit", "pk": 7, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "facility.facilityinventoryunitconverter", "pk": 1, "fields": {"from_unit": 5, "to_unit": 6, "multiplier": 1000.0}}, {"model": "facility.facilityinventoryunitconverter", "pk": 2, "fields": {"from_unit": 2, "to_unit": 1, "multiplier": 12.0}}, {"model": "facility.facilityinventoryitem", "pk": 1, "fields": {"name": "PPE", "default_unit": 1, "description": "", "min_quantity": 150.0, "allowed_units": [1, 2], "tags": [1, 2]}}, {"model": "facility.facilityinventoryitem", "pk": 2, "fields": {"name": "IV Fluid 500 ml", "default_unit": 1, "description": "", "min_quantity": 2.0, "allowed_units": [1, 2], "tags": [2]}}, {"model": "facility.facilityinventoryitem", "pk": 3, "fields": {"name": "Liquid Oxygen", "default_unit": 7, "description": "", "min_quantity": 10.0, "allowed_units": [7], "tags": [2]}}, {"model": "facility.facilityinventoryitem", "pk": 4, "fields": {"name": "Jumbo D Type Oxygen Cylinder", "default_unit": 4, "description": "", "min_quantity": 100.0, "allowed_units": [4], "tags": []}}, {"model": "facility.facilityinventoryitem", "pk": 5, "fields": {"name": "B Type Oxygen Cylinder", "default_unit": 4, "description": "", "min_quantity": 100.0, "allowed_units": [4], "tags": []}}, {"model": "facility.facilityinventoryitem", "pk": 6, "fields": {"name": "C Type Oxygen Cylinder", "default_unit": 4, "description": "", "min_quantity": 100.0, "allowed_units": [4], "tags": []}}, {"model": "facility.facilityinventoryitem", "pk": 7, "fields": {"name": "Gaseous Oxygen", "default_unit": 7, "description": "", "min_quantity": 10.0, "allowed_units": [7], "tags": [2]}}, {"model": "facility.patientregistration", "pk": 1, "fields": {"external_id": "7c1d2896-8ebf-45c7-b507-98fcedd48ef3", "created_date": "2022-09-27T07:19:20.379Z", "modified_date": "2023-12-06T08:36:48.093Z", "deleted": false, "source": 10, "facility": 1, "nearest_facility": null, "meta_info": null, "name": "Dummy <PERSON> One", "gender": 1, "phone_number": "+************", "emergency_phone_number": "+************", "address": "***********", "permanent_address": "***********", "pincode": 600115, "date_of_birth": "2005-10-16", "year_of_birth": 2005, "death_datetime": null, "nationality": "India", "passport_no": "", "ration_card_category": null, "is_medical_worker": false, "blood_group": "O+", "contact_with_confirmed_carrier": false, "contact_with_suspected_carrier": false, "estimated_contact_date": null, "past_travel": false, "countries_travelled_old": null, "countries_travelled": null, "date_of_return": null, "allergies": "", "present_health": "", "ongoing_medication": "", "has_SARI": false, "is_antenatal": false, "last_menstruation_start_date": null, "date_of_delivery": null, "ward_old": "", "ward": 5896, "local_body": 95, "district": 5, "state": 1, "is_migrant_worker": false, "disease_status": 3, "number_of_aged_dependents": 0, "number_of_chronic_diseased_dependents": 0, "last_edited": 2, "action": 10, "review_time": null, "created_by": 2, "is_active": true, "date_of_receipt_of_information": "2022-09-27T07:19:20.374Z", "test_id": "", "date_of_test": null, "srf_id": "", "test_type": 30, "allow_transfer": true, "last_consultation": 1, "will_donate_blood": null, "fit_for_blood_donation": null, "village": "", "designation_of_health_care_worker": "", "instituion_of_health_care_worker": "", "transit_details": null, "frontline_worker": null, "date_of_result": null, "number_of_primary_contacts": null, "number_of_secondary_contacts": null, "is_vaccinated": false, "number_of_doses": 0, "vaccine_name": null, "covin_id": null, "last_vaccinated_date": null, "cluster_name": null, "is_declared_positive": false, "date_declared_positive": null, "assigned_to": null}}, {"model": "facility.patientregistration", "pk": 2, "fields": {"external_id": "018f0db5-aa36-4f61-bf68-213f01ab77b1", "created_date": "2023-12-06T08:33:11.523Z", "modified_date": "2023-12-06T08:33:23.094Z", "deleted": false, "source": 10, "facility": 1, "nearest_facility": null, "meta_info": null, "name": "Dummy <PERSON> Two", "gender": 1, "phone_number": "+************", "emergency_phone_number": "+************", "address": "Test Patient Address", "permanent_address": "Test Patient Address", "pincode": 682001, "date_of_birth": "2001-01-01", "year_of_birth": 2001, "death_datetime": null, "nationality": "India", "passport_no": "", "ration_card_category": null, "is_medical_worker": false, "blood_group": "O+", "contact_with_confirmed_carrier": false, "contact_with_suspected_carrier": false, "estimated_contact_date": null, "past_travel": false, "countries_travelled_old": null, "countries_travelled": null, "date_of_return": null, "allergies": "", "present_health": "", "ongoing_medication": "", "has_SARI": false, "is_antenatal": false, "last_menstruation_start_date": null, "date_of_delivery": null, "ward_old": "", "ward": 15162, "local_body": 6, "district": 7, "state": 1, "is_migrant_worker": false, "disease_status": 3, "number_of_aged_dependents": 0, "number_of_chronic_diseased_dependents": 0, "last_edited": 2, "action": 10, "review_time": null, "created_by": 2, "is_active": true, "date_of_receipt_of_information": "2023-12-06T08:33:11.521Z", "test_id": "", "date_of_test": null, "srf_id": "", "test_type": 10, "allow_transfer": true, "last_consultation": 2, "will_donate_blood": null, "fit_for_blood_donation": null, "village": "", "designation_of_health_care_worker": "", "instituion_of_health_care_worker": "", "transit_details": null, "frontline_worker": null, "date_of_result": null, "number_of_primary_contacts": null, "number_of_secondary_contacts": null, "is_vaccinated": false, "number_of_doses": 0, "vaccine_name": null, "covin_id": null, "last_vaccinated_date": null, "cluster_name": null, "is_declared_positive": false, "date_declared_positive": null, "assigned_to": null}}, {"model": "facility.patientregistration", "pk": 3, "fields": {"external_id": "dc8595ed-dc7a-4a58-a730-459506a6de7b", "created_date": "2023-12-06T08:39:37.467Z", "modified_date": "2023-12-06T08:39:48.866Z", "deleted": false, "source": 10, "facility": 1, "nearest_facility": null, "meta_info": null, "name": "Dummy <PERSON>", "gender": 1, "phone_number": "+************", "emergency_phone_number": "+************", "address": "Test Patient Address", "permanent_address": "Test Patient Address", "pincode": 682001, "date_of_birth": "2001-01-01", "year_of_birth": 2001, "death_datetime": null, "nationality": "India", "passport_no": "", "ration_card_category": null, "is_medical_worker": false, "blood_group": "O+", "contact_with_confirmed_carrier": false, "contact_with_suspected_carrier": false, "estimated_contact_date": null, "past_travel": false, "countries_travelled_old": null, "countries_travelled": null, "date_of_return": null, "allergies": "", "present_health": "", "ongoing_medication": "", "has_SARI": false, "is_antenatal": false, "last_menstruation_start_date": null, "date_of_delivery": null, "ward_old": "", "ward": 15162, "local_body": 6, "district": 7, "state": 1, "is_migrant_worker": false, "disease_status": 3, "number_of_aged_dependents": 0, "number_of_chronic_diseased_dependents": 0, "last_edited": 2, "action": 10, "review_time": null, "created_by": 2, "is_active": true, "date_of_receipt_of_information": "2023-12-06T08:39:37.465Z", "test_id": "", "date_of_test": null, "srf_id": "", "test_type": 10, "allow_transfer": true, "last_consultation": 3, "will_donate_blood": null, "fit_for_blood_donation": null, "village": "", "designation_of_health_care_worker": "", "instituion_of_health_care_worker": "", "transit_details": null, "frontline_worker": null, "date_of_result": null, "number_of_primary_contacts": null, "number_of_secondary_contacts": null, "is_vaccinated": false, "number_of_doses": 0, "vaccine_name": null, "covin_id": null, "last_vaccinated_date": null, "cluster_name": null, "is_declared_positive": false, "date_declared_positive": null, "assigned_to": null}}, {"model": "facility.patientregistration", "pk": 4, "fields": {"external_id": "fceae5ef-c56a-4428-bbfd-b175c7ff8bcb", "created_date": "2023-12-06T08:42:18.800Z", "modified_date": "2023-12-06T08:42:30.156Z", "deleted": false, "source": 10, "facility": 1, "nearest_facility": null, "meta_info": null, "name": "Dummy <PERSON>", "gender": 1, "phone_number": "+************", "emergency_phone_number": "+************", "address": "Test Patient Address", "permanent_address": "Test Patient Address", "pincode": 682001, "date_of_birth": "2001-01-01", "year_of_birth": 2001, "death_datetime": null, "nationality": "India", "passport_no": "", "ration_card_category": null, "is_medical_worker": false, "blood_group": "O+", "contact_with_confirmed_carrier": false, "contact_with_suspected_carrier": false, "estimated_contact_date": null, "past_travel": false, "countries_travelled_old": null, "countries_travelled": null, "date_of_return": null, "allergies": "", "present_health": "", "ongoing_medication": "", "has_SARI": false, "is_antenatal": false, "last_menstruation_start_date": null, "date_of_delivery": null, "ward_old": "", "ward": 15162, "local_body": 6, "district": 7, "state": 1, "is_migrant_worker": false, "disease_status": 3, "number_of_aged_dependents": 0, "number_of_chronic_diseased_dependents": 0, "last_edited": 2, "action": 10, "review_time": null, "created_by": 2, "is_active": true, "date_of_receipt_of_information": "2023-12-06T08:42:18.798Z", "test_id": "", "date_of_test": null, "srf_id": "", "test_type": 10, "allow_transfer": true, "last_consultation": 4, "will_donate_blood": null, "fit_for_blood_donation": null, "village": "", "designation_of_health_care_worker": "", "instituion_of_health_care_worker": "", "transit_details": null, "frontline_worker": null, "date_of_result": null, "number_of_primary_contacts": null, "number_of_secondary_contacts": null, "is_vaccinated": false, "number_of_doses": 0, "vaccine_name": null, "covin_id": null, "last_vaccinated_date": null, "cluster_name": null, "is_declared_positive": false, "date_declared_positive": null, "assigned_to": null}}, {"model": "facility.patientregistration", "pk": 5, "fields": {"external_id": "bba42a24-b755-4d78-bf7c-70bfd3047857", "created_date": "2023-12-06T08:42:41.321Z", "modified_date": "2023-12-06T08:42:52.672Z", "deleted": false, "source": 10, "facility": 1, "nearest_facility": null, "meta_info": null, "name": "Dummy <PERSON>", "gender": 1, "phone_number": "+************", "emergency_phone_number": "+************", "address": "Test Patient Address", "permanent_address": "Test Patient Address", "pincode": 682001, "date_of_birth": "2001-01-01", "year_of_birth": 2001, "death_datetime": null, "nationality": "India", "passport_no": "", "ration_card_category": null, "is_medical_worker": false, "blood_group": "O+", "contact_with_confirmed_carrier": false, "contact_with_suspected_carrier": false, "estimated_contact_date": null, "past_travel": false, "countries_travelled_old": null, "countries_travelled": null, "date_of_return": null, "allergies": "", "present_health": "", "ongoing_medication": "", "has_SARI": false, "is_antenatal": false, "last_menstruation_start_date": null, "date_of_delivery": null, "ward_old": "", "ward": 15162, "local_body": 6, "district": 7, "state": 1, "is_migrant_worker": false, "disease_status": 3, "number_of_aged_dependents": 0, "number_of_chronic_diseased_dependents": 0, "last_edited": 2, "action": 10, "review_time": null, "created_by": 2, "is_active": true, "date_of_receipt_of_information": "2023-12-06T08:42:41.320Z", "test_id": "", "date_of_test": null, "srf_id": "", "test_type": 10, "allow_transfer": true, "last_consultation": 5, "will_donate_blood": null, "fit_for_blood_donation": null, "village": "", "designation_of_health_care_worker": "", "instituion_of_health_care_worker": "", "transit_details": null, "frontline_worker": null, "date_of_result": null, "number_of_primary_contacts": null, "number_of_secondary_contacts": null, "is_vaccinated": false, "number_of_doses": 0, "vaccine_name": null, "covin_id": null, "last_vaccinated_date": null, "cluster_name": null, "is_declared_positive": false, "date_declared_positive": null, "assigned_to": null}}, {"model": "facility.patientregistration", "pk": 6, "fields": {"external_id": "779fee10-d266-4e86-9172-822aee40172a", "created_date": "2023-12-06T08:43:03.778Z", "modified_date": "2023-12-06T08:43:14.993Z", "deleted": false, "source": 10, "facility": 1, "nearest_facility": null, "meta_info": null, "name": "Dummy <PERSON>", "gender": 1, "phone_number": "+************", "emergency_phone_number": "+************", "address": "Test Patient Address", "permanent_address": "Test Patient Address", "pincode": 682001, "date_of_birth": "2001-01-01", "year_of_birth": 2001, "death_datetime": null, "nationality": "India", "passport_no": "", "ration_card_category": null, "is_medical_worker": false, "blood_group": "O+", "contact_with_confirmed_carrier": false, "contact_with_suspected_carrier": false, "estimated_contact_date": null, "past_travel": false, "countries_travelled_old": null, "countries_travelled": null, "date_of_return": null, "allergies": "", "present_health": "", "ongoing_medication": "", "has_SARI": false, "is_antenatal": false, "last_menstruation_start_date": null, "date_of_delivery": null, "ward_old": "", "ward": 15162, "local_body": 6, "district": 7, "state": 1, "is_migrant_worker": false, "disease_status": 3, "number_of_aged_dependents": 0, "number_of_chronic_diseased_dependents": 0, "last_edited": 2, "action": 10, "review_time": null, "created_by": 2, "is_active": true, "date_of_receipt_of_information": "2023-12-06T08:43:03.777Z", "test_id": "", "date_of_test": null, "srf_id": "", "test_type": 10, "allow_transfer": true, "last_consultation": 6, "will_donate_blood": null, "fit_for_blood_donation": null, "village": "", "designation_of_health_care_worker": "", "instituion_of_health_care_worker": "", "transit_details": null, "frontline_worker": null, "date_of_result": null, "number_of_primary_contacts": null, "number_of_secondary_contacts": null, "is_vaccinated": false, "number_of_doses": 0, "vaccine_name": null, "covin_id": null, "last_vaccinated_date": null, "cluster_name": null, "is_declared_positive": false, "date_declared_positive": null, "assigned_to": null}}, {"model": "facility.patientregistration", "pk": 7, "fields": {"external_id": "366dcf68-6ea7-481c-84e1-a11f3eb055d2", "created_date": "2023-12-06T08:43:26.604Z", "modified_date": "2023-12-06T08:43:38.047Z", "deleted": false, "source": 10, "facility": 1, "nearest_facility": null, "meta_info": null, "name": "Dummy <PERSON>", "gender": 1, "phone_number": "+************", "emergency_phone_number": "+************", "address": "Test Patient Address", "permanent_address": "Test Patient Address", "pincode": 682001, "date_of_birth": "2001-01-01", "year_of_birth": 2001, "death_datetime": null, "nationality": "India", "passport_no": "", "ration_card_category": null, "is_medical_worker": false, "blood_group": "O+", "contact_with_confirmed_carrier": false, "contact_with_suspected_carrier": false, "estimated_contact_date": null, "past_travel": false, "countries_travelled_old": null, "countries_travelled": null, "date_of_return": null, "allergies": "", "present_health": "", "ongoing_medication": "", "has_SARI": false, "is_antenatal": false, "last_menstruation_start_date": null, "date_of_delivery": null, "ward_old": "", "ward": 15162, "local_body": 6, "district": 7, "state": 1, "is_migrant_worker": false, "disease_status": 3, "number_of_aged_dependents": 0, "number_of_chronic_diseased_dependents": 0, "last_edited": 2, "action": 10, "review_time": null, "created_by": 2, "is_active": true, "date_of_receipt_of_information": "2023-12-06T08:43:26.604Z", "test_id": "", "date_of_test": null, "srf_id": "", "test_type": 10, "allow_transfer": true, "last_consultation": 7, "will_donate_blood": null, "fit_for_blood_donation": null, "village": "", "designation_of_health_care_worker": "", "instituion_of_health_care_worker": "", "transit_details": null, "frontline_worker": null, "date_of_result": null, "number_of_primary_contacts": null, "number_of_secondary_contacts": null, "is_vaccinated": false, "number_of_doses": 0, "vaccine_name": null, "covin_id": null, "last_vaccinated_date": null, "cluster_name": null, "is_declared_positive": false, "date_declared_positive": null, "assigned_to": null}}, {"model": "facility.patientregistration", "pk": 8, "fields": {"external_id": "de9156b5-14f1-4d62-b594-061aa09484ce", "created_date": "2023-12-06T08:43:50.497Z", "modified_date": "2023-12-06T08:44:01.931Z", "deleted": false, "source": 10, "facility": 1, "nearest_facility": null, "meta_info": null, "name": "Dummy <PERSON>", "gender": 1, "phone_number": "+************", "emergency_phone_number": "+************", "address": "Test Patient Address", "permanent_address": "Test Patient Address", "pincode": 682001, "date_of_birth": "2001-01-01", "year_of_birth": 2001, "death_datetime": null, "nationality": "India", "passport_no": "", "ration_card_category": null, "is_medical_worker": false, "blood_group": "O+", "contact_with_confirmed_carrier": false, "contact_with_suspected_carrier": false, "estimated_contact_date": null, "past_travel": false, "countries_travelled_old": null, "countries_travelled": null, "date_of_return": null, "allergies": "", "present_health": "", "ongoing_medication": "", "has_SARI": false, "is_antenatal": false, "last_menstruation_start_date": null, "date_of_delivery": null, "ward_old": "", "ward": 15162, "local_body": 6, "district": 7, "state": 1, "is_migrant_worker": false, "disease_status": 3, "number_of_aged_dependents": 0, "number_of_chronic_diseased_dependents": 0, "last_edited": 2, "action": 10, "review_time": null, "created_by": 2, "is_active": true, "date_of_receipt_of_information": "2023-12-06T08:43:50.496Z", "test_id": "", "date_of_test": null, "srf_id": "", "test_type": 10, "allow_transfer": true, "last_consultation": 8, "will_donate_blood": null, "fit_for_blood_donation": null, "village": "", "designation_of_health_care_worker": "", "instituion_of_health_care_worker": "", "transit_details": null, "frontline_worker": null, "date_of_result": null, "number_of_primary_contacts": null, "number_of_secondary_contacts": null, "is_vaccinated": false, "number_of_doses": 0, "vaccine_name": null, "covin_id": null, "last_vaccinated_date": null, "cluster_name": null, "is_declared_positive": false, "date_declared_positive": null, "assigned_to": null}}, {"model": "facility.patientregistration", "pk": 9, "fields": {"external_id": "40901064-1485-4a70-be25-4deac6761a1a", "created_date": "2023-12-06T08:44:13.721Z", "modified_date": "2023-12-06T08:44:25.130Z", "deleted": false, "source": 10, "facility": 1, "nearest_facility": null, "meta_info": null, "name": "Du<PERSON>", "gender": 1, "phone_number": "+************", "emergency_phone_number": "+************", "address": "Test Patient Address", "permanent_address": "Test Patient Address", "pincode": 682001, "date_of_birth": "2001-01-01", "year_of_birth": 2001, "death_datetime": null, "nationality": "India", "passport_no": "", "ration_card_category": null, "is_medical_worker": false, "blood_group": "O+", "contact_with_confirmed_carrier": false, "contact_with_suspected_carrier": false, "estimated_contact_date": null, "past_travel": false, "countries_travelled_old": null, "countries_travelled": null, "date_of_return": null, "allergies": "", "present_health": "", "ongoing_medication": "", "has_SARI": false, "is_antenatal": false, "last_menstruation_start_date": null, "date_of_delivery": null, "ward_old": "", "ward": 15162, "local_body": 6, "district": 7, "state": 1, "is_migrant_worker": false, "disease_status": 3, "number_of_aged_dependents": 0, "number_of_chronic_diseased_dependents": 0, "last_edited": 2, "action": 10, "review_time": null, "created_by": 2, "is_active": true, "date_of_receipt_of_information": "2023-12-06T08:44:13.720Z", "test_id": "", "date_of_test": null, "srf_id": "", "test_type": 10, "allow_transfer": true, "last_consultation": 9, "will_donate_blood": null, "fit_for_blood_donation": null, "village": "", "designation_of_health_care_worker": "", "instituion_of_health_care_worker": "", "transit_details": null, "frontline_worker": null, "date_of_result": null, "number_of_primary_contacts": null, "number_of_secondary_contacts": null, "is_vaccinated": false, "number_of_doses": 0, "vaccine_name": null, "covin_id": null, "last_vaccinated_date": null, "cluster_name": null, "is_declared_positive": false, "date_declared_positive": null, "assigned_to": null}}, {"model": "facility.patientregistration", "pk": 10, "fields": {"external_id": "658d1b4b-2061-4d8b-85da-ebf95d9e2789", "created_date": "2023-12-06T08:44:36.349Z", "modified_date": "2023-12-06T08:44:47.705Z", "deleted": false, "source": 10, "facility": 1, "nearest_facility": null, "meta_info": null, "name": "Dummy <PERSON>", "gender": 1, "phone_number": "+************", "emergency_phone_number": "+************", "address": "Test Patient Address", "permanent_address": "Test Patient Address", "pincode": 682001, "date_of_birth": "2001-01-01", "year_of_birth": 2001, "death_datetime": null, "nationality": "India", "passport_no": "", "ration_card_category": null, "is_medical_worker": false, "blood_group": "O+", "contact_with_confirmed_carrier": false, "contact_with_suspected_carrier": false, "estimated_contact_date": null, "past_travel": false, "countries_travelled_old": null, "countries_travelled": null, "date_of_return": null, "allergies": "", "present_health": "", "ongoing_medication": "", "has_SARI": false, "is_antenatal": false, "last_menstruation_start_date": null, "date_of_delivery": null, "ward_old": "", "ward": 15162, "local_body": 6, "district": 7, "state": 1, "is_migrant_worker": false, "disease_status": 3, "number_of_aged_dependents": 0, "number_of_chronic_diseased_dependents": 0, "last_edited": 2, "action": 10, "review_time": null, "created_by": 2, "is_active": true, "date_of_receipt_of_information": "2023-12-06T08:44:36.348Z", "test_id": "", "date_of_test": null, "srf_id": "", "test_type": 10, "allow_transfer": true, "last_consultation": 10, "will_donate_blood": null, "fit_for_blood_donation": null, "village": "", "designation_of_health_care_worker": "", "instituion_of_health_care_worker": "", "transit_details": null, "frontline_worker": null, "date_of_result": null, "number_of_primary_contacts": null, "number_of_secondary_contacts": null, "is_vaccinated": false, "number_of_doses": 0, "vaccine_name": null, "covin_id": null, "last_vaccinated_date": null, "cluster_name": null, "is_declared_positive": false, "date_declared_positive": null, "assigned_to": null}}, {"model": "facility.patientregistration", "pk": 11, "fields": {"external_id": "200ed4e4-7a2f-49d8-b50b-51fed5d830e7", "created_date": "2023-12-06T08:44:58.881Z", "modified_date": "2023-12-06T08:45:10.188Z", "deleted": false, "source": 10, "facility": 1, "nearest_facility": null, "meta_info": null, "name": "Du<PERSON>", "gender": 1, "phone_number": "+************", "emergency_phone_number": "+************", "address": "Test Patient Address", "permanent_address": "Test Patient Address", "pincode": 682001, "date_of_birth": "2001-01-01", "year_of_birth": 2001, "death_datetime": null, "nationality": "India", "passport_no": "", "ration_card_category": null, "is_medical_worker": false, "blood_group": "O+", "contact_with_confirmed_carrier": false, "contact_with_suspected_carrier": false, "estimated_contact_date": null, "past_travel": false, "countries_travelled_old": null, "countries_travelled": null, "date_of_return": null, "allergies": "", "present_health": "", "ongoing_medication": "", "has_SARI": false, "is_antenatal": false, "last_menstruation_start_date": null, "date_of_delivery": null, "ward_old": "", "ward": 15162, "local_body": 6, "district": 7, "state": 1, "is_migrant_worker": false, "disease_status": 3, "number_of_aged_dependents": 0, "number_of_chronic_diseased_dependents": 0, "last_edited": 2, "action": 10, "review_time": null, "created_by": 2, "is_active": true, "date_of_receipt_of_information": "2023-12-06T08:44:58.879Z", "test_id": "", "date_of_test": null, "srf_id": "", "test_type": 10, "allow_transfer": true, "last_consultation": 11, "will_donate_blood": null, "fit_for_blood_donation": null, "village": "", "designation_of_health_care_worker": "", "instituion_of_health_care_worker": "", "transit_details": null, "frontline_worker": null, "date_of_result": null, "number_of_primary_contacts": null, "number_of_secondary_contacts": null, "is_vaccinated": false, "number_of_doses": 0, "vaccine_name": null, "covin_id": null, "last_vaccinated_date": null, "cluster_name": null, "is_declared_positive": false, "date_declared_positive": null, "assigned_to": null}}, {"model": "facility.patientregistration", "pk": 12, "fields": {"external_id": "a51d94bf-8951-4706-9e91-68cde0c470e4", "created_date": "2023-12-06T08:45:21.706Z", "modified_date": "2023-12-06T08:45:34.547Z", "deleted": false, "source": 10, "facility": 1, "nearest_facility": null, "meta_info": null, "name": "Dummy <PERSON>", "gender": 1, "phone_number": "+************", "emergency_phone_number": "+************", "address": "Test Patient Address", "permanent_address": "Test Patient Address", "pincode": 682001, "date_of_birth": "2001-01-01", "year_of_birth": 2001, "death_datetime": null, "nationality": "India", "passport_no": "", "ration_card_category": null, "is_medical_worker": false, "blood_group": "O+", "contact_with_confirmed_carrier": false, "contact_with_suspected_carrier": false, "estimated_contact_date": null, "past_travel": false, "countries_travelled_old": null, "countries_travelled": null, "date_of_return": null, "allergies": "", "present_health": "", "ongoing_medication": "", "has_SARI": false, "is_antenatal": false, "last_menstruation_start_date": null, "date_of_delivery": null, "ward_old": "", "ward": 15162, "local_body": 6, "district": 7, "state": 1, "is_migrant_worker": false, "disease_status": 3, "number_of_aged_dependents": 0, "number_of_chronic_diseased_dependents": 0, "last_edited": 2, "action": 10, "review_time": null, "created_by": 2, "is_active": true, "date_of_receipt_of_information": "2023-12-06T08:45:21.705Z", "test_id": "", "date_of_test": null, "srf_id": "", "test_type": 10, "allow_transfer": true, "last_consultation": 12, "will_donate_blood": null, "fit_for_blood_donation": null, "village": "", "designation_of_health_care_worker": "", "instituion_of_health_care_worker": "", "transit_details": null, "frontline_worker": null, "date_of_result": null, "number_of_primary_contacts": null, "number_of_secondary_contacts": null, "is_vaccinated": false, "number_of_doses": 0, "vaccine_name": null, "covin_id": null, "last_vaccinated_date": null, "cluster_name": null, "is_declared_positive": false, "date_declared_positive": null, "assigned_to": null}}, {"model": "facility.patientregistration", "pk": 13, "fields": {"external_id": "b348dd92-be95-4e20-b929-e2bf7d6bf2e9", "created_date": "2023-12-06T08:45:45.771Z", "modified_date": "2023-12-06T08:45:57.167Z", "deleted": false, "source": 10, "facility": 1, "nearest_facility": null, "meta_info": null, "name": "Du<PERSON>", "gender": 1, "phone_number": "+************", "emergency_phone_number": "+************", "address": "Test Patient Address", "permanent_address": "Test Patient Address", "pincode": 682001, "date_of_birth": "2001-01-01", "year_of_birth": 2001, "death_datetime": null, "nationality": "India", "passport_no": "", "ration_card_category": null, "is_medical_worker": false, "blood_group": "O+", "contact_with_confirmed_carrier": false, "contact_with_suspected_carrier": false, "estimated_contact_date": null, "past_travel": false, "countries_travelled_old": null, "countries_travelled": null, "date_of_return": null, "allergies": "", "present_health": "", "ongoing_medication": "", "has_SARI": false, "is_antenatal": false, "last_menstruation_start_date": null, "date_of_delivery": null, "ward_old": "", "ward": 15162, "local_body": 6, "district": 7, "state": 1, "is_migrant_worker": false, "disease_status": 3, "number_of_aged_dependents": 0, "number_of_chronic_diseased_dependents": 0, "last_edited": 2, "action": 10, "review_time": null, "created_by": 2, "is_active": true, "date_of_receipt_of_information": "2023-12-06T08:45:45.770Z", "test_id": "", "date_of_test": null, "srf_id": "", "test_type": 10, "allow_transfer": true, "last_consultation": 13, "will_donate_blood": null, "fit_for_blood_donation": null, "village": "", "designation_of_health_care_worker": "", "instituion_of_health_care_worker": "", "transit_details": null, "frontline_worker": null, "date_of_result": null, "number_of_primary_contacts": null, "number_of_secondary_contacts": null, "is_vaccinated": false, "number_of_doses": 0, "vaccine_name": null, "covin_id": null, "last_vaccinated_date": null, "cluster_name": null, "is_declared_positive": false, "date_declared_positive": null, "assigned_to": null}}, {"model": "facility.patientregistration", "pk": 14, "fields": {"external_id": "8fd405a8-cf0f-4487-b147-f2b3edfae213", "created_date": "2023-12-06T08:46:08.683Z", "modified_date": "2023-12-06T08:46:19.974Z", "deleted": false, "source": 10, "facility": 1, "nearest_facility": null, "meta_info": null, "name": "Du<PERSON>", "gender": 1, "phone_number": "+************", "emergency_phone_number": "+************", "address": "Test Patient Address", "permanent_address": "Test Patient Address", "pincode": 682001, "date_of_birth": "2001-01-01", "year_of_birth": 2001, "death_datetime": null, "nationality": "India", "passport_no": "", "ration_card_category": null, "is_medical_worker": false, "blood_group": "O+", "contact_with_confirmed_carrier": false, "contact_with_suspected_carrier": false, "estimated_contact_date": null, "past_travel": false, "countries_travelled_old": null, "countries_travelled": null, "date_of_return": null, "allergies": "", "present_health": "", "ongoing_medication": "", "has_SARI": false, "is_antenatal": false, "last_menstruation_start_date": null, "date_of_delivery": null, "ward_old": "", "ward": 15162, "local_body": 6, "district": 7, "state": 1, "is_migrant_worker": false, "disease_status": 3, "number_of_aged_dependents": 0, "number_of_chronic_diseased_dependents": 0, "last_edited": 2, "action": 10, "review_time": null, "created_by": 2, "is_active": true, "date_of_receipt_of_information": "2023-12-06T08:46:08.682Z", "test_id": "", "date_of_test": null, "srf_id": "", "test_type": 10, "allow_transfer": true, "last_consultation": 14, "will_donate_blood": null, "fit_for_blood_donation": null, "village": "", "designation_of_health_care_worker": "", "instituion_of_health_care_worker": "", "transit_details": null, "frontline_worker": null, "date_of_result": null, "number_of_primary_contacts": null, "number_of_secondary_contacts": null, "is_vaccinated": false, "number_of_doses": 0, "vaccine_name": null, "covin_id": null, "last_vaccinated_date": null, "cluster_name": null, "is_declared_positive": false, "date_declared_positive": null, "assigned_to": null}}, {"model": "facility.patientregistration", "pk": 15, "fields": {"external_id": "efb7f800-ca35-4053-b95e-fae6b2df87db", "created_date": "2023-12-06T08:46:31.290Z", "modified_date": "2023-12-06T08:46:42.617Z", "deleted": false, "source": 10, "facility": 1, "nearest_facility": null, "meta_info": null, "name": "Discharge Patient One", "gender": 1, "phone_number": "+************", "emergency_phone_number": "+************", "address": "Test Patient Address", "permanent_address": "Test Patient Address", "pincode": 682001, "date_of_birth": "2001-01-01", "year_of_birth": 2001, "death_datetime": null, "nationality": "India", "passport_no": "", "ration_card_category": null, "is_medical_worker": false, "blood_group": "O+", "contact_with_confirmed_carrier": false, "contact_with_suspected_carrier": false, "estimated_contact_date": null, "past_travel": false, "countries_travelled_old": null, "countries_travelled": null, "date_of_return": null, "allergies": "", "present_health": "", "ongoing_medication": "", "has_SARI": false, "is_antenatal": false, "last_menstruation_start_date": null, "date_of_delivery": null, "ward_old": "", "ward": 15162, "local_body": 6, "district": 7, "state": 1, "is_migrant_worker": false, "disease_status": 3, "number_of_aged_dependents": 0, "number_of_chronic_diseased_dependents": 0, "last_edited": 2, "action": 10, "review_time": null, "created_by": 2, "is_active": true, "date_of_receipt_of_information": "2023-12-06T08:46:31.289Z", "test_id": "", "date_of_test": null, "srf_id": "", "test_type": 10, "allow_transfer": true, "last_consultation": 15, "will_donate_blood": null, "fit_for_blood_donation": null, "village": "", "designation_of_health_care_worker": "", "instituion_of_health_care_worker": "", "transit_details": null, "frontline_worker": null, "date_of_result": null, "number_of_primary_contacts": null, "number_of_secondary_contacts": null, "is_vaccinated": false, "number_of_doses": 0, "vaccine_name": null, "covin_id": null, "last_vaccinated_date": null, "cluster_name": null, "is_declared_positive": false, "date_declared_positive": null, "assigned_to": null}}, {"model": "facility.patientregistration", "pk": 16, "fields": {"external_id": "7fb4585a-728a-481c-94a0-220733cd0dbb", "created_date": "2023-12-06T08:46:55.987Z", "modified_date": "2023-12-06T08:47:07.467Z", "deleted": false, "source": 10, "facility": 1, "nearest_facility": null, "meta_info": null, "name": "Discharge Patient Two", "gender": 1, "phone_number": "+************", "emergency_phone_number": "+************", "address": "Test Patient Address", "permanent_address": "Test Patient Address", "pincode": 682001, "date_of_birth": "2001-01-01", "year_of_birth": 2001, "death_datetime": null, "nationality": "India", "passport_no": "", "ration_card_category": null, "is_medical_worker": false, "blood_group": "O+", "contact_with_confirmed_carrier": false, "contact_with_suspected_carrier": false, "estimated_contact_date": null, "past_travel": false, "countries_travelled_old": null, "countries_travelled": null, "date_of_return": null, "allergies": "", "present_health": "", "ongoing_medication": "", "has_SARI": false, "is_antenatal": false, "last_menstruation_start_date": null, "date_of_delivery": null, "ward_old": "", "ward": 15162, "local_body": 6, "district": 7, "state": 1, "is_migrant_worker": false, "disease_status": 3, "number_of_aged_dependents": 0, "number_of_chronic_diseased_dependents": 0, "last_edited": 2, "action": 10, "review_time": null, "created_by": 2, "is_active": true, "date_of_receipt_of_information": "2023-12-06T08:46:55.986Z", "test_id": "", "date_of_test": null, "srf_id": "", "test_type": 10, "allow_transfer": true, "last_consultation": 16, "will_donate_blood": null, "fit_for_blood_donation": null, "village": "", "designation_of_health_care_worker": "", "instituion_of_health_care_worker": "", "transit_details": null, "frontline_worker": null, "date_of_result": null, "number_of_primary_contacts": null, "number_of_secondary_contacts": null, "is_vaccinated": false, "number_of_doses": 0, "vaccine_name": null, "covin_id": null, "last_vaccinated_date": null, "cluster_name": null, "is_declared_positive": false, "date_declared_positive": null, "assigned_to": null}}, {"model": "facility.patientregistration", "pk": 17, "fields": {"external_id": "0b8e61c2-b7d2-4a58-ad8c-b82ed46dc7e0", "created_date": "2023-12-06T08:47:19.338Z", "modified_date": "2023-12-06T08:47:30.885Z", "deleted": false, "source": 10, "facility": 1, "nearest_facility": null, "meta_info": null, "name": "Discharge Patient Three", "gender": 1, "phone_number": "+************", "emergency_phone_number": "+************", "address": "Test Patient Address", "permanent_address": "Test Patient Address", "pincode": 682001, "date_of_birth": "2001-01-01", "year_of_birth": 2001, "death_datetime": null, "nationality": "India", "passport_no": "", "ration_card_category": null, "is_medical_worker": false, "blood_group": "O+", "contact_with_confirmed_carrier": false, "contact_with_suspected_carrier": false, "estimated_contact_date": null, "past_travel": false, "countries_travelled_old": null, "countries_travelled": null, "date_of_return": null, "allergies": "", "present_health": "", "ongoing_medication": "", "has_SARI": false, "is_antenatal": false, "last_menstruation_start_date": null, "date_of_delivery": null, "ward_old": "", "ward": 15162, "local_body": 6, "district": 7, "state": 1, "is_migrant_worker": false, "disease_status": 3, "number_of_aged_dependents": 0, "number_of_chronic_diseased_dependents": 0, "last_edited": 2, "action": 10, "review_time": null, "created_by": 2, "is_active": true, "date_of_receipt_of_information": "2023-12-06T08:47:19.337Z", "test_id": "", "date_of_test": null, "srf_id": "", "test_type": 10, "allow_transfer": true, "last_consultation": 17, "will_donate_blood": null, "fit_for_blood_donation": null, "village": "", "designation_of_health_care_worker": "", "instituion_of_health_care_worker": "", "transit_details": null, "frontline_worker": null, "date_of_result": null, "number_of_primary_contacts": null, "number_of_secondary_contacts": null, "is_vaccinated": false, "number_of_doses": 0, "vaccine_name": null, "covin_id": null, "last_vaccinated_date": null, "cluster_name": null, "is_declared_positive": false, "date_declared_positive": null, "assigned_to": null}}, {"model": "facility.patientregistration", "pk": 18, "fields": {"external_id": "0f9552bd-1498-4b92-8f62-a83ad6f493b6", "created_date": "2023-12-06T08:47:42.391Z", "modified_date": "2023-12-06T08:47:53.773Z", "deleted": false, "source": 10, "facility": 1, "nearest_facility": null, "meta_info": null, "name": "Discharge <PERSON> Four", "gender": 1, "phone_number": "+************", "emergency_phone_number": "+************", "address": "Test Patient Address", "permanent_address": "Test Patient Address", "pincode": 682001, "date_of_birth": "2001-01-01", "year_of_birth": 2001, "death_datetime": null, "nationality": "India", "passport_no": "", "ration_card_category": null, "is_medical_worker": false, "blood_group": "O+", "contact_with_confirmed_carrier": false, "contact_with_suspected_carrier": false, "estimated_contact_date": null, "past_travel": false, "countries_travelled_old": null, "countries_travelled": null, "date_of_return": null, "allergies": "", "present_health": "", "ongoing_medication": "", "has_SARI": false, "is_antenatal": false, "last_menstruation_start_date": null, "date_of_delivery": null, "ward_old": "", "ward": 15162, "local_body": 6, "district": 7, "state": 1, "is_migrant_worker": false, "disease_status": 3, "number_of_aged_dependents": 0, "number_of_chronic_diseased_dependents": 0, "last_edited": 2, "action": 10, "review_time": null, "created_by": 2, "is_active": true, "date_of_receipt_of_information": "2023-12-06T08:47:42.390Z", "test_id": "", "date_of_test": null, "srf_id": "", "test_type": 10, "allow_transfer": true, "last_consultation": 18, "will_donate_blood": null, "fit_for_blood_donation": null, "village": "", "designation_of_health_care_worker": "", "instituion_of_health_care_worker": "", "transit_details": null, "frontline_worker": null, "date_of_result": null, "number_of_primary_contacts": null, "number_of_secondary_contacts": null, "is_vaccinated": false, "number_of_doses": 0, "vaccine_name": null, "covin_id": null, "last_vaccinated_date": null, "cluster_name": null, "is_declared_positive": false, "date_declared_positive": null, "assigned_to": null}}, {"model": "facility.disease", "pk": 2, "fields": {"patient": 2, "disease": 1, "details": "", "deleted": false}}, {"model": "facility.disease", "pk": 3, "fields": {"patient": 1, "disease": 1, "details": "", "deleted": false}}, {"model": "facility.disease", "pk": 4, "fields": {"patient": 3, "disease": 1, "details": "", "deleted": false}}, {"model": "facility.disease", "pk": 5, "fields": {"patient": 4, "disease": 1, "details": "", "deleted": false}}, {"model": "facility.disease", "pk": 6, "fields": {"patient": 5, "disease": 1, "details": "", "deleted": false}}, {"model": "facility.disease", "pk": 7, "fields": {"patient": 6, "disease": 1, "details": "", "deleted": false}}, {"model": "facility.disease", "pk": 8, "fields": {"patient": 7, "disease": 1, "details": "", "deleted": false}}, {"model": "facility.disease", "pk": 9, "fields": {"patient": 8, "disease": 1, "details": "", "deleted": false}}, {"model": "facility.disease", "pk": 10, "fields": {"patient": 9, "disease": 1, "details": "", "deleted": false}}, {"model": "facility.disease", "pk": 11, "fields": {"patient": 10, "disease": 1, "details": "", "deleted": false}}, {"model": "facility.disease", "pk": 12, "fields": {"patient": 11, "disease": 1, "details": "", "deleted": false}}, {"model": "facility.disease", "pk": 13, "fields": {"patient": 12, "disease": 1, "details": "", "deleted": false}}, {"model": "facility.disease", "pk": 14, "fields": {"patient": 13, "disease": 1, "details": "", "deleted": false}}, {"model": "facility.disease", "pk": 15, "fields": {"patient": 14, "disease": 1, "details": "", "deleted": false}}, {"model": "facility.disease", "pk": 16, "fields": {"patient": 15, "disease": 1, "details": "", "deleted": false}}, {"model": "facility.disease", "pk": 17, "fields": {"patient": 16, "disease": 1, "details": "", "deleted": false}}, {"model": "facility.disease", "pk": 18, "fields": {"patient": 17, "disease": 1, "details": "", "deleted": false}}, {"model": "facility.disease", "pk": 19, "fields": {"patient": 18, "disease": 1, "details": "", "deleted": false}}, {"model": "facility.patientexternaltest", "pk": 1, "fields": {"external_id": "98133a62-f16b-4526-9d0f-8e38063ab6d8", "created_date": "2024-02-01T14:01:21.153Z", "modified_date": "2024-02-01T14:01:21.153Z", "deleted": false, "srf_id": "00/EKM/0000", "name": "Patient 1", "age": 24, "age_in": "years", "gender": "m", "address": "CSN HQ\nKochi, Kerala", "mobile_number": "**********", "is_repeat": false, "patient_status": "Asymptomatic", "ward": 15100, "local_body": 704, "district": 7, "source": "Secondary contact aparna", "patient_category": "Cat 17: All individuals who wish to get themselves tested", "lab_name": "Karothukuzhi Laboratory", "test_type": "Antigen", "sample_type": "Ag-SD_Biosensor_Standard_Q_COVID-19_Ag_detection_kit", "result": "Negative", "sample_collection_date": "2020-10-14", "result_date": "2020-10-15", "patient_created": false}}, {"model": "facility.patientexternaltest", "pk": 2, "fields": {"external_id": "c80f14f5-6e41-488e-b7b4-d16a77b23b6f", "created_date": "2024-02-01T14:01:21.160Z", "modified_date": "2024-02-01T14:01:21.160Z", "deleted": false, "srf_id": "00/EKM/0001", "name": "Patient 2", "age": 24, "age_in": "years", "gender": "m", "address": "CSN HQ\nKochi, Kerala", "mobile_number": "**********", "is_repeat": false, "patient_status": "Asymptomatic", "ward": 15100, "local_body": 704, "district": 7, "source": "Secondary contact aparna", "patient_category": "Cat 17: All individuals who wish to get themselves tested", "lab_name": "Karothukuzhi Laboratory", "test_type": "Antigen", "sample_type": "Ag-SD_Biosensor_Standard_Q_COVID-19_Ag_detection_kit", "result": "Negative", "sample_collection_date": "2020-10-14", "result_date": "2020-10-15", "patient_created": false}}, {"model": "facility.patientexternaltest", "pk": 3, "fields": {"external_id": "b269ee70-7334-431d-a6de-c99b01b9c01c", "created_date": "2024-02-01T14:01:21.164Z", "modified_date": "2024-02-01T14:01:21.164Z", "deleted": false, "srf_id": "00/EKM/0002", "name": "Patient 3", "age": 24, "age_in": "years", "gender": "m", "address": "CSN HQ\nKochi, Kerala", "mobile_number": "**********", "is_repeat": false, "patient_status": "Asymptomatic", "ward": 15100, "local_body": 704, "district": 7, "source": "Secondary contact aparna", "patient_category": "Cat 17: All individuals who wish to get themselves tested", "lab_name": "Karothukuzhi Laboratory", "test_type": "Antigen", "sample_type": "Ag-SD_Biosensor_Standard_Q_COVID-19_Ag_detection_kit", "result": "Negative", "sample_collection_date": "2020-10-14", "result_date": "2020-10-15", "patient_created": false}}, {"model": "facility.patientexternaltest", "pk": 4, "fields": {"external_id": "8b9a9560-a936-4960-bfda-e609fe7093ea", "created_date": "2024-02-01T14:01:21.169Z", "modified_date": "2024-02-01T14:01:21.169Z", "deleted": false, "srf_id": "00/EKM/0003", "name": "Patient 4", "age": 24, "age_in": "years", "gender": "m", "address": "CSN HQ\nKochi, Kerala", "mobile_number": "**********", "is_repeat": false, "patient_status": "Asymptomatic", "ward": 15100, "local_body": 704, "district": 7, "source": "Secondary contact aparna", "patient_category": "Cat 17: All individuals who wish to get themselves tested", "lab_name": "Karothukuzhi Laboratory", "test_type": "Antigen", "sample_type": "Ag-SD_Biosensor_Standard_Q_COVID-19_Ag_detection_kit", "result": "Negative", "sample_collection_date": "2020-10-14", "result_date": "2020-10-15", "patient_created": false}}, {"model": "facility.patientexternaltest", "pk": 5, "fields": {"external_id": "864e21d3-b470-4a1c-8836-0125e150233b", "created_date": "2024-02-01T14:01:21.174Z", "modified_date": "2024-02-01T14:01:21.174Z", "deleted": false, "srf_id": "00/EKM/0004", "name": "Patient 5", "age": 24, "age_in": "years", "gender": "m", "address": "CSN HQ\nKochi, Kerala", "mobile_number": "**********", "is_repeat": false, "patient_status": "Asymptomatic", "ward": 15100, "local_body": 704, "district": 7, "source": "Secondary contact aparna", "patient_category": "Cat 17: All individuals who wish to get themselves tested", "lab_name": "Karothukuzhi Laboratory", "test_type": "Antigen", "sample_type": "Ag-SD_Biosensor_Standard_Q_COVID-19_Ag_detection_kit", "result": "Negative", "sample_collection_date": "2020-10-14", "result_date": "2020-10-15", "patient_created": false}}, {"model": "facility.patientexternaltest", "pk": 6, "fields": {"external_id": "accb12f6-3d54-4e5a-a9ac-52d166b94fcf", "created_date": "2024-02-01T14:01:21.178Z", "modified_date": "2024-02-01T14:01:21.178Z", "deleted": false, "srf_id": "00/EKM/0005", "name": "Patient 6", "age": 24, "age_in": "years", "gender": "m", "address": "CSN HQ\nKochi, Kerala", "mobile_number": "**********", "is_repeat": false, "patient_status": "Asymptomatic", "ward": 15100, "local_body": 704, "district": 7, "source": "Secondary contact aparna", "patient_category": "Cat 17: All individuals who wish to get themselves tested", "lab_name": "Karothukuzhi Laboratory", "test_type": "Antigen", "sample_type": "Ag-SD_Biosensor_Standard_Q_COVID-19_Ag_detection_kit", "result": "Negative", "sample_collection_date": "2020-10-14", "result_date": "2020-10-15", "patient_created": false}}, {"model": "facility.patientexternaltest", "pk": 7, "fields": {"external_id": "8992577c-ae45-430c-9e6d-e4431be186e3", "created_date": "2024-02-01T14:01:21.183Z", "modified_date": "2024-02-01T14:01:21.183Z", "deleted": false, "srf_id": "00/EKM/0006", "name": "Patient 7", "age": 24, "age_in": "years", "gender": "m", "address": "CSN HQ\nKochi, Kerala", "mobile_number": "**********", "is_repeat": false, "patient_status": "Asymptomatic", "ward": 15100, "local_body": 704, "district": 7, "source": "Secondary contact aparna", "patient_category": "Cat 17: All individuals who wish to get themselves tested", "lab_name": "Karothukuzhi Laboratory", "test_type": "Antigen", "sample_type": "Ag-SD_Biosensor_Standard_Q_COVID-19_Ag_detection_kit", "result": "Negative", "sample_collection_date": "2020-10-14", "result_date": "2020-10-15", "patient_created": false}}, {"model": "facility.patientexternaltest", "pk": 8, "fields": {"external_id": "04bfef7a-6020-416e-b06c-993b2d5d9373", "created_date": "2024-02-01T14:01:21.187Z", "modified_date": "2024-02-01T14:01:21.187Z", "deleted": false, "srf_id": "00/EKM/0007", "name": "Patient 8", "age": 24, "age_in": "years", "gender": "m", "address": "CSN HQ\nKochi, Kerala", "mobile_number": "**********", "is_repeat": false, "patient_status": "Asymptomatic", "ward": 15100, "local_body": 704, "district": 7, "source": "Secondary contact aparna", "patient_category": "Cat 17: All individuals who wish to get themselves tested", "lab_name": "Karothukuzhi Laboratory", "test_type": "Antigen", "sample_type": "Ag-SD_Biosensor_Standard_Q_COVID-19_Ag_detection_kit", "result": "Negative", "sample_collection_date": "2020-10-14", "result_date": "2020-10-15", "patient_created": false}}, {"model": "facility.patientexternaltest", "pk": 9, "fields": {"external_id": "e26b9c69-f196-4753-9b63-67c7cb11184a", "created_date": "2024-02-01T14:01:21.192Z", "modified_date": "2024-02-01T14:01:21.192Z", "deleted": false, "srf_id": "00/EKM/0008", "name": "Patient 9", "age": 24, "age_in": "years", "gender": "m", "address": "CSN HQ\nKochi, Kerala", "mobile_number": "**********", "is_repeat": false, "patient_status": "Asymptomatic", "ward": 15100, "local_body": 704, "district": 7, "source": "Secondary contact aparna", "patient_category": "Cat 17: All individuals who wish to get themselves tested", "lab_name": "Karothukuzhi Laboratory", "test_type": "Antigen", "sample_type": "Ag-SD_Biosensor_Standard_Q_COVID-19_Ag_detection_kit", "result": "Negative", "sample_collection_date": "2020-10-14", "result_date": "2020-10-15", "patient_created": false}}, {"model": "facility.patientexternaltest", "pk": 10, "fields": {"external_id": "f1221cad-3d1e-4412-b14c-d8a1c38fff20", "created_date": "2024-02-01T14:01:21.196Z", "modified_date": "2024-02-01T14:01:21.196Z", "deleted": false, "srf_id": "00/EKM/0009", "name": "Patient 10", "age": 24, "age_in": "years", "gender": "m", "address": "CSN HQ\nKochi, Kerala", "mobile_number": "**********", "is_repeat": false, "patient_status": "Asymptomatic", "ward": 15100, "local_body": 704, "district": 7, "source": "Secondary contact aparna", "patient_category": "Cat 17: All individuals who wish to get themselves tested", "lab_name": "Karothukuzhi Laboratory", "test_type": "Antigen", "sample_type": "Ag-SD_Biosensor_Standard_Q_COVID-19_Ag_detection_kit", "result": "Negative", "sample_collection_date": "2020-10-14", "result_date": "2020-10-15", "patient_created": false}}, {"model": "facility.patientexternaltest", "pk": 11, "fields": {"external_id": "23a8dafe-d3ba-4de4-a3cb-41afe36deeb7", "created_date": "2024-02-01T14:01:21.201Z", "modified_date": "2024-02-01T14:01:21.201Z", "deleted": false, "srf_id": "00/EKM/0010", "name": "Patient 11", "age": 24, "age_in": "years", "gender": "m", "address": "CSN HQ\nKochi, Kerala", "mobile_number": "**********", "is_repeat": false, "patient_status": "Asymptomatic", "ward": 15100, "local_body": 704, "district": 7, "source": "Secondary contact aparna", "patient_category": "Cat 17: All individuals who wish to get themselves tested", "lab_name": "Karothukuzhi Laboratory", "test_type": "Antigen", "sample_type": "Ag-SD_Biosensor_Standard_Q_COVID-19_Ag_detection_kit", "result": "Negative", "sample_collection_date": "2020-10-14", "result_date": "2020-10-15", "patient_created": false}}, {"model": "facility.patientexternaltest", "pk": 12, "fields": {"external_id": "18d89352-c023-4855-b795-aef5677f3bc5", "created_date": "2024-02-01T14:01:21.205Z", "modified_date": "2024-02-01T14:01:21.205Z", "deleted": false, "srf_id": "00/EKM/0011", "name": "Patient 12", "age": 24, "age_in": "years", "gender": "m", "address": "CSN HQ\nKochi, Kerala", "mobile_number": "**********", "is_repeat": false, "patient_status": "Asymptomatic", "ward": 15100, "local_body": 704, "district": 7, "source": "Secondary contact aparna", "patient_category": "Cat 17: All individuals who wish to get themselves tested", "lab_name": "Karothukuzhi Laboratory", "test_type": "Antigen", "sample_type": "Ag-SD_Biosensor_Standard_Q_COVID-19_Ag_detection_kit", "result": "Negative", "sample_collection_date": "2020-10-14", "result_date": "2020-10-15", "patient_created": false}}, {"model": "facility.patientexternaltest", "pk": 13, "fields": {"external_id": "fa665077-0c86-4bc5-9629-45c0922dd3dc", "created_date": "2024-02-01T14:01:21.210Z", "modified_date": "2024-02-01T14:01:21.210Z", "deleted": false, "srf_id": "00/EKM/0012", "name": "Patient 13", "age": 24, "age_in": "years", "gender": "m", "address": "CSN HQ\nKochi, Kerala", "mobile_number": "**********", "is_repeat": false, "patient_status": "Asymptomatic", "ward": 15100, "local_body": 704, "district": 7, "source": "Secondary contact aparna", "patient_category": "Cat 17: All individuals who wish to get themselves tested", "lab_name": "Karothukuzhi Laboratory", "test_type": "Antigen", "sample_type": "Ag-SD_Biosensor_Standard_Q_COVID-19_Ag_detection_kit", "result": "Negative", "sample_collection_date": "2020-10-14", "result_date": "2020-10-15", "patient_created": false}}, {"model": "facility.patientexternaltest", "pk": 14, "fields": {"external_id": "3ec19e36-380e-46f2-a3c4-b9440e66f9bf", "created_date": "2024-02-01T14:01:21.215Z", "modified_date": "2024-02-01T14:01:21.215Z", "deleted": false, "srf_id": "00/EKM/0013", "name": "Patient 14", "age": 24, "age_in": "years", "gender": "m", "address": "CSN HQ\nKochi, Kerala", "mobile_number": "**********", "is_repeat": false, "patient_status": "Asymptomatic", "ward": 15100, "local_body": 704, "district": 7, "source": "Secondary contact aparna", "patient_category": "Cat 17: All individuals who wish to get themselves tested", "lab_name": "Karothukuzhi Laboratory", "test_type": "Antigen", "sample_type": "Ag-SD_Biosensor_Standard_Q_COVID-19_Ag_detection_kit", "result": "Negative", "sample_collection_date": "2020-10-14", "result_date": "2020-10-15", "patient_created": false}}, {"model": "facility.patientexternaltest", "pk": 15, "fields": {"external_id": "9b3c6a56-786a-42ef-bc04-ca07687a0899", "created_date": "2024-02-01T14:01:21.219Z", "modified_date": "2024-02-01T14:01:21.219Z", "deleted": false, "srf_id": "00/EKM/0014", "name": "Patient 15", "age": 24, "age_in": "years", "gender": "m", "address": "CSN HQ\nKochi, Kerala", "mobile_number": "**********", "is_repeat": false, "patient_status": "Asymptomatic", "ward": 15100, "local_body": 704, "district": 7, "source": "Secondary contact aparna", "patient_category": "Cat 17: All individuals who wish to get themselves tested", "lab_name": "Karothukuzhi Laboratory", "test_type": "Antigen", "sample_type": "Ag-SD_Biosensor_Standard_Q_COVID-19_Ag_detection_kit", "result": "Negative", "sample_collection_date": "2020-10-14", "result_date": "2020-10-15", "patient_created": false}}, {"model": "facility.patientexternaltest", "pk": 16, "fields": {"external_id": "69347901-fee6-4bde-8a87-e06494121764", "created_date": "2024-02-01T14:01:21.224Z", "modified_date": "2024-02-01T14:01:21.224Z", "deleted": false, "srf_id": "00/EKM/0015", "name": "Patient 16", "age": 24, "age_in": "years", "gender": "m", "address": "CSN HQ\nKochi, Kerala", "mobile_number": "**********", "is_repeat": false, "patient_status": "Asymptomatic", "ward": 15100, "local_body": 704, "district": 7, "source": "Secondary contact aparna", "patient_category": "Cat 17: All individuals who wish to get themselves tested", "lab_name": "Karothukuzhi Laboratory", "test_type": "Antigen", "sample_type": "Ag-SD_Biosensor_Standard_Q_COVID-19_Ag_detection_kit", "result": "Negative", "sample_collection_date": "2020-10-14", "result_date": "2020-10-15", "patient_created": false}}, {"model": "facility.patientexternaltest", "pk": 17, "fields": {"external_id": "a42c14db-a584-48d3-b437-806b89b2d2a6", "created_date": "2024-02-01T14:01:21.228Z", "modified_date": "2024-02-01T14:01:21.228Z", "deleted": false, "srf_id": "00/EKM/0016", "name": "Patient 17", "age": 24, "age_in": "years", "gender": "m", "address": "CSN HQ\nKochi, Kerala", "mobile_number": "**********", "is_repeat": false, "patient_status": "Asymptomatic", "ward": 15100, "local_body": 704, "district": 7, "source": "Secondary contact aparna", "patient_category": "Cat 17: All individuals who wish to get themselves tested", "lab_name": "Karothukuzhi Laboratory", "test_type": "Antigen", "sample_type": "Ag-SD_Biosensor_Standard_Q_COVID-19_Ag_detection_kit", "result": "Negative", "sample_collection_date": "2020-10-14", "result_date": "2020-10-15", "patient_created": false}}, {"model": "facility.patientexternaltest", "pk": 18, "fields": {"external_id": "df690fe4-e96f-4448-a5a5-9ea9ccb6ef69", "created_date": "2024-02-01T14:01:21.233Z", "modified_date": "2024-02-01T14:01:21.233Z", "deleted": false, "srf_id": "00/EKM/0017", "name": "Patient 18", "age": 24, "age_in": "years", "gender": "m", "address": "CSN HQ\nKochi, Kerala", "mobile_number": "**********", "is_repeat": false, "patient_status": "Asymptomatic", "ward": 15100, "local_body": 704, "district": 7, "source": "Secondary contact aparna", "patient_category": "Cat 17: All individuals who wish to get themselves tested", "lab_name": "Karothukuzhi Laboratory", "test_type": "Antigen", "sample_type": "Ag-SD_Biosensor_Standard_Q_COVID-19_Ag_detection_kit", "result": "Negative", "sample_collection_date": "2020-10-14", "result_date": "2020-10-15", "patient_created": false}}, {"model": "facility.patientexternaltest", "pk": 19, "fields": {"external_id": "736cf9ed-bb76-4b36-996b-8c2c04c248d3", "created_date": "2024-02-01T14:01:21.237Z", "modified_date": "2024-02-01T14:01:21.237Z", "deleted": false, "srf_id": "00/EKM/0018", "name": "Patient 19", "age": 24, "age_in": "years", "gender": "m", "address": "CSN HQ\nKochi, Kerala", "mobile_number": "**********", "is_repeat": false, "patient_status": "Asymptomatic", "ward": 15100, "local_body": 704, "district": 7, "source": "Secondary contact aparna", "patient_category": "Cat 17: All individuals who wish to get themselves tested", "lab_name": "Karothukuzhi Laboratory", "test_type": "Antigen", "sample_type": "Ag-SD_Biosensor_Standard_Q_COVID-19_Ag_detection_kit", "result": "Negative", "sample_collection_date": "2020-10-14", "result_date": "2020-10-15", "patient_created": false}}, {"model": "facility.patientexternaltest", "pk": 20, "fields": {"external_id": "47a5306a-1a63-40da-a2eb-0c11ea2a9203", "created_date": "2024-02-01T14:01:21.242Z", "modified_date": "2024-02-01T14:01:21.242Z", "deleted": false, "srf_id": "00/EKM/0019", "name": "Patient 20", "age": 24, "age_in": "years", "gender": "m", "address": "CSN HQ\nKochi, Kerala", "mobile_number": "**********", "is_repeat": false, "patient_status": "Asymptomatic", "ward": 15100, "local_body": 704, "district": 7, "source": "Secondary contact aparna", "patient_category": "Cat 17: All individuals who wish to get themselves tested", "lab_name": "Karothukuzhi Laboratory", "test_type": "Antigen", "sample_type": "Ag-SD_Biosensor_Standard_Q_COVID-19_Ag_detection_kit", "result": "Negative", "sample_collection_date": "2020-10-14", "result_date": "2020-10-15", "patient_created": false}}, {"model": "facility.patientsample", "pk": 1, "fields": {"external_id": "29689b96-6018-426f-984f-344fa5e3186b", "created_date": "2022-09-27T07:23:29.568Z", "modified_date": "2022-09-27T07:23:29.574Z", "deleted": false, "patient": 1, "consultation": 1, "sample_type": 1, "sample_type_other": "", "has_sari": false, "has_ari": false, "doctor_name": "NO DOCTOR SPECIFIED", "diagnosis": "", "diff_diagnosis": "", "etiology_identified": "", "is_atypical_presentation": false, "atypical_presentation": "", "is_unusual_course": false, "icmr_category": 10, "icmr_label": "Test", "status": 1, "result": 3, "fast_track": "", "date_of_sample": "2022-09-27T07:23:29.508Z", "date_of_result": null, "testing_facility": 1, "created_by": 2, "last_edited_by": 2}}, {"model": "facility.patientsample", "pk": 2, "fields": {"external_id": "ea48179e-90c5-450b-800d-c1866d2d3d74", "created_date": "2022-09-27T07:25:39.605Z", "modified_date": "2022-09-27T07:25:39.609Z", "deleted": false, "patient": 1, "consultation": 1, "sample_type": 1, "sample_type_other": "", "has_sari": false, "has_ari": false, "doctor_name": "NO DOCTOR SPECIFIED", "diagnosis": "", "diff_diagnosis": "", "etiology_identified": "", "is_atypical_presentation": false, "atypical_presentation": "", "is_unusual_course": false, "icmr_category": 10, "icmr_label": "Test", "status": 1, "result": 3, "fast_track": "", "date_of_sample": "2022-09-27T07:23:29.508Z", "date_of_result": null, "testing_facility": 1, "created_by": 2, "last_edited_by": 2}}, {"model": "facility.patientsample", "pk": 3, "fields": {"external_id": "dcaf07f9-8412-45bb-bb7c-1c232c28e9da", "created_date": "2022-09-27T07:25:41.681Z", "modified_date": "2022-09-27T07:25:41.685Z", "deleted": false, "patient": 1, "consultation": 1, "sample_type": 1, "sample_type_other": "", "has_sari": false, "has_ari": false, "doctor_name": "NO DOCTOR SPECIFIED", "diagnosis": "", "diff_diagnosis": "", "etiology_identified": "", "is_atypical_presentation": false, "atypical_presentation": "", "is_unusual_course": false, "icmr_category": 10, "icmr_label": "Test", "status": 1, "result": 3, "fast_track": "", "date_of_sample": "2022-09-27T07:23:29.508Z", "date_of_result": null, "testing_facility": 1, "created_by": 2, "last_edited_by": 2}}, {"model": "facility.patientsample", "pk": 4, "fields": {"external_id": "80868c78-4917-4401-b4b4-9eea49c47493", "created_date": "2022-09-27T07:25:42.673Z", "modified_date": "2022-09-27T07:25:42.676Z", "deleted": false, "patient": 1, "consultation": 1, "sample_type": 1, "sample_type_other": "", "has_sari": false, "has_ari": false, "doctor_name": "NO DOCTOR SPECIFIED", "diagnosis": "", "diff_diagnosis": "", "etiology_identified": "", "is_atypical_presentation": false, "atypical_presentation": "", "is_unusual_course": false, "icmr_category": 10, "icmr_label": "Test", "status": 1, "result": 3, "fast_track": "", "date_of_sample": "2022-09-27T07:23:29.508Z", "date_of_result": null, "testing_facility": 1, "created_by": 2, "last_edited_by": 2}}, {"model": "facility.patientsample", "pk": 5, "fields": {"external_id": "093863c4-1778-4464-9ee9-c408bef0bafe", "created_date": "2022-09-27T07:25:43.611Z", "modified_date": "2022-09-27T07:25:43.615Z", "deleted": false, "patient": 1, "consultation": 1, "sample_type": 1, "sample_type_other": "", "has_sari": false, "has_ari": false, "doctor_name": "NO DOCTOR SPECIFIED", "diagnosis": "", "diff_diagnosis": "", "etiology_identified": "", "is_atypical_presentation": false, "atypical_presentation": "", "is_unusual_course": false, "icmr_category": 10, "icmr_label": "Test", "status": 1, "result": 3, "fast_track": "", "date_of_sample": "2022-09-27T07:23:29.508Z", "date_of_result": null, "testing_facility": 1, "created_by": 2, "last_edited_by": 2}}, {"model": "facility.patientsample", "pk": 6, "fields": {"external_id": "5b015be7-ffb3-4f36-a08a-7510dd53ee7f", "created_date": "2022-09-27T07:25:44.707Z", "modified_date": "2022-09-27T07:25:44.711Z", "deleted": false, "patient": 1, "consultation": 1, "sample_type": 1, "sample_type_other": "", "has_sari": false, "has_ari": false, "doctor_name": "NO DOCTOR SPECIFIED", "diagnosis": "", "diff_diagnosis": "", "etiology_identified": "", "is_atypical_presentation": false, "atypical_presentation": "", "is_unusual_course": false, "icmr_category": 10, "icmr_label": "Test", "status": 1, "result": 3, "fast_track": "", "date_of_sample": "2022-09-27T07:23:29.508Z", "date_of_result": null, "testing_facility": 1, "created_by": 2, "last_edited_by": 2}}, {"model": "facility.patientsample", "pk": 7, "fields": {"external_id": "b8cb7a31-5480-4466-8388-de1eafa35ede", "created_date": "2022-09-27T07:25:52.375Z", "modified_date": "2022-09-27T07:25:52.379Z", "deleted": false, "patient": 1, "consultation": 1, "sample_type": 1, "sample_type_other": "", "has_sari": false, "has_ari": false, "doctor_name": "NO DOCTOR SPECIFIED", "diagnosis": "", "diff_diagnosis": "", "etiology_identified": "", "is_atypical_presentation": false, "atypical_presentation": "", "is_unusual_course": false, "icmr_category": 10, "icmr_label": "Test", "status": 1, "result": 3, "fast_track": "", "date_of_sample": "2022-09-27T07:23:29.508Z", "date_of_result": null, "testing_facility": 1, "created_by": 2, "last_edited_by": 2}}, {"model": "facility.patientsample", "pk": 8, "fields": {"external_id": "c04f9bd2-e11f-466e-8649-d80f9e5d8649", "created_date": "2022-09-27T07:25:53.556Z", "modified_date": "2022-09-27T07:25:53.559Z", "deleted": false, "patient": 1, "consultation": 1, "sample_type": 1, "sample_type_other": "", "has_sari": false, "has_ari": false, "doctor_name": "NO DOCTOR SPECIFIED", "diagnosis": "", "diff_diagnosis": "", "etiology_identified": "", "is_atypical_presentation": false, "atypical_presentation": "", "is_unusual_course": false, "icmr_category": 10, "icmr_label": "Test", "status": 1, "result": 3, "fast_track": "", "date_of_sample": "2022-09-27T07:23:29.508Z", "date_of_result": null, "testing_facility": 1, "created_by": 2, "last_edited_by": 2}}, {"model": "facility.patientsample", "pk": 9, "fields": {"external_id": "27001f33-e193-42d5-8809-61b2c63304a9", "created_date": "2022-09-27T07:25:54.406Z", "modified_date": "2022-09-27T07:25:54.410Z", "deleted": false, "patient": 1, "consultation": 1, "sample_type": 1, "sample_type_other": "", "has_sari": false, "has_ari": false, "doctor_name": "NO DOCTOR SPECIFIED", "diagnosis": "", "diff_diagnosis": "", "etiology_identified": "", "is_atypical_presentation": false, "atypical_presentation": "", "is_unusual_course": false, "icmr_category": 10, "icmr_label": "Test", "status": 1, "result": 3, "fast_track": "", "date_of_sample": "2022-09-27T07:23:29.508Z", "date_of_result": null, "testing_facility": 1, "created_by": 2, "last_edited_by": 2}}, {"model": "facility.patientsample", "pk": 10, "fields": {"external_id": "1a604325-11bf-44b7-985b-6222b6c37092", "created_date": "2022-09-27T07:25:55.280Z", "modified_date": "2022-09-27T07:25:55.284Z", "deleted": false, "patient": 1, "consultation": 1, "sample_type": 1, "sample_type_other": "", "has_sari": false, "has_ari": false, "doctor_name": "NO DOCTOR SPECIFIED", "diagnosis": "", "diff_diagnosis": "", "etiology_identified": "", "is_atypical_presentation": false, "atypical_presentation": "", "is_unusual_course": false, "icmr_category": 10, "icmr_label": "Test", "status": 1, "result": 3, "fast_track": "", "date_of_sample": "2022-09-27T07:23:29.508Z", "date_of_result": null, "testing_facility": 1, "created_by": 2, "last_edited_by": 2}}, {"model": "facility.patientsample", "pk": 11, "fields": {"external_id": "0bd4f6ff-e084-4d2c-9803-79b829682a1d", "created_date": "2022-09-27T07:25:56.119Z", "modified_date": "2022-09-27T07:25:56.123Z", "deleted": false, "patient": 1, "consultation": 1, "sample_type": 1, "sample_type_other": "", "has_sari": false, "has_ari": false, "doctor_name": "NO DOCTOR SPECIFIED", "diagnosis": "", "diff_diagnosis": "", "etiology_identified": "", "is_atypical_presentation": false, "atypical_presentation": "", "is_unusual_course": false, "icmr_category": 10, "icmr_label": "Test", "status": 1, "result": 3, "fast_track": "", "date_of_sample": "2022-09-27T07:23:29.508Z", "date_of_result": null, "testing_facility": 1, "created_by": 2, "last_edited_by": 2}}, {"model": "facility.patientsample", "pk": 12, "fields": {"external_id": "fecb4ef3-2889-47ca-8e08-6a27a5e77715", "created_date": "2022-09-27T07:25:56.959Z", "modified_date": "2022-09-27T07:25:56.964Z", "deleted": false, "patient": 1, "consultation": 1, "sample_type": 1, "sample_type_other": "", "has_sari": false, "has_ari": false, "doctor_name": "NO DOCTOR SPECIFIED", "diagnosis": "", "diff_diagnosis": "", "etiology_identified": "", "is_atypical_presentation": false, "atypical_presentation": "", "is_unusual_course": false, "icmr_category": 10, "icmr_label": "Test", "status": 1, "result": 3, "fast_track": "", "date_of_sample": "2022-09-27T07:23:29.508Z", "date_of_result": null, "testing_facility": 1, "created_by": 2, "last_edited_by": 2}}, {"model": "facility.patientsample", "pk": 13, "fields": {"external_id": "45186f6d-1ed5-454a-aadd-94ceaefacf20", "created_date": "2022-09-27T07:25:57.875Z", "modified_date": "2022-09-27T07:25:57.878Z", "deleted": false, "patient": 1, "consultation": 1, "sample_type": 1, "sample_type_other": "", "has_sari": false, "has_ari": false, "doctor_name": "NO DOCTOR SPECIFIED", "diagnosis": "", "diff_diagnosis": "", "etiology_identified": "", "is_atypical_presentation": false, "atypical_presentation": "", "is_unusual_course": false, "icmr_category": 10, "icmr_label": "Test", "status": 1, "result": 3, "fast_track": "", "date_of_sample": "2022-09-27T07:23:29.508Z", "date_of_result": null, "testing_facility": 1, "created_by": 2, "last_edited_by": 2}}, {"model": "facility.patientsample", "pk": 14, "fields": {"external_id": "bcbc2e7e-d2b8-4e5e-ab50-7c36a51c1e09", "created_date": "2022-09-27T07:25:58.676Z", "modified_date": "2022-09-27T07:25:58.681Z", "deleted": false, "patient": 1, "consultation": 1, "sample_type": 1, "sample_type_other": "", "has_sari": false, "has_ari": false, "doctor_name": "NO DOCTOR SPECIFIED", "diagnosis": "", "diff_diagnosis": "", "etiology_identified": "", "is_atypical_presentation": false, "atypical_presentation": "", "is_unusual_course": false, "icmr_category": 10, "icmr_label": "Test", "status": 1, "result": 3, "fast_track": "", "date_of_sample": "2022-09-27T07:23:29.508Z", "date_of_result": null, "testing_facility": 1, "created_by": 2, "last_edited_by": 2}}, {"model": "facility.patientsample", "pk": 15, "fields": {"external_id": "342639b4-f347-4f83-87d1-85c6d7968ee7", "created_date": "2022-09-27T07:25:59.671Z", "modified_date": "2022-09-27T07:25:59.674Z", "deleted": false, "patient": 1, "consultation": 1, "sample_type": 1, "sample_type_other": "", "has_sari": false, "has_ari": false, "doctor_name": "NO DOCTOR SPECIFIED", "diagnosis": "", "diff_diagnosis": "", "etiology_identified": "", "is_atypical_presentation": false, "atypical_presentation": "", "is_unusual_course": false, "icmr_category": 10, "icmr_label": "Test", "status": 1, "result": 3, "fast_track": "", "date_of_sample": "2022-09-27T07:23:29.508Z", "date_of_result": null, "testing_facility": 1, "created_by": 2, "last_edited_by": 2}}, {"model": "facility.patientsample", "pk": 16, "fields": {"external_id": "388099f9-f005-47c1-9be3-650ddda769a0", "created_date": "2022-09-27T07:26:00.354Z", "modified_date": "2022-09-27T07:26:00.358Z", "deleted": false, "patient": 1, "consultation": 1, "sample_type": 1, "sample_type_other": "", "has_sari": false, "has_ari": false, "doctor_name": "NO DOCTOR SPECIFIED", "diagnosis": "", "diff_diagnosis": "", "etiology_identified": "", "is_atypical_presentation": false, "atypical_presentation": "", "is_unusual_course": false, "icmr_category": 10, "icmr_label": "Test", "status": 1, "result": 3, "fast_track": "", "date_of_sample": "2022-09-27T07:23:29.508Z", "date_of_result": null, "testing_facility": 1, "created_by": 2, "last_edited_by": 2}}, {"model": "facility.patientsample", "pk": 17, "fields": {"external_id": "5aef7a72-5e2c-4a0f-b689-747aae8b0dce", "created_date": "2022-09-27T07:26:00.860Z", "modified_date": "2022-09-27T07:26:00.863Z", "deleted": false, "patient": 1, "consultation": 1, "sample_type": 1, "sample_type_other": "", "has_sari": false, "has_ari": false, "doctor_name": "NO DOCTOR SPECIFIED", "diagnosis": "", "diff_diagnosis": "", "etiology_identified": "", "is_atypical_presentation": false, "atypical_presentation": "", "is_unusual_course": false, "icmr_category": 10, "icmr_label": "Test", "status": 1, "result": 3, "fast_track": "", "date_of_sample": "2022-09-27T07:23:29.508Z", "date_of_result": null, "testing_facility": 1, "created_by": 2, "last_edited_by": 2}}, {"model": "facility.patientsampleflow", "pk": 1, "fields": {"external_id": "64bf6eb2-055d-4a16-8c91-e7ce1390e541", "created_date": "2022-09-27T07:23:29.580Z", "modified_date": "2022-09-27T07:23:29.580Z", "deleted": false, "patient_sample": 1, "status": 1, "notes": "created", "created_by": 2}}, {"model": "facility.patientsampleflow", "pk": 2, "fields": {"external_id": "1f68131b-9aab-456f-b61b-eb0a93d521c4", "created_date": "2022-09-27T07:25:39.613Z", "modified_date": "2022-09-27T07:25:39.613Z", "deleted": false, "patient_sample": 2, "status": 1, "notes": "created", "created_by": 2}}, {"model": "facility.patientsampleflow", "pk": 3, "fields": {"external_id": "80ead199-cd58-44c3-97de-4142e3e7d7ac", "created_date": "2022-09-27T07:25:41.689Z", "modified_date": "2022-09-27T07:25:41.689Z", "deleted": false, "patient_sample": 3, "status": 1, "notes": "created", "created_by": 2}}, {"model": "facility.patientsampleflow", "pk": 4, "fields": {"external_id": "ebe8ffac-1ca0-40fe-80d3-b895e7524ce2", "created_date": "2022-09-27T07:25:42.681Z", "modified_date": "2022-09-27T07:25:42.681Z", "deleted": false, "patient_sample": 4, "status": 1, "notes": "created", "created_by": 2}}, {"model": "facility.patientsampleflow", "pk": 5, "fields": {"external_id": "ed7f8727-3784-40cd-97c6-49e9de3e2fdb", "created_date": "2022-09-27T07:25:43.620Z", "modified_date": "2022-09-27T07:25:43.620Z", "deleted": false, "patient_sample": 5, "status": 1, "notes": "created", "created_by": 2}}, {"model": "facility.patientsampleflow", "pk": 6, "fields": {"external_id": "f7a3695f-bfd4-4af5-9280-e2d54124286e", "created_date": "2022-09-27T07:25:44.716Z", "modified_date": "2022-09-27T07:25:44.716Z", "deleted": false, "patient_sample": 6, "status": 1, "notes": "created", "created_by": 2}}, {"model": "facility.patientsampleflow", "pk": 7, "fields": {"external_id": "a0cad511-4dad-4244-93c4-50277d6734c2", "created_date": "2022-09-27T07:25:52.383Z", "modified_date": "2022-09-27T07:25:52.383Z", "deleted": false, "patient_sample": 7, "status": 1, "notes": "created", "created_by": 2}}, {"model": "facility.patientsampleflow", "pk": 8, "fields": {"external_id": "f3ff09b9-1c79-4afc-bd3d-70eeecc87daa", "created_date": "2022-09-27T07:25:53.564Z", "modified_date": "2022-09-27T07:25:53.564Z", "deleted": false, "patient_sample": 8, "status": 1, "notes": "created", "created_by": 2}}, {"model": "facility.patientsampleflow", "pk": 9, "fields": {"external_id": "a03684c4-4769-4643-b457-8be382895e8b", "created_date": "2022-09-27T07:25:54.414Z", "modified_date": "2022-09-27T07:25:54.414Z", "deleted": false, "patient_sample": 9, "status": 1, "notes": "created", "created_by": 2}}, {"model": "facility.patientsampleflow", "pk": 10, "fields": {"external_id": "a62f7677-31af-42f6-94f9-fcb5e8ab94de", "created_date": "2022-09-27T07:25:55.288Z", "modified_date": "2022-09-27T07:25:55.288Z", "deleted": false, "patient_sample": 10, "status": 1, "notes": "created", "created_by": 2}}, {"model": "facility.patientsampleflow", "pk": 11, "fields": {"external_id": "e9dae65e-bca7-47fc-b490-8ef4de67e331", "created_date": "2022-09-27T07:25:56.127Z", "modified_date": "2022-09-27T07:25:56.127Z", "deleted": false, "patient_sample": 11, "status": 1, "notes": "created", "created_by": 2}}, {"model": "facility.patientsampleflow", "pk": 12, "fields": {"external_id": "e065bb9a-10d2-4bdd-b375-affa163dacdf", "created_date": "2022-09-27T07:25:56.968Z", "modified_date": "2022-09-27T07:25:56.968Z", "deleted": false, "patient_sample": 12, "status": 1, "notes": "created", "created_by": 2}}, {"model": "facility.patientsampleflow", "pk": 13, "fields": {"external_id": "dae215d0-bbfe-41e5-a6e2-20e4504eb068", "created_date": "2022-09-27T07:25:57.883Z", "modified_date": "2022-09-27T07:25:57.883Z", "deleted": false, "patient_sample": 13, "status": 1, "notes": "created", "created_by": 2}}, {"model": "facility.patientsampleflow", "pk": 14, "fields": {"external_id": "dabdd193-f800-4848-902b-b33b345252e5", "created_date": "2022-09-27T07:25:58.686Z", "modified_date": "2022-09-27T07:25:58.686Z", "deleted": false, "patient_sample": 14, "status": 1, "notes": "created", "created_by": 2}}, {"model": "facility.patientsampleflow", "pk": 15, "fields": {"external_id": "8a32a9f0-0686-4104-8fe7-27c7be6a4233", "created_date": "2022-09-27T07:25:59.679Z", "modified_date": "2022-09-27T07:25:59.679Z", "deleted": false, "patient_sample": 15, "status": 1, "notes": "created", "created_by": 2}}, {"model": "facility.patientsampleflow", "pk": 16, "fields": {"external_id": "db4ceed2-a7e5-4cb1-828b-085ddae5fa0a", "created_date": "2022-09-27T07:26:00.363Z", "modified_date": "2022-09-27T07:26:00.363Z", "deleted": false, "patient_sample": 16, "status": 1, "notes": "created", "created_by": 2}}, {"model": "facility.patientsampleflow", "pk": 17, "fields": {"external_id": "48b8eb3a-c901-429a-abbb-7e5aae0f9cd2", "created_date": "2022-09-27T07:26:00.867Z", "modified_date": "2022-09-27T07:26:00.867Z", "deleted": false, "patient_sample": 17, "status": 1, "notes": "created", "created_by": 2}}, {"model": "facility.shiftingrequest", "pk": 1, "fields": {"external_id": "a0e4cf70-49b4-4e26-83fa-c2c962386885", "created_date": "2022-09-27T07:22:00.581Z", "modified_date": "2023-12-06T08:37:17.082Z", "deleted": false, "origin_facility": 1, "shifting_approving_facility": 2, "assigned_facility_type": 2, "assigned_facility": null, "assigned_facility_external": null, "patient": 1, "emergency": true, "is_up_shift": true, "reason": "Test", "vehicle_preference": "", "preferred_vehicle_choice": 10, "comments": "", "refering_facility_contact_name": "Someone at Facility", "refering_facility_contact_number": "+914455666777", "is_kasp": false, "status": 100, "breathlessness_level": 30, "is_assigned_to_user": false, "assigned_to": null, "ambulance_driver_name": "", "ambulance_phone_number": "", "ambulance_number": "", "created_by": 2, "last_edited_by": 2}}, {"model": "facility.shiftingrequest", "pk": 2, "fields": {"external_id": "b1e6df81-35a2-4dc2-89cf-1b78a968e456", "created_date": "2023-05-12T09:18:40.123Z", "modified_date": "2023-11-20T14:55:30.987Z", "deleted": false, "origin_facility": 3, "shifting_approving_facility": 4, "assigned_facility_type": 1, "assigned_facility": null, "assigned_facility_external": null, "patient": 2, "emergency": false, "is_up_shift": false, "reason": "Scheduled transfer for further treatment", "vehicle_preference": "Car", "preferred_vehicle_choice": 40, "comments": "No special instructions", "refering_facility_contact_name": "<PERSON>", "refering_facility_contact_number": "+**********", "is_kasp": true, "status": 20, "breathlessness_level": 15, "is_assigned_to_user": true, "assigned_to": 3, "ambulance_driver_name": "", "ambulance_phone_number": "", "ambulance_number": "", "created_by": 4, "last_edited_by": 3}}, {"model": "facility.shiftingrequest", "pk": 3, "fields": {"external_id": "c2d8ab92-15e3-4f67-8d36-9a5175f83467", "created_date": "2023-07-18T15:40:20.789Z", "modified_date": "2023-10-30T11:25:55.321Z", "deleted": false, "origin_facility": 2, "shifting_approving_facility": 1, "assigned_facility_type": 3, "assigned_facility": null, "assigned_facility_external": null, "patient": 3, "emergency": true, "is_up_shift": true, "reason": "Patient needs immediate ICU admission", "vehicle_preference": "All double chambered Ambulance with EMT", "preferred_vehicle_choice": 20, "comments": "", "refering_facility_contact_name": "<PERSON>", "refering_facility_contact_number": "+**********", "is_kasp": false, "status": 40, "breathlessness_level": 40, "is_assigned_to_user": false, "assigned_to": null, "ambulance_driver_name": "<PERSON>", "ambulance_phone_number": "+**********", "ambulance_number": "", "created_by": 1, "last_edited_by": 1}}, {"model": "facility.shiftingrequest", "pk": 4, "fields": {"external_id": "d3e9bcf3-6a7f-4c7e-ba5f-5c1f98535d82", "created_date": "2023-08-24T13:55:10.422Z", "modified_date": "2023-11-15T16:30:45.123Z", "deleted": false, "origin_facility": 4, "shifting_approving_facility": 3, "assigned_facility_type": null, "assigned_facility": null, "assigned_facility_external": null, "patient": 4, "emergency": false, "is_up_shift": true, "reason": "Patient needs to be shifted to a specialized facility", "vehicle_preference": "Auto-rickshaw", "preferred_vehicle_choice": 50, "comments": "", "refering_facility_contact_name": "<PERSON>", "refering_facility_contact_number": "+**********", "is_kasp": true, "status": 80, "breathlessness_level": 20, "is_assigned_to_user": true, "assigned_to": 2, "ambulance_driver_name": "<PERSON>", "ambulance_phone_number": "+**********", "ambulance_number": "", "created_by": 3, "last_edited_by": 4}}, {"model": "facility.shiftingrequest", "pk": 5, "fields": {"external_id": "e4f5cd6e-8b0d-4b68-86d3-2f3f51c4af90", "created_date": "2023-10-01T09:30:45.654Z", "modified_date": "2023-12-05T14:20:30.987Z", "deleted": false, "origin_facility": 1, "shifting_approving_facility": null, "assigned_facility_type": 2, "assigned_facility": null, "assigned_facility_external": null, "patient": 5, "emergency": true, "is_up_shift": false, "reason": "Patient requires transfer for specialized surgery", "vehicle_preference": "", "preferred_vehicle_choice": null, "comments": "Urgent transfer needed", "refering_facility_contact_name": "<PERSON>", "refering_facility_contact_number": "+**********", "is_kasp": false, "status": 15, "breathlessness_level": null, "is_assigned_to_user": false, "assigned_to": null, "ambulance_driver_name": "", "ambulance_phone_number": "", "ambulance_number": "", "created_by": 1, "last_edited_by": 2}}, {"model": "facility.shiftingrequest", "pk": 6, "fields": {"external_id": "550e8400-e29b-41d4-a716-446655440000", "created_date": "2023-11-15T10:20:30.123Z", "modified_date": "2023-12-06T08:00:15.789Z", "deleted": false, "origin_facility": 3, "shifting_approving_facility": 1, "assigned_facility_type": 1, "assigned_facility": null, "assigned_facility_external": null, "patient": 6, "emergency": false, "is_up_shift": false, "reason": "Scheduled transfer for follow-up treatment", "vehicle_preference": "D Level Ambulance", "preferred_vehicle_choice": 10, "comments": "", "refering_facility_contact_name": "<PERSON>", "refering_facility_contact_number": "+**********", "is_kasp": true, "status": 70, "breathlessness_level": 30, "is_assigned_to_user": false, "assigned_to": null, "ambulance_driver_name": "<PERSON>", "ambulance_phone_number": "+**********", "ambulance_number": "", "created_by": 4, "last_edited_by": 3}}, {"model": "facility.shiftingrequest", "pk": 7, "fields": {"external_id": "7c9e6679-7425-40de-944b-e07fc1f90ae7", "created_date": "2023-12-06T08:30:45.987Z", "modified_date": "2023-12-06T08:30:45.987Z", "deleted": false, "origin_facility": 2, "shifting_approving_facility": null, "assigned_facility_type": null, "assigned_facility": null, "assigned_facility_external": null, "patient": 7, "emergency": true, "is_up_shift": true, "reason": "Urgent transfer for advanced treatment", "vehicle_preference": "Car", "preferred_vehicle_choice": 40, "comments": "", "refering_facility_contact_name": "<PERSON>", "refering_facility_contact_number": "+**********", "is_kasp": false, "status": 60, "breathlessness_level": 15, "is_assigned_to_user": false, "assigned_to": null, "ambulance_driver_name": "", "ambulance_phone_number": "", "ambulance_number": "", "created_by": 2, "last_edited_by": 1}}, {"model": "facility.shiftingrequest", "pk": 8, "fields": {"external_id": "123e4567-e89b-12d3-a456-426614174000", "created_date": "2023-12-06T08:47:55.705Z", "modified_date": "2023-12-06T08:47:55.705Z", "deleted": false, "origin_facility": 4, "shifting_approving_facility": 3, "assigned_facility_type": 3, "assigned_facility": null, "assigned_facility_external": null, "patient": 8, "emergency": false, "is_up_shift": true, "reason": "Patient needs specialized care", "vehicle_preference": "", "preferred_vehicle_choice": null, "comments": "Patient requires specific equipment", "refering_facility_contact_name": "<PERSON>", "refering_facility_contact_number": "+**********", "is_kasp": true, "status": 100, "breathlessness_level": 40, "is_assigned_to_user": false, "assigned_to": null, "ambulance_driver_name": "", "ambulance_phone_number": "", "ambulance_number": "", "created_by": 3, "last_edited_by": 4}}, {"model": "facility.shiftingrequest", "pk": 9, "fields": {"external_id": "09876543-210e-4567-a890-**********ab", "created_date": "2023-12-06T09:00:00Z", "modified_date": "2023-12-06T09:00:00Z", "deleted": false, "origin_facility": 1, "shifting_approving_facility": 2, "assigned_facility_type": 2, "assigned_facility": null, "assigned_facility_external": null, "patient": 9, "emergency": true, "is_up_shift": false, "reason": "Patient needs immediate attention", "vehicle_preference": "Ambulance without EMT", "preferred_vehicle_choice": 30, "comments": "Critical condition, prioritize", "refering_facility_contact_name": "<PERSON>", "refering_facility_contact_number": "+**********", "is_kasp": false, "status": 20, "breathlessness_level": 20, "is_assigned_to_user": true, "assigned_to": 1, "ambulance_driver_name": "<PERSON>", "ambulance_phone_number": "+**********", "ambulance_number": "", "created_by": 2, "last_edited_by": 1}}, {"model": "facility.shiftingrequest", "pk": 11, "fields": {"external_id": "abcdef01-2345-6789-abcd-ef0123456789", "created_date": "2023-08-19T16:40:55.743Z", "modified_date": "2023-11-30T10:20:05.214Z", "deleted": false, "origin_facility": 1, "shifting_approving_facility": 2, "assigned_facility_type": 2, "assigned_facility": null, "assigned_facility_external": null, "patient": 11, "emergency": true, "is_up_shift": false, "reason": "Patient needs immediate care", "vehicle_preference": "Ambulance without EMT", "preferred_vehicle_choice": 30, "comments": "Contact family members", "refering_facility_contact_name": "<PERSON>", "refering_facility_contact_number": "+**********", "is_kasp": true, "status": 20, "breathlessness_level": 20, "is_assigned_to_user": true, "assigned_to": 1, "ambulance_driver_name": "<PERSON>", "ambulance_phone_number": "+**********", "ambulance_number": "AMB1122", "created_by": 2, "last_edited_by": 1}}, {"model": "facility.shiftingrequest", "pk": 12, "fields": {"external_id": "a0b1c2d3-e4f5-6789-abcd-0123456789ab", "created_date": "2023-04-15T14:30:20.990Z", "modified_date": "2023-10-25T08:55:15.789Z", "deleted": false, "origin_facility": 2, "shifting_approving_facility": null, "assigned_facility_type": null, "assigned_facility": null, "assigned_facility_external": null, "patient": 12, "emergency": false, "is_up_shift": true, "reason": "Patient needs specialized care", "vehicle_preference": "Car", "preferred_vehicle_choice": 40, "comments": "Coordinate with receiving facility", "refering_facility_contact_name": "<PERSON>", "refering_facility_contact_number": "+**********", "is_kasp": false, "status": 60, "breathlessness_level": 15, "is_assigned_to_user": false, "assigned_to": null, "ambulance_driver_name": "", "ambulance_phone_number": "", "ambulance_number": "", "created_by": 1, "last_edited_by": 2}}, {"model": "facility.shiftingrequest", "pk": 13, "fields": {"external_id": "e3a2dc83-82e1-422a-9c4f-86b4c1f0c2d3", "created_date": "2023-11-10T11:05:45.372Z", "modified_date": "2023-12-03T16:25:35.948Z", "deleted": false, "origin_facility": 3, "shifting_approving_facility": 1, "assigned_facility_type": 1, "assigned_facility": null, "assigned_facility_external": null, "patient": 13, "emergency": true, "is_up_shift": false, "reason": "Patient requires transfer for specialized surgery", "vehicle_preference": "Auto-rickshaw", "preferred_vehicle_choice": 50, "comments": "Ensure medical records are transferred", "refering_facility_contact_name": "<PERSON>", "refering_facility_contact_number": "+**********", "is_kasp": true, "status": 15, "breathlessness_level": null, "is_assigned_to_user": false, "assigned_to": null, "ambulance_driver_name": "", "ambulance_phone_number": "", "ambulance_number": "", "created_by": 2, "last_edited_by": 1}}, {"model": "facility.shiftingrequest", "pk": 14, "fields": {"external_id": "f0e1d2c3-b4a5-6789-cdef-0123456789ab", "created_date": "2023-06-20T18:15:30.125Z", "modified_date": "2023-12-02T09:40:55.801Z", "deleted": false, "origin_facility": 4, "shifting_approving_facility": 3, "assigned_facility_type": 3, "assigned_facility": null, "assigned_facility_external": null, "patient": 14, "emergency": false, "is_up_shift": true, "reason": "Scheduled transfer for follow-up treatment", "vehicle_preference": "D Level Ambulance", "preferred_vehicle_choice": 10, "comments": "Provide discharge summary", "refering_facility_contact_name": "<PERSON>", "refering_facility_contact_number": "+**********", "is_kasp": true, "status": 70, "breathlessness_level": 30, "is_assigned_to_user": false, "assigned_to": null, "ambulance_driver_name": "<PERSON>", "ambulance_phone_number": "+**********", "ambulance_number": "", "created_by": 4, "last_edited_by": 3}}, {"model": "facility.shiftingrequest", "pk": 15, "fields": {"external_id": "98765432-10e2-40f1-a0b9-876543210abc", "created_date": "2023-09-05T22:50:10.221Z", "modified_date": "2023-12-04T14:30:45.501Z", "deleted": false, "origin_facility": 1, "shifting_approving_facility": 2, "assigned_facility_type": 2, "assigned_facility": null, "assigned_facility_external": null, "patient": 15, "emergency": true, "is_up_shift": true, "reason": "Test", "vehicle_preference": "", "preferred_vehicle_choice": 10, "comments": "", "refering_facility_contact_name": "Someone at Facility", "refering_facility_contact_number": "+914455666777", "is_kasp": false, "status": 100, "breathlessness_level": 30, "is_assigned_to_user": false, "assigned_to": null, "ambulance_driver_name": "", "ambulance_phone_number": "", "ambulance_number": "", "created_by": 2, "last_edited_by": 2}}, {"model": "facility.resourcerequest", "pk": 1, "fields": {"external_id": "067ad8fc-2551-4267-8a09-6facaebf0e1f", "created_date": "2023-09-05T22:50:10.221Z", "modified_date": "2023-09-05T22:50:10.221Z", "deleted": false, "origin_facility": 1, "approving_facility": 2, "assigned_facility": 3, "emergency": false, "title": "Oxygen Cylinder Request", "reason": "Insufficient oxygen supply", "refering_facility_contact_name": "Dr. <PERSON>", "refering_facility_contact_number": "+**********", "status": 10, "category": 100, "priority": null, "is_assigned_to_user": false, "assigned_to": null, "created_by": 1, "last_edited_by": 1}}, {"model": "facility.resourcerequest", "pk": 2, "fields": {"external_id": "0c77c84a-3b91-419c-807c-65e53f11a74f", "created_date": "2023-09-06T22:50:10.221Z", "modified_date": "2023-09-06T22:50:10.221Z", "deleted": false, "origin_facility": 1, "approving_facility": 5, "assigned_facility": 8, "emergency": true, "title": "Oxygen Cylinder Request", "reason": "Urgent need for oxygen supply", "refering_facility_contact_name": "Dr. <PERSON>", "refering_facility_contact_number": "+987654321", "status": 15, "category": 100, "priority": 1, "is_assigned_to_user": true, "assigned_to": 3, "created_by": 2, "last_edited_by": 2}}, {"model": "facility.resourcerequest", "pk": 3, "fields": {"external_id": "b08312cc-c301-46c0-8347-c521cbae8c54", "created_date": "2023-09-07T22:50:10.221Z", "modified_date": "2023-09-07T22:50:10.221Z", "deleted": false, "origin_facility": 7, "approving_facility": 2, "assigned_facility": 3, "emergency": false, "title": "Medical Supplies Request", "reason": "Need for surgical supplies", "refering_facility_contact_name": "Dr. <PERSON>", "refering_facility_contact_number": "+**********", "status": 20, "category": 200, "priority": null, "is_assigned_to_user": false, "assigned_to": null, "created_by": 3, "last_edited_by": 3}}, {"model": "facility.resourcerequest", "pk": 4, "fields": {"external_id": "e9664dcf-224f-4e1e-a003-c4ff0d3699d3", "created_date": "2023-09-08T22:50:10.221Z", "modified_date": "2023-09-08T22:50:10.221Z", "deleted": false, "origin_facility": 9, "approving_facility": 1, "assigned_facility": 4, "emergency": true, "title": "Oxygen Cylinder Request", "reason": "Critical shortage of oxygen", "refering_facility_contact_name": "Dr. <PERSON>", "refering_facility_contact_number": "+**********", "status": 30, "category": 100, "priority": 2, "is_assigned_to_user": false, "assigned_to": null, "created_by": 4, "last_edited_by": 4}}, {"model": "facility.resourcerequest", "pk": 5, "fields": {"external_id": "ce300e4d-fae6-4ee4-8bf9-f70b98abd7be", "created_date": "2023-09-09T22:50:10.221Z", "modified_date": "2023-09-09T22:50:10.221Z", "deleted": false, "origin_facility": 5, "approving_facility": 6, "assigned_facility": 7, "emergency": false, "title": "Medical Supplies Request", "reason": "Need for bandages and gauze", "refering_facility_contact_name": "Dr. <PERSON>", "refering_facility_contact_number": "+**********", "status": 55, "category": 200, "priority": null, "is_assigned_to_user": false, "assigned_to": null, "created_by": 5, "last_edited_by": 5}}, {"model": "facility.resourcerequest", "pk": 6, "fields": {"external_id": "79b90b87-1cac-449b-95d2-656f0c543652", "created_date": "2023-09-10T22:50:10.221Z", "modified_date": "2023-09-10T22:50:10.221Z", "deleted": false, "origin_facility": 12, "approving_facility": 9, "assigned_facility": 1, "emergency": true, "title": "Oxygen Cylinder Request", "reason": "Urgent need for oxygen supply", "refering_facility_contact_name": "Dr. <PERSON>", "refering_facility_contact_number": "+**********", "status": 80, "category": 100, "priority": 1, "is_assigned_to_user": true, "assigned_to": 6, "created_by": 6, "last_edited_by": 6}}, {"model": "facility.resourcerequest", "pk": 7, "fields": {"external_id": "fb5fcb52-8640-4a34-95f3-8adaa7c34f6d", "created_date": "2023-09-11T22:50:10.221Z", "modified_date": "2023-09-11T22:50:10.221Z", "deleted": false, "origin_facility": 10, "approving_facility": 11, "assigned_facility": 9, "emergency": false, "title": "Medical Supplies Request", "reason": "Need for disposable gloves", "refering_facility_contact_name": "Dr. <PERSON>", "refering_facility_contact_number": "+**********", "status": 70, "category": 200, "priority": null, "is_assigned_to_user": false, "assigned_to": null, "created_by": 7, "last_edited_by": 7}}, {"model": "facility.resourcerequest", "pk": 8, "fields": {"external_id": "94ac6a65-cf45-4a01-aa5a-11238ae7dca5", "created_date": "2023-09-12T22:50:10.221Z", "modified_date": "2023-09-12T22:50:10.221Z", "deleted": false, "origin_facility": 15, "approving_facility": 7, "assigned_facility": 6, "emergency": true, "title": "Oxygen Cylinder Request", "reason": "Critical need for oxygen cylinders", "refering_facility_contact_name": "Dr. <PERSON>", "refering_facility_contact_number": "+**********", "status": 80, "category": 100, "priority": 2, "is_assigned_to_user": true, "assigned_to": 8, "created_by": 8, "last_edited_by": 8}}, {"model": "facility.resourcerequest", "pk": 9, "fields": {"external_id": "b8137245-82de-48d8-add1-132d8cf4458a", "created_date": "2023-09-13T22:50:10.221Z", "modified_date": "2023-09-13T22:50:10.221Z", "deleted": false, "origin_facility": 3, "approving_facility": 9, "assigned_facility": 1, "emergency": false, "title": "Medical Supplies Request", "reason": "Need for IV fluids", "refering_facility_contact_name": "Dr. <PERSON>", "refering_facility_contact_number": "+**********", "status": 70, "category": 200, "priority": null, "is_assigned_to_user": false, "assigned_to": null, "created_by": 9, "last_edited_by": 9}}, {"model": "facility.resourcerequest", "pk": 10, "fields": {"external_id": "996d4aa0-3694-4b05-bb75-53a194d65158", "created_date": "2023-09-14T22:50:10.221Z", "modified_date": "2023-09-14T22:50:10.221Z", "deleted": false, "origin_facility": 10, "approving_facility": 1, "assigned_facility": 5, "emergency": true, "title": "Oxygen Cylinder Request", "reason": "Urgent need for oxygen supply", "refering_facility_contact_name": "Dr. <PERSON>", "refering_facility_contact_number": "+**********", "status": 10, "category": 100, "priority": 1, "is_assigned_to_user": true, "assigned_to": 10, "created_by": 10, "last_edited_by": 10}}]