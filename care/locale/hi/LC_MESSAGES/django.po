msgid ""
msgstr ""
"Project-Id-Version: ohccarefe\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-25 17:02+0530\n"
"PO-Revision-Date: 2024-09-16 14:40\n"
"Last-Translator: \n"
"Language-Team: Hindi\n"
"Language: hi_IN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: ohccarefe\n"
"X-Crowdin-Project-ID: 704503\n"
"X-Crowdin-Language: hi\n"
"X-Crowdin-File: /[ohcnetwork.care] develop/locale/kn/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 76\n"

#: care/abdm/apps.py:7
msgid "ABDM Integration"
msgstr "ABDM एकीकरण"

#: care/audit_log/apps.py:7
msgid "Audit Log Management"
msgstr "ऑडिट लॉग प्रबंधन"

#: care/facility/apps.py:7
msgid "Facility Management"
msgstr "सुविधा प्रबंधन"

#: care/facility/models/encounter_symptom.py:16
msgid "In Progress"
msgstr "प्रगति पर है"

#: care/facility/models/encounter_symptom.py:17
msgid "Completed"
msgstr "पुरा होना।"

#: care/facility/models/encounter_symptom.py:18
#: care/facility/models/icd11_diagnosis.py:53
msgid "Entered in Error"
msgstr "त्रुटि दर्ज की गई"

#: care/facility/models/icd11_diagnosis.py:48
msgid "Unconfirmed"
msgstr "अपुष्ट"

#: care/facility/models/icd11_diagnosis.py:49
msgid "Provisional"
msgstr "अनंतिम"

#: care/facility/models/icd11_diagnosis.py:50
msgid "Differential"
msgstr "अंतर"

#: care/facility/models/icd11_diagnosis.py:51
msgid "Confirmed"
msgstr "की पुष्टि"

#: care/facility/models/icd11_diagnosis.py:52
msgid "Refuted"
msgstr "का खंडन किया"

#: care/facility/models/patient.py:50
msgid "Non-card holder"
msgstr "गैर-कार्ड धारक"

#: care/facility/models/patient.py:51
msgid "BPL"
msgstr "गरीबी रेखा से नीचे"

#: care/facility/models/patient.py:52
msgid "APL"
msgstr "एपीएल"

#: care/facility/models/patient_base.py:88
msgid "Unknown"
msgstr "अज्ञात"

#: care/facility/models/patient_base.py:89
msgid "Recovered"
msgstr "बरामद"

#: care/facility/models/patient_base.py:90
msgid "Referred"
msgstr "निर्दिष्ट"

#: care/facility/models/patient_base.py:91
msgid "Expired"
msgstr "खत्म हो चुका"

#: care/facility/models/patient_base.py:92
msgid "LAMA"
msgstr "लामा"

#: care/facility/models/patient_base.py:127
msgid "Outpatient/Emergency Room"
msgstr "बाह्य रोगी/आपातकालीन कक्ष"

#: care/facility/models/patient_base.py:128
msgid "Referred from another facility"
msgstr "किसी अन्य सुविधा से संदर्भित"

#: care/facility/models/patient_base.py:129
msgid "Internal Transfer within the facility"
msgstr "सुविधा के भीतर आंतरिक स्थानांतरण"

#: care/facility/models/patient_base.py:130
msgid "(Unknown)"
msgstr "(अज्ञात)"

#: care/hcx/apps.py:7
msgid "HCX Integration"
msgstr "HCX एकीकरण"

#: care/templates/pages/home.html:8
msgid "Open Healthcare Network"
msgstr "ओपन हेल्थकेयर नेटवर्क"

#: care/templates/pages/home.html:9
msgid "Our Goal is to defend the Healthcare system of Kerala from overloading beyond capacity."
msgstr "हमारा लक्ष्य केरल की स्वास्थ्य सेवा प्रणाली को क्षमता से अधिक भार से बचाना है।"

#: care/users/apps.py:7
msgid "Users"
msgstr "उपयोगकर्ताओं"

#: care/users/forms.py:15
msgid "This username has already been taken."
msgstr "यह उपयोगकर्ता नाम पहले ही ले लिया गया है."

#: care/users/models.py:179
msgid "username"
msgstr "उपयोगकर्ता नाम"

#: care/users/models.py:184
msgid "A user with that username already exists."
msgstr "इस नाम का उपयोगकर्ता पहले से मौजूद है।"

#: care/users/reset_password_views.py:245
msgid "There is no active user associated with this username or the password can not be changed"
msgstr "इस उपयोगकर्ता नाम से कोई सक्रिय उपयोगकर्ता संबद्ध नहीं है या पासवर्ड बदला नहीं जा सकता"

#: care/utils/models/validators.py:54
msgid "Username must be 4 to 16 characters long. It may only contain lowercase alphabets, numbers, underscores, hyphens and dots. It shouldn't start or end with underscores, hyphens or dots. It shouldn't contain consecutive underscores, hyphens or dots."
msgstr "उपयोगकर्ता नाम 4 से 16 अक्षरों का होना चाहिए। इसमें केवल लोअरकेस अक्षर, संख्याएँ, अंडरस्कोर, हाइफ़न और डॉट्स हो सकते हैं। यह अंडरस्कोर, हाइफ़न या डॉट्स से शुरू या खत्म नहीं होना चाहिए। इसमें लगातार अंडरस्कोर, हाइफ़न या डॉट्स नहीं होने चाहिए।"

#: care/utils/ulid/serializers.py:9
#, python-brace-format
msgid "\"{value}\" is not a valid ULID."
msgstr "\"{value}\" एक वैध ULID नहीं है."

#: config/auth_views.py:19
msgid "Too Many Requests Provide Captcha"
msgstr "बहुत सारे अनुरोधों में कैप्चा दिया गया है"

#: config/auth_views.py:27
msgid "No active account found with the given credentials"
msgstr "दिए गए क्रेडेंशियल के साथ कोई सक्रिय खाता नहीं मिला"

#: config/authentication.py:141
msgid "Authorization header must contain two space-delimited values"
msgstr "प्राधिकरण हेडर में दो स्पेस-डिलीमिटेड मान होने चाहिए"

#: config/authentication.py:270
msgid "Used for authenticating requests from the middleware. The scheme requires a valid JWT token in the Authorization header along with the facility id in the X-Facility-Id header. --The value field is just for preview, filling it will show allowed endpoints.--"
msgstr "मिडलवेयर से अनुरोधों को प्रमाणित करने के लिए उपयोग किया जाता है। इस योजना के लिए प्राधिकरण हेडर में वैध JWT टोकन के साथ-साथ X-Facility-Id हेडर में सुविधा आईडी की आवश्यकता होती है। --मूल्य फ़ील्ड केवल पूर्वावलोकन के लिए है, इसे भरने से अनुमत समापन बिंदु दिखाई देंगे।--"

#: config/authentication.py:289
msgid "Used for authenticating requests from the middleware on behalf of assets. The scheme requires a valid JWT token in the Authorization header along with the facility id in the X-Facility-Id header. --The value field is just for preview, filling it will show allowed endpoints.--"
msgstr "परिसंपत्तियों की ओर से मिडलवेयर से अनुरोधों को प्रमाणित करने के लिए उपयोग किया जाता है। इस योजना के लिए प्राधिकरण हेडर में वैध JWT टोकन के साथ-साथ X-Facility-Id हेडर में सुविधा आईडी की आवश्यकता होती है। --मूल्य फ़ील्ड केवल पूर्वावलोकन के लिए है, इसे भरने से अनुमत एंडपॉइंट दिखाई देंगे।--"

#: config/authentication.py:306 config/authentication.py:320
msgid "Do not use this scheme for production."
msgstr "उत्पादन के लिए इस योजना का उपयोग न करें।"

#: config/patient_otp_authentication.py:56
msgid "Given token not valid for any token type"
msgstr "दिया गया टोकन किसी भी टोकन प्रकार के लिए मान्य नहीं है"

#: config/settings/base.py:60
msgid "English"
msgstr "अंग्रेज़ी"

#: config/settings/base.py:61
msgid "Malayalam"
msgstr "मलयालम"

#: config/settings/base.py:62
msgid "Hindi"
msgstr "हिन्दी"

#: config/settings/base.py:63
msgid "Tamil"
msgstr "तामिल"

#: config/validators.py:12
msgid "The password must contain at least 1 digit, 0-9."
msgstr "पासवर्ड में कम से कम एक अंक, 0-9, होना चाहिए।"

#: config/validators.py:17
msgid "Your password must contain at least 1 digit, 0-9."
msgstr "आपके पासवर्ड में कम से कम एक अंक, 0-9, होना चाहिए।"

#: config/validators.py:24
msgid "The password must contain at least 1 uppercase letter, A-Z."
msgstr "पासवर्ड में कम से कम एक बड़ा अक्षर, AZ होना चाहिए।"

#: config/validators.py:29
msgid "Your password must contain at least 1 uppercase letter, A-Z."
msgstr "आपके पासवर्ड में कम से कम 1 बड़ा अक्षर, AZ होना चाहिए।"

#: config/validators.py:36
msgid "The password must contain at least 1 lowercase letter, a-z."
msgstr "पासवर्ड में कम से कम 1 छोटा अक्षर, az होना चाहिए।"

#: config/validators.py:41
msgid "Your password must contain at least 1 lowercase letter, a-z."
msgstr "आपके पासवर्ड में कम से कम 1 छोटा अक्षर, az होना चाहिए।"

#: config/validators.py:49
msgid "The password must contain at least 1 symbol: ()[]{}|\\`~!@#$%^&*_-+=;:'\\\",<>./?"
msgstr "पासवर्ड में कम से कम 1 प्रतीक होना चाहिए: ()[]{}|\\`~!@#$%^&*_-+=;:'\\\",<>./?"

#: config/validators.py:57
msgid "Your password must contain at least 1 symbol: ()[]{}|\\`~!@#$%^&*_-+=;:'\\\",<>./?"
msgstr "आपके पासवर्ड में कम से कम 1 प्रतीक होना चाहिए: ()[]{}|\\`~!@#$%^&*_-+=;:'\\\",<>./?"

#: config/validators.py:66
msgid "The domain name is invalid. It should not start with scheme and should not end with a trailing slash."
msgstr "डोमेन नाम अमान्य है। इसे स्कीम से शुरू नहीं करना चाहिए और अंत में स्लैश से नहीं होना चाहिए।"
