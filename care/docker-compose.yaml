
networks:
  default:
    name: care

services:
  db:
    image: postgres:alpine
    restart: unless-stopped
    env_file:
      - ./docker/.prebuilt.env
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ${BACKUP_DIR:-./care-backups}:/backups
    ports:
      - "5433:5432"

  redis:
    image: redis:7.2-alpine
    restart: unless-stopped
    volumes:
      - care-redis-data:/data
    ports:
      - "6380:6379"

  minio:
    image: minio/minio:latest
    restart: unless-stopped
    environment:
      MINIO_ROOT_USER: ${MINIO_ACCESS_KEY:-minioadmin}
      MINIO_ROOT_PASSWORD: ${MINIO_SECRET_KEY:-minioadmin}
      AWS_DEFAULT_REGION: ap-south-1  # To maintain compatibility with existing apps
    volumes:
      - "./care/media/minio:/data"
      - "./docker/minio/init-script.sh:/init-script.sh:ro"  # Mount the init script
      - "./docker/minio/entrypoint.sh:/entrypoint.sh:ro"  # Mount the entrypoint script
    ports:
      - "9100:9000"  # S3 API
      - "9001:9001"  # Web Console
    entrypoint: ["/entrypoint.sh"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/ready"]
      interval: 10s
      retries: 5
      start_period: 10s
      timeout: 10s

volumes:
  postgres-data:
  care-redis-data:
