version: 2
updates:
  - package-ecosystem: "pip"
    directory: "/"
    schedule:
      interval: "weekly"
    groups:
      boto:
        patterns:
          - "boto*"
    commit-message:
      prefix: "chore"
      include: "scope"

  - package-ecosystem: "docker"
    directory: "/docker"
    schedule:
      interval: "weekly"
    groups:
      all-dependencies:
        patterns:
          - "*"
    commit-message:
      prefix: "chore"
      include: "scope"

  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
    groups:
      all-dependencies:
        patterns:
          - "*"
    commit-message:
      prefix: "chore"
      include: "scope"

  - package-ecosystem: "devcontainers"
    directory: "/"
    schedule:
      interval: "weekly"
    groups:
      all-dependencies:
        patterns:
          - "*"
    commit-message:
      prefix: "chore"
      include: "scope"
