name: Lint Code Base

on:
  pull_request:
    branches:
      - develop
      - staging
  merge_group:

jobs:
  lint:
    runs-on: ubuntu-24.04-arm
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - run: |
          python -m pip install pre-commit

      - run: |
          pre-commit run --show-diff-on-failure --color=always \
          --from-ref ${{ github.event.pull_request.base.sha }} --to-ref ${{ github.event.pull_request.head.sha }}
