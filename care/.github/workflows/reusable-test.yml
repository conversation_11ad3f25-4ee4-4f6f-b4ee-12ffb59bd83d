name: Test

on:
  workflow_call:
    secrets:
      CODECOV_TOKEN:
        required: true
    inputs:
      event_name:
        required: false
        type: string
        default: "pull_request"

jobs:
  test:
    name: Test
    runs-on: ubuntu-24.04-arm
    steps:
      - uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Restore Docker layers cache
        id: cache-restore
        uses: actions/cache/restore@v4
        with:
          path: |
            ${{ runner.temp }}/.buildx-cache
            ${{ runner.temp }}/.buildx-mounted-cache
          key: ${{ runner.os }}-${{ runner.arch }}-buildx-dev-${{ hashFiles('Pipfile.lock', 'docker/dev.Dockerfile') }}
          restore-keys: |
            ${{ runner.os }}-${{ runner.arch }}-buildx-dev-

      - name: inject cache into docker
        uses: reproducible-containers/buildkit-cache-dance@v3.1.2
        with:
          cache-map: |
            {
              "${{ runner.temp }}/.buildx-mounted-cache": "/root/.cache/pip"
            }
          skip-extraction: ${{ steps.cache-restore.outputs.cache-hit }}

      - name: Create new cache
        run: |
          mkdir -p ${{ runner.temp }}/.buildx-cache
          mkdir -p ${{ runner.temp }}/.buildx-cache-new
          mkdir -p ${{ runner.temp }}/.buildx-mounted-cache

      - name: Build images
        run: |
          docker buildx build \
            --file docker/dev.Dockerfile \
            --tag care_local \
            --cache-from=type=local,src=${{ runner.temp }}/.buildx-cache \
            --cache-to=type=local,dest=${{ runner.temp }}/.buildx-cache-new,mode=max \
            --platform linux/arm64 \
            --load \
            .

      - name: Start services
        run: |
          docker compose -f docker-compose.yaml -f docker-compose.local.yaml up -d --wait || \
          (docker compose -f docker-compose.yaml -f docker-compose.local.yaml logs && exit 1)

      - name: Check missing migrations
        run: make checkmigration

      - name: Validate integrity of fixtures
        run: make load-dummy-data

      - name: Dump db
        if: ${{ inputs.event_name == 'push' || github.event_name == 'push' }}
        run: make dump-db

      - name: Run tests
        run: make test-coverage

      - name: Upload coverage report
        uses: codecov/codecov-action@v5
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}

      - name: Move cache
        run: |
          rm -rf ${{ runner.temp }}/.buildx-cache
          mv ${{ runner.temp }}/.buildx-cache-new ${{ runner.temp }}/.buildx-cache

      - name: Save Docker layers cache
        if: ${{ github.event_name == 'push' && github.ref == 'refs/heads/develop' && steps.cache-restore.outputs.cache-hit != 'true' }}
        uses: actions/cache/save@v4
        with:
          path: |
            ${{ runner.temp }}/.buildx-cache
            ${{ runner.temp }}/.buildx-mounted-cache
          key: ${{ runner.os }}-${{ runner.arch }}-buildx-dev-${{ hashFiles('Pipfile.lock', 'docker/dev.Dockerfile') }}

      # Upload dummy db as artifact so it can be used to speed up frontend tests
      - name: Dump db
        if: ${{ github.event_name == 'push' && github.ref == 'refs/heads/develop' }}
        run: make dump-db

      - name: Upload db artifact
        if: ${{ github.event_name == 'push' && github.ref == 'refs/heads/develop' }}
        uses: actions/upload-artifact@v4
        with:
          name: care-db-dump
          path: care_db.dump
          retention-days: 30
          compression-level: 0 # file is already compressed
