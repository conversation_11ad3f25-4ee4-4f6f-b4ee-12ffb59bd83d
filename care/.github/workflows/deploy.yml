name: Deploy Care

on:
  workflow_dispatch:
  push:
    tags:
      - "v*"
    branches:
      - develop
    paths-ignore:
      - "docs/**"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

# env:
#   AWS_DEFAULT_REGION: ap-south-1
#   AWS_DEFAULT_OUTPUT: json
#   ECS_SERVICE_BACKEND: "care-backend"
#   ECS_SERVICE_CELERY: "care-celery"
#   ECS_TASK_BACKEND: "care"
#   ECS_TASK_CELERY: "care-celery"
#   ECS_CLUSTER: "egov"
#   CONTAINER_NAME_BACKEND: "care-backend"
#   CONTAINER_NAME_WORKER: "care-celery-worker"
#   CONTAINER_NAME_CRON: "care-celery-beat"

jobs:
  test:
    if: github.repository == 'ohcnetwork/care'
    name: Test
    uses: ./.github/workflows/reusable-test.yml
    secrets:
      CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
    with:
      event_name: ${{ github.event_name }}

  build:
    needs: test
    name: Build ${{ matrix.platform }}
    runs-on: ${{ matrix.runner }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - platform: linux/amd64
            runner: ubuntu-24.04
          - platform: linux/arm64
            runner: ubuntu-24.04-arm
    steps:
      - uses: actions/checkout@v4

      - name: Prepare
        run: |
          platform=${{ matrix.platform }}
          echo "PLATFORM_PAIR=${platform//\//-}" >> $GITHUB_ENV

      - name: Generate docker tags
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            ghcr.io/${{ github.repository }}

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Cache Docker layers
        uses: actions/cache@v4
        with:
          path: ${{ runner.temp }}/.buildx-cache
          key: ${{ runner.os }}-${{ runner.arch }}-buildx-prod-${{ hashFiles('Pipfile.lock', 'docker/prod.Dockerfile') }}
          restore-keys: |
            ${{ runner.os }}-${{ runner.arch }}-buildx-prod-

      - name: Create new cache
        run: |
          mkdir -p ${{ runner.temp }}/.buildx-cache
          mkdir -p ${{ runner.temp }}/.buildx-cache-new

      - name: Build and push by digest
        id: build
        uses: docker/build-push-action@v6
        with:
          context: .
          file: docker/prod.Dockerfile
          platforms: ${{ matrix.platform }}
          labels: ${{ steps.meta.outputs.labels }}
          outputs: type=image,name=ghcr.io/${{ github.repository }},push-by-digest=true,name-canonical=true,push=true
          build-args: |
            APP_VERSION=${{ github.sha }}
            ADDITIONAL_PLUGS=${{ env.ADDITIONAL_PLUGS }}
          cache-from: type=local,src=${{ runner.temp }}/.buildx-cache
          cache-to: type=local,dest=${{ runner.temp }}/.buildx-cache-new,mode=max

      - name: Export digest
        run: |
          mkdir -p ${{ runner.temp }}/digests
          digest="${{ steps.build.outputs.digest }}"
          touch "${{ runner.temp }}/digests/${digest#sha256:}"

      - name: Upload digest
        uses: actions/upload-artifact@v4
        with:
          name: digests-${{ env.PLATFORM_PAIR }}
          path: ${{ runner.temp }}/digests/*
          if-no-files-found: error
          retention-days: 1

      - name: Move cache
        run: |
          rm -rf ${{ runner.temp }}/.buildx-cache
          mv ${{ runner.temp }}/.buildx-cache-new ${{ runner.temp }}/.buildx-cache

  merge-manifests:
    needs: build
    name: Merge & Push Manifests
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Download digests
        uses: actions/download-artifact@v4
        with:
          path: ${{ runner.temp }}/digests
          pattern: digests-*
          merge-multiple: true

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Generate docker tags
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            ghcr.io/${{ github.repository }}
          tags: |
            type=raw,value=production-latest,enable=${{ startsWith(github.event.ref, 'refs/tags/v') }}
            type=raw,value=production-latest-${{ github.run_number }}-{{date 'YYYYMMDD'}}-{{sha}},enable=${{ startsWith(github.event.ref, 'refs/tags/v') }}
            type=raw,value=staging-latest,enable=${{ github.ref == 'refs/heads/staging' }}
            type=raw,value=staging-latest-${{ github.run_number }}-{{date 'YYYYMMDD'}}-{{sha}},enable=${{ github.ref == 'refs/heads/staging' }}
            type=raw,value=latest,enable=${{ github.ref == 'refs/heads/develop' }}
            type=raw,value=latest-${{ github.run_number }},enable=${{ github.ref == 'refs/heads/develop' }}
            type=semver,pattern={{version}}

      - name: Create manifest list and push
        working-directory: ${{ runner.temp }}/digests
        run: |
          docker buildx imagetools create $(jq -cr '.tags | map("-t " + .) | join(" ")' <<< "$DOCKER_METADATA_OUTPUT_JSON") \
            $(printf 'ghcr.io/${{ github.repository }}@sha256:%s ' *)

      - name: Create Sentry release
        uses: getsentry/action-release@v1
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG: ${{ secrets.SENTRY_ORG }}
          SENTRY_PROJECT: ${{ secrets.SENTRY_PROJECT }}
        with:
          version: ${{ github.sha }}

  notify-release:
    needs: merge-manifests
    if: startsWith(github.event.ref, 'refs/tags/v')
    name: Notify release
    runs-on: ubuntu-24.04-arm
    steps:
      - name: Notify release
        run: |
          echo "Release ${{ github.sha }} is ready to be deployed to production"

#   deploy-staging-egov:
#     needs: merge-manifests
#     if: github.ref == 'refs/heads/develop'
#     name: Deploy to ECS API Egov
#     runs-on: ubuntu-24.04-arm
#     environment:
#       name: Staging-egov
#       url: https://careapi.ohc.network
#     steps:
#       - name: Configure AWS credentials
#         uses: aws-actions/configure-aws-credentials@v4
#         with:
#           aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
#           aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
#           aws-region: ${{ env.AWS_DEFAULT_REGION }}
#
#       - name: IMAGE Tagging
#         env:
#           ECR_REGISTRY: ghcr.io/${{ github.repository }}
#           IMAGE_TAG: latest-${{ github.run_number }}
#         run: echo "IMAGE_VALUE=`echo ghcr.io/${{ github.repository }}:$IMAGE_TAG`" >> $GITHUB_ENV
#
#       - name: Download task definition for Celery Service
#         run: |
#           set -e
#           if aws ecs describe-task-definition --task-definition ${{ env.ECS_TASK_CELERY }} --query taskDefinition > celery-task-definition.json; then
#             echo "Successfully downloaded Celery task definition."
#           else
#             echo "Failed to download Celery task definition." >&2
#             exit 1
#           fi
#
#       - name: Fill in the new image ID for the worker container
#         id: render-worker
#         uses: aws-actions/amazon-ecs-render-task-definition@v1
#         with:
#           task-definition: celery-task-definition.json
#           container-name: ${{ env.CONTAINER_NAME_WORKER }}
#           image: ${{ env.IMAGE_VALUE }}
#
#       - name: Fill in the new image ID for the cron container
#         id: render-cron
#         uses: aws-actions/amazon-ecs-render-task-definition@v1
#         with:
#           task-definition: ${{ steps.render-worker.outputs.task-definition }}
#           container-name: ${{ env.CONTAINER_NAME_CRON }}
#           image: ${{ env.IMAGE_VALUE }}
#
#       - name: Deploy Celery task definition
#         uses: aws-actions/amazon-ecs-deploy-task-definition@v2
#         with:
#           task-definition: ${{ steps.render-cron.outputs.task-definition }}
#           service: ${{ env.ECS_SERVICE_CELERY }}
#           cluster: ${{ env.ECS_CLUSTER }}
#           wait-for-service-stability: true
#
#       - name: Download task definition for Backend Service
#         run: |
#           set -e
#           if aws ecs describe-task-definition --task-definition ${{ env.ECS_TASK_BACKEND }} --query taskDefinition > backend-task-definition.json; then
#             echo "Successfully downloaded Backend task definition."
#           else
#             echo "Failed to download Backend task definition." >&2
#             exit 1
#           fi
#       - name: Fill in the new image ID in the Backend task definition
#         id: backend-task-def
#         uses: aws-actions/amazon-ecs-render-task-definition@v1
#         with:
#           task-definition: backend-task-definition.json
#           container-name: ${{ env.CONTAINER_NAME_BACKEND }}
#           image: ${{ env.IMAGE_VALUE }}
#
#       - name: Deploy Backend task definition
#         uses: aws-actions/amazon-ecs-deploy-task-definition@v2
#         with:
#           task-definition: ${{ steps.backend-task-def.outputs.task-definition }}
#           service: ${{ env.ECS_SERVICE_BACKEND }}
#           cluster: ${{ env.ECS_CLUSTER }}
#           wait-for-service-stability: true
