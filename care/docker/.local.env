POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_HOST=db
POSTGRES_DB=care
POSTGRES_PORT=5432
DATABASE_URL=************************************/care
REDIS_URL=redis://redis:6379
CELERY_BROKER_URL=redis://redis:6379/0

DJANGO_DEBUG=true
ATTACH_DEBUGGER=false

BUCKET_REGION=ap-south-1
BUCKET_KEY=${MINIO_ACCESS_KEY:-minioadmin}
BUCKET_SECRET=${MINIO_SECRET_KEY:-minioadmin}
BUCKET_ENDPOINT=http://minio:9000
BUCKET_EXTERNAL_ENDPOINT=http://localhost:9100
FILE_UPLOAD_BUCKET=patient-bucket
FACILITY_S3_BUCKET=facility-bucket

# HCX Sandbox Config for local and testing
HCX_AUTH_BASE_PATH=https://staging-hcx.swasth.app/auth/realms/swasth-health-claim-exchange/protocol/openid-connect/token
HCX_ENCRYPTION_PRIVATE_KEY_URL=https://raw.githubusercontent.com/Swasth-Digital-Health-Foundation/hcx-platform/main/demo-app/server/resources/keys/x509-private-key.pem
HCX_IG_URL=https://ig.hcxprotocol.io/v0.7.1
HCX_PARTICIPANT_CODE=qwertyreboot.gmail@swasth-hcx-staging
HCX_PASSWORD=Opensaber@123
HCX_PROTOCOL_BASE_PATH=http://staging-hcx.swasth.app/api/v0.7
HCX_USERNAME=<EMAIL>
HCX_CERT_URL=https://raw.githubusercontent.com/Swasth-Digital-Health-Foundation/hcx-platform/main/demo-app/server/resources/keys/x509-self-signed-certificate.pem
