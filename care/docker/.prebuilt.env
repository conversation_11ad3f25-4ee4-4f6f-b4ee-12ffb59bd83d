POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_HOST=db
POSTGRES_DB=care
POSTGRES_PORT=5432
DATABASE_URL=************************************/care
REDIS_URL=redis://redis:6379/0
CELERY_BROKER_URL=redis://redis:6379/0
DJANGO_SETTINGS_MODULE=config.settings.deployment
DJANGO_DEBUG=False

BUCKET_REGION=ap-south-1
# WARNING: These are default MinIO credentials. Ensure to change these in production environments
BUCKET_KEY=minioadmin
BUCKET_SECRET=minioadmin
BUCKET_ENDPOINT=http://minio:9000
BUCKET_EXTERNAL_ENDPOINT=http://localhost:9100
FILE_UPLOAD_BUCKET=patient-bucket
FACILITY_S3_BUCKET=facility-bucket

SNS_ACCESS_KEY=123
SNS_SECRET_KEY=123
DJANGO_ADMIN_URL=admin
EMAIL_HOST=123
EMAIL_USER=123
EMAIL_PASSWORD=123
DJANGO_SECRET_KEY=123

DJANGO_SECURE_HSTS_PRELOAD=False
DJANGO_SECURE_HSTS_INCLUDE_SUBDOMAINS=False
DJANGO_SECURE_SSL_REDIRECT=False
DJANGO_SECURE_CONTENT_TYPE_NOSNIFF=False

# HCX Sandbox Config for local and testing
HCX_AUTH_BASE_PATH=https://staging-hcx.swasth.app/auth/realms/swasth-health-claim-exchange/protocol/openid-connect/token
HCX_ENCRYPTION_PRIVATE_KEY_URL=https://raw.githubusercontent.com/Swasth-Digital-Health-Foundation/hcx-platform/main/demo-app/server/resources/keys/x509-private-key.pem
HCX_IG_URL=https://ig.hcxprotocol.io/v0.7.1
HCX_PARTICIPANT_CODE=qwertyreboot.gmail@swasth-hcx-staging
HCX_PASSWORD=Opensaber@123
HCX_PROTOCOL_BASE_PATH=http://staging-hcx.swasth.app/api/v0.7
HCX_USERNAME=<EMAIL>
HCX_CERT_URL=https://raw.githubusercontent.com/Swasth-Digital-Health-Foundation/hcx-platform/main/demo-app/server/resources/keys/x509-self-signed-certificate.pem
