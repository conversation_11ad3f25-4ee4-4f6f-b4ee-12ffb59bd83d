from django.db import models

from care.facility.models import (
    FacilityBaseModel,
    PatientRegistration,
    pretty_boolean,
    reverse_choices,
)
from care.users.models import User
from care.utils.models.validators import mobile_or_landline_number_validator

RESOURCE_STATUS_CHOICES = (
    (10, "PENDING"),
    (15, "ON HOLD"),
    (20, "APPROVED"),
    (30, "REJECTED"),
    (55, "TRANSPORTATION TO BE ARRANGED"),
    (70, "TRANSFER IN PROGRESS"),
    (80, "COMPLETED"),
)

RESOURCE_CATEGORY_CHOICES = (
    (10, "PATIENT_CARE"),
    (20, "COMFORT_DEVICES"),
    (30, "MEDICINES"),
    (40, "FINANCIAL"),
    (100, "OTHERS"),
    (200, "SUPPLIES"),
)

RESOURCE_SUB_CATEGORY_CHOICES = (
    (110, "LIQUID OXYGEN"),
    (120, "B TYPE OXYGEN CYLINDER"),
    (130, "C TYPE OXYGEN CYLINDER"),
    (140, "JUMBO D TYPE OXYGEN CYLINDER"),
    (1000, "UNSPECIFIED"),
)

REVERSE_CATEGORY = reverse_choices(RESOURCE_CATEGORY_CHOICES)
REVERSE_SUB_CATEGORY = reverse_choices(RESOURCE_SUB_CATEGORY_CHOICES)


class ResourceRequest(FacilityBaseModel):
    origin_facility = models.ForeignKey(
        "Facility",
        on_delete=models.PROTECT,
        related_name="resource_requesting_facility",
    )
    approving_facility = models.ForeignKey(
        "Facility",
        on_delete=models.SET_NULL,
        null=True,
        related_name="resource_approving_facility",
    )
    assigned_facility = models.ForeignKey(
        "Facility",
        on_delete=models.SET_NULL,
        null=True,
        related_name="resource_assigned_facility",
    )
    emergency = models.BooleanField(default=False)
    title = models.CharField(max_length=255, null=False, blank=False)
    reason = models.TextField(default="", blank=True)
    refering_facility_contact_name = models.TextField(default="", blank=True)
    refering_facility_contact_number = models.CharField(
        max_length=14,
        validators=[mobile_or_landline_number_validator],
        default="",
        blank=True,
    )
    status = models.IntegerField(
        choices=RESOURCE_STATUS_CHOICES, default=10, null=False, blank=False
    )
    category = models.IntegerField(
        choices=RESOURCE_CATEGORY_CHOICES, default=100, null=False, blank=False
    )
    priority = models.IntegerField(default=None, null=True, blank=True)

    is_assigned_to_user = models.BooleanField(default=False)
    assigned_to = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="resource_request_assigned_to",
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="resource_request_created_by",
    )
    last_edited_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="resource_request_last_edited_by",
    )

    related_patient = models.ForeignKey(
        PatientRegistration,
        on_delete=models.CASCADE,
        default=None,
        null=True,
        blank=True,
    )

    CSV_MAPPING = {
        "created_date": "Created Date",
        "modified_date": "Modified Date",
        "origin_facility__name": "From Facility",
        "assigned_facility__name": "Assigned Facility",
        "approving_facility__name": "Approving Facility",
        "status": "Current Status",
        "emergency": "Emergency Shift",
        "reason": "Reason for Shifting",
        "title": "Title",
        "category": "Category",
        "priority": "Priority",
        "assigned_to__username": "Assigned User Username",
    }

    CSV_MAKE_PRETTY = {
        "category": (lambda x: REVERSE_CATEGORY.get(x, "-")),
        "sub_category": (lambda x: REVERSE_SUB_CATEGORY.get(x, "-")),
        "emergency": pretty_boolean,
    }

    class Meta:
        indexes = [
            models.Index(fields=["status", "deleted"]),
        ]

    @staticmethod
    def has_write_permission(request):
        return request.user.user_type not in User.READ_ONLY_TYPES

    @staticmethod
    def has_read_permission(request):
        return True

    def has_object_read_permission(self, request):
        return True

    def has_object_write_permission(self, request):
        return request.user.user_type not in User.READ_ONLY_TYPES

    def has_object_transfer_permission(self, request):
        return True

    def has_object_update_permission(self, request):
        return self.has_object_write_permission(request)


class ResourceRequestComment(FacilityBaseModel):
    request = models.ForeignKey(
        ResourceRequest, on_delete=models.PROTECT, null=False, blank=False
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
    )
    comment = models.TextField(default="", blank=True)
