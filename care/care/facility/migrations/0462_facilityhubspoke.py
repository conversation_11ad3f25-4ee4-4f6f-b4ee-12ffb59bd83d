# Generated by Django 5.1.1 on 2024-09-21 12:26

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("facility", "0461_remove_patientconsultation_prescriptions_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="FacilityHubSpoke",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "relationship",
                    models.IntegerField(
                        choices=[(1, "Regular Hub"), (2, "Tele ICU Hub")], default=1
                    ),
                ),
                (
                    "hub",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="spokes",
                        to="facility.facility",
                    ),
                ),
                (
                    "spoke",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="hubs",
                        to="facility.facility",
                    ),
                ),
            ],
            options={
                "constraints": [
                    models.CheckConstraint(
                        condition=models.Q(("hub", models.F("spoke")), _negated=True),
                        name="hub_and_spoke_not_same",
                    ),
                    models.UniqueConstraint(
                        condition=models.Q(("deleted", False)),
                        fields=("hub", "spoke"),
                        name="unique_hub_spoke",
                    ),
                    models.UniqueConstraint(
                        condition=models.Q(("deleted", False)),
                        fields=("spoke", "hub"),
                        name="unique_spoke_hub",
                    ),
                ],
            },
        ),
    ]
