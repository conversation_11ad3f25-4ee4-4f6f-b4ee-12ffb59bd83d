# Generated by Django 4.2.8 on 2024-02-10 10:05

from django.db import migrations, models

import care.utils.models.validators


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0414_remove_bed_old_name"),
    ]

    def set_prn_prescriptions_dosage_type(apps, schema_editor):
        Prescription = apps.get_model("facility", "Prescription")
        Prescription.objects.filter(is_prn=True).update(dosage_type="PRN")

    def reverse_set_prn_prescriptions_dosage_type(apps, schema_editor):
        Prescription = apps.get_model("facility", "Prescription")
        Prescription.objects.filter(dosage_type="PRN").update(is_prn=True)

    operations = [
        migrations.RenameField(
            model_name="prescription",
            old_name="dosage",
            new_name="base_dosage",
        ),
        migrations.AddField(
            model_name="prescription",
            name="dosage_type",
            field=models.CharField(
                choices=[
                    ("REGULAR", "REGULAR"),
                    ("TITRATED", "TITRATED"),
                    ("PRN", "PRN"),
                ],
                default="REGULAR",
                max_length=100,
            ),
        ),
        migrations.RunPython(
            set_prn_prescriptions_dosage_type, reverse_set_prn_prescriptions_dosage_type
        ),
        migrations.RemoveField(
            model_name="prescription",
            name="is_prn",
        ),
        migrations.AddField(
            model_name="medicineadministration",
            name="dosage",
            field=models.CharField(
                blank=True,
                max_length=100,
                null=True,
                validators=[
                    care.utils.models.validators.DenominationValidator(
                        allow_floats=True,
                        max_amount=5000,
                        min_amount=0.0001,
                        precision=4,
                        units={"mg", "ml", "drop(s)", "ampule(s)", "g", "tsp"},
                    )
                ],
            ),
        ),
        migrations.AddField(
            model_name="prescription",
            name="instruction_on_titration",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="prescription",
            name="target_dosage",
            field=models.CharField(
                blank=True,
                max_length=100,
                null=True,
                validators=[
                    care.utils.models.validators.DenominationValidator(
                        allow_floats=True,
                        max_amount=5000,
                        min_amount=0.0001,
                        precision=4,
                        units={"mg", "ml", "drop(s)", "ampule(s)", "g", "tsp"},
                    )
                ],
            ),
        ),
    ]
