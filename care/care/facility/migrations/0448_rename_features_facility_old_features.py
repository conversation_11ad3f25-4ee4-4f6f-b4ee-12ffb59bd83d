# Generated by Django 4.2.10 on 2024-06-03 06:24

import django.contrib.postgres.fields
from django.db import migrations, models


def convert_features_to_array(apps, schema_editor):
    Facility = apps.get_model("facility", "Facility")

    facilities_to_update = Facility.objects.filter(old_features__isnull=False)

    updated_facilities = []

    for facility in facilities_to_update:
        try:
            facility.features = list(facility.old_features)
            updated_facilities.append(facility)
        except ValueError:
            print(f"facility '{facility.name}' has invalid facility features")

    if updated_facilities:
        Facility.objects.bulk_update(updated_facilities, ["features"])


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0447_patientconsultationevent_taken_at"),
    ]

    operations = [
        migrations.RenameField(
            model_name="facility",
            old_name="features",
            new_name="old_features",
        ),
        migrations.AddField(
            model_name="facility",
            name="features",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.SmallIntegerField(
                    choices=[
                        (1, "CT Scan Facility"),
                        (2, "Maternity Care"),
                        (3, "X-Ray Facility"),
                        (4, "Neonatal Care"),
                        (5, "Operation Theater"),
                        (6, "Blood Bank"),
                    ]
                ),
                blank=True,
                null=True,
                size=None,
            ),
        ),
        migrations.RunPython(convert_features_to_array),
    ]
