# Generated by Django 2.2.11 on 2023-06-13 10:51

import uuid

import django.contrib.postgres.fields
import django.contrib.postgres.fields.jsonb
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import simple_history.models
from django.conf import settings
from django.db import migrations, models

import care.facility.models.mixins.permissions.asset
import care.facility.models.mixins.permissions.facility
import care.facility.models.mixins.permissions.patient
import care.utils.models.validators


class Migration(migrations.Migration):
    initial = True

    replaces = [
        ("facility", "0001_initial"),
        ("facility", "0002_auto_20200319_1244"),
        ("facility", "0003_auto_20200319_1739"),
        ("facility", "0004_auto_20200319_1833"),
        ("facility", "0005_facility_created_by"),
        ("facility", "0006_auto_20200320_1616"),
        ("facility", "0007_auto_20200320_1641"),
        ("facility", "0008_auto_20200320_1847"),
        ("facility", "0009_auto_20200320_1850"),
        ("facility", "0010_auto_20200321_0758"),
        ("facility", "0011_facility_phone_number"),
        ("facility", "0011_auto_20200321_0927"),
        ("facility", "0012_merge_20200321_1015"),
        ("facility", "0013_auto_20200322_1158"),
        ("facility", "0014_delete_facilitylocation"),
        ("facility", "0015_facility_location"),
        ("facility", "0014_facility_oxygen_capacity"),
        ("facility", "0016_merge_20200322_1622"),
        ("facility", "0017_auto_20200322_1642"),
        ("facility", "0018_ambulance_ambulancedriver"),
        ("facility", "0019_auto_20200322_2056"),
        ("facility", "0020_auto_20200323_1029"),
        ("facility", "0021_auto_20200324_0756"),
        ("facility", "0022_patientregistration_patientteleconsultation"),
        ("facility", "0022_ambulance_ambulance_type"),
        ("facility", "0023_merge_20200324_1959"),
        ("facility", "0024_auto_20200325_0311"),
        ("facility", "0025_auto_20200325_1908"),
        ("facility", "0026_map_to_district"),
        ("facility", "0027_auto_20200326_1015"),
        ("facility", "0028_auto_20200326_1705"),
        ("facility", "0029_auto_20200326_1706"),
        ("facility", "0030_auto_20200327_0619"),
        ("facility", "0031_rename_kasargode"),
        ("facility", "0028_auto_20200326_1752"),
        ("facility", "0030_merge_20200326_2047"),
        ("facility", "0032_merge_20200327_1215"),
        ("facility", "0033_ambulance_service_charge"),
        ("facility", "0034_auto_20200327_1628"),
        ("facility", "0035_auto_20200328_0442"),
        ("facility", "0036_patientconsultation_created_by"),
        ("facility", "0037_auto_20200328_1256"),
        ("facility", "0035_building_num_buildings"),
        ("facility", "0038_merge_20200328_1433"),
        ("facility", "0039_auto_20200328_1657"),
        ("facility", "0040_auto_20200328_1713"),
        ("facility", "0039_auto_20200328_1706"),
        ("facility", "0035_historicalfacilitycapacity"),
        ("facility", "0034_facilitypatientstatshistory"),
        ("facility", "0041_merge_20200328_1855"),
        ("facility", "0042_auto_20200328_2018"),
        ("facility", "0043_populate_facility_district"),
        ("facility", "0044_patientregistration_real_name"),
        ("facility", "0045_auto_20200329_0757"),
        ("facility", "0044_patientsample_patientsampleflow"),
        ("facility", "0046_merge_20200329_0842"),
        ("facility", "0047_auto_20200329_1051"),
        ("facility", "0048_auto_20200330_0433"),
        ("facility", "0049_auto_20200330_1047"),
        ("facility", "0050_dailyround"),
        ("facility", "0051_auto_20200330_1257"),
        ("facility", "0052_remove_patientconsultation_created_by"),
        ("facility", "0053_delete_duplicate_diseases"),
        ("facility", "0054_auto_20200331_0950"),
        ("facility", "0055_auto_20200331_1144"),
        ("facility", "0056_auto_20200401_0932"),
        ("facility", "0057_auto_20200401_1018"),
        ("facility", "0058_auto_20200401_1820"),
        ("facility", "0059_patientsample_fast_track"),
        ("facility", "0060_auto_20200402_1126"),
        ("facility", "0061_auto_20200402_1128"),
        ("facility", "0062_populate_facility_in_patient"),
        ("facility", "0062_auto_20200402_1336"),
        ("facility", "0063_merge_20200402_1402"),
        ("facility", "0064_auto_20200402_1624"),
        ("facility", "0065_auto_20200402_1647"),
        ("facility", "0066_auto_20200402_1806"),
        ("facility", "0067_auto_20200402_1841"),
        ("facility", "0068_auto_20200402_2149"),
        ("facility", "0069_auto_20200402_2209"),
        ("facility", "0070_auto_20200402_2228"),
        ("facility", "0071_auto_20200404_1737"),
        ("facility", "0068_auto_20200403_1611"),
        ("facility", "0072_merge_20200404_2148"),
        ("facility", "0073_auto_20200404_2303"),
        ("facility", "0074_auto_20200404_2352"),
        ("facility", "0075_auto_20200405_1122"),
        ("facility", "0076_auto_20200409_0412"),
        ("facility", "0077_auto_20200409_0422"),
        ("facility", "0078_auto_20200409_0436"),
        ("facility", "0079_auto_20200409_0451"),
        ("facility", "0080_auto_20200409_0459"),
        ("facility", "0081_auto_20200409_1201"),
        ("facility", "0082_populate_patient_search"),
        ("facility", "0083_auto_20200411_1136"),
        ("facility", "0084_install_trigram_similarity"),
        ("facility", "0085_auto_20200412_0116"),
        ("facility", "0086_auto_20200412_1313"),
        ("facility", "0087_add_users_to_facility_user"),
        ("facility", "0088_patientconsultationicmr_patienticmr_patientsampleicmr"),
        ("facility", "0089_auto_20200413_2036"),
        ("facility", "0090_auto_20200415_0710"),
        ("facility", "0091_auto_20200415_1158"),
        ("facility", "0092_recompute_facility_types"),
        ("facility", "0093_auto_20200415_1102"),
        ("facility", "0094_auto_20200417_1038"),
        ("facility", "0095_recompute_facility_types"),
        ("facility", "0096_auto_20200416_1414"),
        ("facility", "0097_auto_20200417_1404"),
        ("facility", "0098_auto_20200418_1302"),
        ("facility", "0099_auto_20200418_2030"),
        ("facility", "0100_auto_20200418_2315"),
        ("facility", "0101_populate_countries_travelled"),
        ("facility", "0102_auto_20200424_1508"),
        ("facility", "0103_auto_20200425_1440"),
        ("facility", "0104_populate_patient_external_ids"),
        ("facility", "0105_auto_20200425_1446"),
        ("facility", "0106_auto_20200510_1557"),
        ("facility", "0107_populate_external_ids"),
        ("facility", "0108_auto_20200510_1612"),
        ("facility", "0109_auto_20200511_2120"),
        ("facility", "0110_populate_patient_search_ids"),
        ("facility", "0111_auto_20200511_2123"),
        ("facility", "0112_auto_20200515_1738"),
        ("facility", "0113_auto_20200519_0005"),
        ("facility", "0114_auto_20200610_1720"),
        ("facility", "0115_auto_20200611_1336"),
        ("facility", "0116_facility_pincode"),
        ("facility", "0117_patientsample_icmr_category"),
        ("facility", "0118_auto_20200616_1051"),
        ("facility", "0119_auto_20200617_1618"),
        ("facility", "0120_patientsample_icmr_label"),
        ("facility", "0121_auto_20200619_2306"),
        ("facility", "0122_auto_20200624_1127"),
        ("facility", "0123_auto_20200626_0144"),
        ("facility", "0124_populate_daily_round_ids"),
        ("facility", "0125_auto_20200626_0146"),
        ("facility", "0126_auto_20200626_0156"),
        ("facility", "0127_patientsearch_is_active"),
        ("facility", "0128_facilityrelatedsummary"),
        ("facility", "0129_auto_20200706_1912"),
        ("facility", "0130_auto_20200706_1946"),
        ("facility", "0131_auto_20200706_1954"),
        ("facility", "0132_patientsample_testing_facility"),
        ("facility", "0133_auto_20200710_2355"),
        ("facility", "0134_auto_20200713_1756"),
        ("facility", "0135_auto_20200715_1433"),
        ("facility", "0136_auto_20200716_1003"),
        ("facility", "0137_auto_20200718_0654"),
        ("facility", "0138_auto_20200720_2227"),
        ("facility", "0139_shiftingrequest"),
        ("facility", "0140_auto_20200723_2355"),
        ("facility", "0141_facilityrelatedsummary_modified_date"),
        ("facility", "0142_shiftingrequest_patient"),
        ("facility", "0143_auto_20200731_1217"),
        ("facility", "0144_patientmobileotp"),
        ("facility", "0145_auto_20200731_1913"),
        ("facility", "0146_auto_20200801_1844"),
        ("facility", "0147_auto_20200802_2134"),
        ("facility", "0148_populate_unencrypted_patients"),
        ("facility", "0149_auto_20200802_2156"),
        ("facility", "0150_auto_20200802_2156"),
        ("facility", "0151_auto_20200802_2207"),
        ("facility", "0152_auto_20200805_1906"),
        ("facility", "0153_auto_20200805_2226"),
        ("facility", "0154_populate_patients_last_consultation"),
        ("facility", "0155_auto_20200805_2312"),
        ("facility", "0156_auto_20200808_2205"),
        ("facility", "0157_patientconsultation_ip_no"),
        ("facility", "0158_auto_20200809_1140"),
        ("facility", "0159_patientconsultation_is_telemedicine"),
        ("facility", "0160_patientconsultation_doctor"),
        ("facility", "0161_auto_20200810_1338"),
        ("facility", "0162_auto_20200811_1101"),
        ("facility", "0163_auto_20200811_1115"),
        ("facility", "0164_auto_20200811_1157"),
        ("facility", "0165_facilitypatientstatshistory_num_patient_confirmed_positive"),
        ("facility", "0166_auto_20200815_1930"),
        ("facility", "0167_auto_20200830_1121"),
        ("facility", "0168_auto_20200830_1222"),
        ("facility", "0169_auto_20200830_1232"),
        ("facility", "0170_patientconsultation_test_id"),
        ("facility", "0171_auto_20200901_2205"),
        ("facility", "0172_auto_20200903_1617"),
        ("facility", "0173_auto_20200903_1625"),
        ("facility", "0174_auto_20200903_1836"),
        ("facility", "0175_auto_20200904_1000"),
        ("facility", "0176_auto_20200916_1443"),
        ("facility", "0177_auto_20200916_1448"),
        ("facility", "0178_auto_20200916_1534"),
        ("facility", "0179_auto_20200918_1132"),
        ("facility", "0180_facility_ward"),
        ("facility", "0181_auto_20200921_1754"),
        ("facility", "0182_auto_20200921_1756"),
        ("facility", "0183_shiftingrequest_is_kasp"),
        ("facility", "0184_auto_20200925_2353"),
        ("facility", "0185_correct_blood_donation"),
        ("facility", "0186_auto_20200926_0001"),
        ("facility", "0187_patientexternaltest"),
        ("facility", "0188_auto_20200928_0139"),
        ("facility", "0189_auto_20200929_1258"),
        ("facility", "0190_auto_20201001_1134"),
        ("facility", "0191_dailyround_spo2"),
        ("facility", "0192_auto_20201004_1457"),
        ("facility", "0193_auto_20201004_1458"),
        ("facility", "0194_auto_20201009_1936"),
        ("facility", "0195_auto_20201009_1939"),
        ("facility", "0196_auto_20201011_2219"),
        ("facility", "0197_auto_20201011_2338"),
        ("facility", "0198_shiftingrequest_is_assigned_to_user"),
        ("facility", "0199_shiftingrequest_assigned_to"),
        ("facility", "0200_dailyround_admitted_to"),
        ("facility", "0201_populate_admitted_to_daily_round"),
        ("facility", "0202_auto_20201024_1956"),
        ("facility", "0203_auto_20201024_2024"),
        ("facility", "0204_auto_20201024_2052"),
        ("facility", "0205_auto_20201025_1709"),
        ("facility", "0206_notification"),
        ("facility", "0207_auto_20201123_0056"),
        ("facility", "0208_notification_caused_objects"),
        ("facility", "0209_auto_20201123_1132"),
        ("facility", "0210_remove_notification_caused_object_external_id"),
        ("facility", "0211_auto_20201214_1014"),
        ("facility", "0212_auto_20201223_1617"),
        ("facility", "0213_auto_20210107_1310"),
        ("facility", "0214_auto_20210129_2250"),
        ("facility", "0215_auto_20210130_2236"),
        ("facility", "0216_auto_20210201_2228"),
        (
            "facility",
            "0217_investigationsession_investigationvalue_patientinvestigation_patientinvestigationgroup",
        ),
        ("facility", "0218_auto_20210417_2354"),
        ("facility", "0219_remove_investigationsession_session"),
        ("facility", "0220_populate_investigations"),
        ("facility", "0221_auto_20210426_1153"),
        ("facility", "0222_auto_20210427_0002"),
        ("facility", "0221_investigationsession_created_by"),
        ("facility", "0221_facility_kasp_empanelled"),
        ("facility", "0223_merge_20210427_1419"),
        ("facility", "0224_change_disease_status_from_recover_to_recovered"),
        ("facility", "0225_facilityinventorylog_current_stock"),
        ("facility", "0226_facilityinventorylog_quantity_in_default_unit"),
        ("facility", "0227_auto_20210506_1409"),
        ("facility", "0228_shiftingrequest_breathlessness_level"),
        ("facility", "0227_auto_20210505_2055"),
        ("facility", "0229_merge_20210506_1549"),
        ("facility", "0230_facility_expected_oxygen_requirement"),
        ("facility", "0231_auto_20210508_2214"),
        ("facility", "0232_auto_20210510_1218"),
        ("facility", "0233_auto_20210510_1832"),
        ("facility", "0234_auto_20210511_2058"),
        ("facility", "0222_auto_20210501_0958"),
        ("facility", "0235_merge_20210513_1316"),
        ("facility", "0236_auto_20210513_1336"),
        ("facility", "0237_dailyround_created_by_telemedicine"),
        ("facility", "0238_fileupload_file_category"),
        ("facility", "0239_patientconsultation_is_kasp"),
        ("facility", "0240_patientconsultation_kasp_enabled_date"),
        ("facility", "0241_auto_20210519_0115"),
        ("facility", "0242_auto_20210523_1310"),
        ("facility", "0243_auto_20210524_2245"),
        ("facility", "0244_resourcerequest_title"),
        ("facility", "0245_auto_20210525_0130"),
        ("facility", "0246_facilityinventorylog_probable_accident"),
        ("facility", "0247_auto_20210526_1922"),
        ("facility", "0248_auto_20210529_1957"),
        ("facility", "0249_auto_20210529_2031"),
        ("facility", "0250_auto_20210603_0359"),
        ("facility", "0251_auto_20210607_2009"),
        ("facility", "0252_auto_20210609_2332"),
        ("facility", "0253_auto_20210612_1256"),
        ("facility", "0254_patientnotes"),
        (
            "facility",
            "0255_asset_assetlocation_assettransaction_facilitydefaultassetlocation_userdefaultassetlocation",
        ),
        ("facility", "0256_assettransaction_asset"),
        ("facility", "0257_auto_20210618_1642"),
        ("facility", "0258_auto_20210623_1742"),
        ("facility", "0259_patientexternaltest_patient_created"),
        ("facility", "0260_auto_20210710_1742"),
        ("facility", "0261_auto_20210710_2305"),
        ("facility", "0262_auto_20210711_0007"),
        ("facility", "0263_auto_20210711_1716"),
        ("facility", "0264_dailyround_in_prone_position"),
        ("facility", "0265_auto_20210712_1133"),
        ("facility", "0266_auto_20210717_2041"),
        ("facility", "0267_dailyround_meta"),
        ("facility", "0268_patientconsultation_special_instruction"),
        ("facility", "0268_auto_20210729_1327"),
        ("facility", "0269_merge_20210815_1344"),
        ("facility", "0270_auto_20210815_1356"),
        ("facility", "0271_auto_20210815_1617"),
        ("facility", "0272_auto_20210825_1248"),
        ("facility", "0273_auto_20210825_1829"),
        ("facility", "0274_auto_20210910_1647"),
        ("facility", "0275_auto_20210917_1544"),
        ("facility", "0276_auto_20210930_1451"),
        ("facility", "0275_auto_20210927_2033"),
        ("facility", "0275_auto_20210920_1811"),
        ("facility", "0277_merge_20211011_2103"),
        ("facility", "0278_asset_not_working_reason"),
        ("facility", "0279_auto_20211122_1138"),
        ("facility", "0280_auto_20211210_0038"),
        ("facility", "0281_auto_20211214_1454"),
        ("facility", "0282_patientconsultation_last_daily_round"),
        ("facility", "0282_dailyround_pain"),
        ("facility", "0283_merge_20220128_0315"),
        ("facility", "0284_patientconsultation_hba1c"),
        ("facility", "0285_asset_asset_class"),
        ("facility", "0286_auto_20220316_2004"),
        ("facility", "0287_auto_20220415_1932"),
        ("facility", "0288_patientconsultation_current_bed"),
        ("facility", "0287_asset_qr_code_id"),
        ("facility", "0288_auto_20220422_0206"),
        ("facility", "0289_merge_20220422_2220"),
        ("facility", "0290_auto_20220426_2231"),
        ("facility", "0291_facility_cover_image_url"),
        ("facility", "0292_auto_20220430_1748"),
        ("facility", "0293_auto_20220520_1941"),
        ("facility", "0294_auto_20220526_0133"),
        ("facility", "0293_facility_middleware_address"),
        ("facility", "0294_auto_20220523_1419"),
        ("facility", "0295_merge_20220527_1430"),
        ("facility", "0296_auto_20220527_1925"),
        ("facility", "0297_auto_20220619_1821"),
        ("facility", "0298_facility_features"),
        ("facility", "0298_auto_20220625_1627"),
        ("facility", "0299_merge_20220704_0225"),
        ("facility", "0297_facility_location"),
        ("facility", "0300_merge_20220707_2339"),
        ("facility", "0301_auto_20220709_2051"),
        ("facility", "0302_patientconsultation_prn_prescription"),
        ("facility", "0303_patientconsultation_procedure"),
        ("facility", "0304_auto_20220726_1903"),
        ("facility", "0305_auto_20220730_1956"),
        ("facility", "0306_auto_20220803_1946"),
        ("facility", "0307_auto_20220805_1933"),
        ("facility", "0308_auto_20220805_2247"),
        ("facility", "0309_auto_20220820_1541"),
        ("facility", "0309_auto_20220818_1949"),
        ("facility", "0310_merge_20220820_2047"),
        ("facility", "0311_auto_20220824_1757"),
        ("facility", "0312_patientconsultation_investigation"),
        ("facility", "0313_auto_20220901_2213"),
        ("facility", "0314_patientconsultation_icd11_diagnoses"),
        ("facility", "0315_auto_20220908_1027"),
        ("facility", "0316_auto_20220908_1112"),
        ("facility", "0315_patientconsultation_icd11_provisional_diagnoses"),
        ("facility", "0317_merge_20220915_2209"),
        ("facility", "0315_auto_20220909_0925"),
        ("facility", "0318_merge_20220917_2113"),
        ("facility", "0319_dailyround_medicine_administration"),
        ("facility", "0318_merge_20220918_1241"),
        ("facility", "0320_merge_20220920_2114"),
        ("facility", "0319_file_upload_completed"),
        ("facility", "0321_merge_20220921_2255"),
        ("facility", "0322_fileupload_is_archived"),
        ("facility", "0323_fileupload_archive_reason"),
        ("facility", "0322_patientconsultation_review_interval"),
        ("facility", "0324_merge_20221013_0053"),
        ("facility", "0315_auto_20220909_2322"),
        ("facility", "0316_auto_20220909_2331"),
        ("facility", "0325_merge_20221015_2136"),
        ("facility", "0326_auto_20221018_1526"),
        ("facility", "0327_fileupload_archived_by"),
        ("facility", "0327_auto_20221122_1750"),
        ("facility", "0328_merge_20221208_1110"),
        ("facility", "0329_auto_20221223_1152"),
        ("facility", "0330_auto_20221223_1952"),
        ("facility", "0331_auto_20221224_1531"),
        ("facility", "0332_auto_20230110_2114"),
        ("facility", "0333_auto_20230112_2233"),
        ("facility", "0332_patientconsultation_consultation_status"),
        ("facility", "0334_merge_20230113_1507"),
        ("facility", "0335_auto_20230207_1914"),
        ("facility", "0336_auto_20230222_1602"),
        ("facility", "0337_patientconsultation_referred_to_external"),
        ("facility", "0338_auto_20230323_1249"),
        ("facility", "0336_patientconsultation_op_no"),
        ("facility", "0339_merge_20230406_1408"),
        ("facility", "0340_auto_20230406_1409"),
        ("facility", "0341_auto_20230407_0346"),
        ("facility", "0342_auto_20230407_1424"),
        ("facility", "0343_auto_20230407_1850"),
        ("facility", "0344_auto_20230414_2040"),
        ("facility", "0345_auto_20230414_2046"),
        ("facility", "0337_metaicd11diagnosis"),
        ("facility", "0346_merge_20230419_0952"),
        ("facility", "0347_auto_20230421_1357"),
        ("facility", "0344_shiftingrequest_assigned_facility_external"),
        ("facility", "0348_merge_20230421_1917"),
        ("facility", "0349_auto_20230422_2058"),
        ("facility", "0350_auto_20230422_2114"),
        ("facility", "0351_auto_20230424_1227"),
        ("facility", "0352_auto_20230428_1539"),
        ("facility", "0353_auto_20230429_2026"),
        ("facility", "0354_auto_20230519_1501"),
        ("facility", "0355_auto_20230429_2027"),
        ("facility", "0356_auto_20230512_1122"),
        ("facility", "0357_auto_20230523_1304"),
        ("facility", "0358_auto_20230524_1853"),
        ("facility", "0359_auto_20230529_1907"),
        ("facility", "0360_auto_20230608_1750"),
    ]

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("users", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Ambulance",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "vehicle_number",
                    models.CharField(
                        db_index=True,
                        max_length=20,
                        unique=True,
                        validators=[
                            django.core.validators.RegexValidator(
                                code="invalid_vehicle_number",
                                message="Please Enter the vehicle number in all uppercase without spaces, eg: KL13AB1234",
                                regex="^[A-Z]{2}[0-9]{1,2}[A-Z]{0,2}[0-9]{1,4}$",
                            )
                        ],
                    ),
                ),
                ("owner_name", models.CharField(max_length=255)),
                (
                    "owner_phone_number",
                    models.CharField(
                        max_length=14,
                        validators=[
                            django.core.validators.RegexValidator(
                                code="invalid_mobile",
                                message="Please Enter 10/11 digit mobile number or landline as 0<std code><phone number>",
                                regex="^((\\+91|91|0)[\\- ]{0,1})?[456789]\\d{9}$",
                            )
                        ],
                    ),
                ),
                ("owner_is_smart_phone", models.BooleanField(default=True)),
                ("has_oxygen", models.BooleanField()),
                ("has_ventilator", models.BooleanField()),
                ("has_suction_machine", models.BooleanField()),
                ("has_defibrillator", models.BooleanField()),
                (
                    "insurance_valid_till_year",
                    models.IntegerField(
                        choices=[(2020, 2020), (2021, 2021), (2022, 2022)]
                    ),
                ),
                (
                    "ambulance_type",
                    models.IntegerField(
                        choices=[(1, "Basic"), (2, "Cardiac"), (3, "Hearse")], default=1
                    ),
                ),
                (
                    "price_per_km",
                    models.DecimalField(decimal_places=2, max_digits=7, null=True),
                ),
                ("has_free_service", models.BooleanField(default=False)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "primary_district",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="primary_ambulances",
                        to="users.District",
                    ),
                ),
                (
                    "secondary_district",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="secondary_ambulances",
                        to="users.District",
                    ),
                ),
                (
                    "third_district",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="third_ambulances",
                        to="users.District",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Asset",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("name", models.CharField(max_length=1024)),
                ("description", models.TextField(blank=True, default="", null=True)),
                (
                    "asset_type",
                    models.IntegerField(
                        choices=[(50, "INTERNAL"), (100, "EXTERNAL")], default=50
                    ),
                ),
                (
                    "asset_class",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("ONVIF", "onvif"),
                            ("HL7MONITOR", "hl7monitor"),
                            ("VENTILATOR", "ventilator"),
                        ],
                        default=None,
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "status",
                    models.IntegerField(
                        choices=[(50, "ACTIVE"), (100, "TRANSFER_IN_PROGRESS")],
                        default=50,
                    ),
                ),
                (
                    "is_working",
                    models.BooleanField(blank=True, default=None, null=True),
                ),
                (
                    "not_working_reason",
                    models.CharField(blank=True, max_length=1024, null=True),
                ),
                (
                    "serial_number",
                    models.CharField(blank=True, max_length=1024, null=True),
                ),
                (
                    "warranty_details",
                    models.TextField(blank=True, default="", null=True),
                ),
                (
                    "meta",
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True,
                        default=dict,
                        validators=[
                            care.utils.models.validators.JSONFieldSchemaValidator(
                                {
                                    "$schema": "http://json-schema.org/draft-07/schema#",
                                    "anyOf": [
                                        {"$ref": "#/definitions/onvif"},
                                        {"$ref": "#/definitions/hl7monitor"},
                                        {"$ref": "#/definitions/empty"},
                                    ],
                                    "definitions": {
                                        "empty": {
                                            "additionalProperties": False,
                                            "type": "object",
                                        },
                                        "hl7monitor": {
                                            "additionalProperties": False,
                                            "properties": {
                                                "asset_type": {"type": "string"},
                                                "insecure_connection": {
                                                    "type": "boolean"
                                                },
                                                "local_ip_address": {"type": "string"},
                                                "middleware_hostname": {
                                                    "type": "string"
                                                },
                                            },
                                            "required": ["local_ip_address"],
                                            "type": "object",
                                        },
                                        "onvif": {
                                            "additionalProperties": False,
                                            "properties": {
                                                "asset_type": {"type": "string"},
                                                "camera_access_key": {"type": "string"},
                                                "camera_type": {"type": "string"},
                                                "insecure_connection": {
                                                    "type": "boolean"
                                                },
                                                "local_ip_address": {"type": "string"},
                                                "middleware_hostname": {
                                                    "type": "string"
                                                },
                                            },
                                            "required": [
                                                "local_ip_address",
                                                "camera_access_key",
                                            ],
                                            "type": "object",
                                        },
                                        "ventilator": {
                                            "additionalProperties": False,
                                            "properties": {
                                                "asset_type": {"type": "string"},
                                                "insecure_connection": {
                                                    "type": "boolean"
                                                },
                                                "local_ip_address": {"type": "string"},
                                                "middleware_hostname": {
                                                    "type": "string"
                                                },
                                            },
                                            "required": ["local_ip_address"],
                                            "type": "object",
                                        },
                                    },
                                }
                            )
                        ],
                    ),
                ),
                (
                    "vendor_name",
                    models.CharField(blank=True, max_length=1024, null=True),
                ),
                (
                    "support_name",
                    models.CharField(blank=True, max_length=1024, null=True),
                ),
                (
                    "support_phone",
                    models.CharField(
                        default="",
                        max_length=14,
                        validators=[
                            django.core.validators.RegexValidator(
                                code="invalid_mobile",
                                message="Please Enter 10/11 digit mobile/landline/tollfree number",
                                regex="^((\\+91|91|0)[\\- ]{0,1})?[456789]\\d{9}|1800\\d{6,7}$",
                            )
                        ],
                    ),
                ),
                (
                    "support_email",
                    models.EmailField(blank=True, max_length=254, null=True),
                ),
                (
                    "qr_code_id",
                    models.CharField(
                        blank=True, default=None, max_length=1024, null=True
                    ),
                ),
                (
                    "manufacturer",
                    models.CharField(blank=True, max_length=1024, null=True),
                ),
                (
                    "warranty_amc_end_of_validity",
                    models.DateField(blank=True, default=None, null=True),
                ),
                (
                    "last_serviced_on",
                    models.DateField(blank=True, default=None, null=True),
                ),
                ("notes", models.TextField(blank=True, default="", null=True)),
            ],
        ),
        migrations.CreateModel(
            name="AssetBed",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "meta",
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=dict
                    ),
                ),
                (
                    "asset",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="facility.Asset"
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="AssetLocation",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("name", models.CharField(max_length=1024)),
                ("description", models.TextField(blank=True, default="", null=True)),
                (
                    "location_type",
                    models.IntegerField(choices=[(1, "OTHER"), (10, "ICU")], default=1),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(
                models.Model,
                # care.facility.models.mixins.permissions.asset.AssetsPermissionMixin,
            ),
        ),
        migrations.CreateModel(
            name="Bed",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("name", models.CharField(max_length=1024)),
                ("description", models.TextField(blank=True, default="")),
                (
                    "bed_type",
                    models.IntegerField(
                        choices=[
                            (1, "ISOLATION"),
                            (2, "ICU"),
                            (3, "ICU_WITH_NON_INVASIVE_VENTILATOR"),
                            (4, "ICU_WITH_OXYGEN_SUPPORT"),
                            (5, "ICU_WITH_INVASIVE_VENTILATOR"),
                            (6, "BED_WITH_OXYGEN_SUPPORT"),
                            (7, "REGULAR"),
                        ],
                        default=7,
                    ),
                ),
                (
                    "meta",
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=dict
                    ),
                ),
                (
                    "assets",
                    models.ManyToManyField(
                        through="facility.AssetBed", to="facility.Asset"
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Building",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("name", models.CharField(max_length=1000)),
                (
                    "num_rooms",
                    models.IntegerField(
                        default=0,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "num_floors",
                    models.IntegerField(
                        default=0,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "num_buildings",
                    models.IntegerField(
                        default=0,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="ConsultationBed",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("start_date", models.DateTimeField()),
                ("end_date", models.DateTimeField(blank=True, default=None, null=True)),
                (
                    "meta",
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=dict
                    ),
                ),
                (
                    "bed",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="facility.Bed"
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="DailyRound",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "temperature",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=None,
                        max_digits=5,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(95),
                            django.core.validators.MaxValueValidator(106),
                        ],
                    ),
                ),
                (
                    "spo2",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=None,
                        max_digits=4,
                        null=True,
                    ),
                ),
                (
                    "temperature_measured_at",
                    models.DateTimeField(blank=True, null=True),
                ),
                ("physical_examination_info", models.TextField(blank=True, null=True)),
                (
                    "additional_symptoms",
                    models.CharField(
                        blank=True,
                        choices=[
                            (1, "ASYMPTOMATIC"),
                            (2, "FEVER"),
                            (3, "SORE THROAT"),
                            (4, "COUGH"),
                            (5, "BREATHLESSNESS"),
                            (6, "MYALGIA"),
                            (7, "ABDOMINAL DISCOMFORT"),
                            (8, "VOMITING"),
                            (9, "OTHERS"),
                            (11, "SPUTUM"),
                            (12, "NAUSEA"),
                            (13, "CHEST PAIN"),
                            (14, "HEMOPTYSIS"),
                            (15, "NASAL DISCHARGE"),
                            (16, "BODY ACHE"),
                            (17, "DIARRHOEA"),
                            (18, "PAIN"),
                            (19, "PEDAL EDEMA"),
                            (20, "WOUND"),
                            (21, "CONSTIPATION"),
                            (22, "HEAD ACHE"),
                            (23, "BLEEDING"),
                            (24, "DIZZINESS"),
                        ],
                        default=1,
                        max_length=59,
                        null=True,
                    ),
                ),
                ("other_symptoms", models.TextField(blank=True, default="")),
                (
                    "deprecated_covid_category",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("ASYM", "ASYMPTOMATIC"),
                            ("Mild", "Category-A"),
                            ("Moderate", "Category-B"),
                            ("Severe", "Category-C"),
                            (None, "UNCLASSIFIED"),
                        ],
                        default=None,
                        max_length=8,
                        null=True,
                    ),
                ),
                (
                    "patient_category",
                    models.CharField(
                        choices=[
                            ("Comfort", "Comfort Care"),
                            ("Stable", "Stable"),
                            ("Moderate", "Abnormal"),
                            ("Critical", "Critical"),
                        ],
                        max_length=8,
                        null=True,
                    ),
                ),
                (
                    "current_health",
                    models.IntegerField(
                        blank=True,
                        choices=[
                            (0, "NO DATA"),
                            (1, "REQUIRES VENTILATOR"),
                            (2, "WORSE"),
                            (3, "STATUS QUO"),
                            (4, "BETTER"),
                        ],
                        default=0,
                    ),
                ),
                (
                    "recommend_discharge",
                    models.BooleanField(
                        default=False, verbose_name="Recommend Discharging Patient"
                    ),
                ),
                ("other_details", models.TextField(blank=True, null=True)),
                (
                    "medication_given",
                    django.contrib.postgres.fields.jsonb.JSONField(default=dict),
                ),
                ("last_updated_by_telemedicine", models.BooleanField(default=False)),
                ("created_by_telemedicine", models.BooleanField(default=False)),
                (
                    "taken_at",
                    models.DateTimeField(blank=True, db_index=True, null=True),
                ),
                (
                    "rounds_type",
                    models.IntegerField(
                        choices=[
                            (0, "NORMAL"),
                            (100, "VENTILATOR"),
                            (200, "ICU"),
                            (300, "AUTOMATED"),
                        ],
                        default=0,
                    ),
                ),
                (
                    "consciousness_level",
                    models.IntegerField(
                        choices=[
                            (0, "UNKNOWN"),
                            (5, "ALERT"),
                            (10, "DROWSY"),
                            (15, "STUPOROUS"),
                            (20, "COMATOSE"),
                            (25, "CANNOT_BE_ASSESSED"),
                        ],
                        default=0,
                    ),
                ),
                (
                    "consciousness_level_detail",
                    models.TextField(blank=True, default=None, null=True),
                ),
                (
                    "in_prone_position",
                    models.BooleanField(blank=True, default=None, null=True),
                ),
                (
                    "left_pupil_size",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(8),
                        ],
                        verbose_name="Left Pupil Size",
                    ),
                ),
                (
                    "left_pupil_size_detail",
                    models.TextField(blank=True, default=None, null=True),
                ),
                (
                    "left_pupil_light_reaction",
                    models.IntegerField(
                        choices=[
                            (0, "UNKNOWN"),
                            (5, "BRISK"),
                            (10, "SLUGGISH"),
                            (15, "FIXED"),
                            (20, "CANNOT_BE_ASSESSED"),
                        ],
                        default=0,
                    ),
                ),
                (
                    "left_pupil_light_reaction_detail",
                    models.TextField(blank=True, default=None, null=True),
                ),
                (
                    "right_pupil_size",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(8),
                        ],
                        verbose_name="Right Pupil Size",
                    ),
                ),
                (
                    "right_pupil_size_detail",
                    models.TextField(blank=True, default=None, null=True),
                ),
                (
                    "right_pupil_light_reaction",
                    models.IntegerField(
                        choices=[
                            (0, "UNKNOWN"),
                            (5, "BRISK"),
                            (10, "SLUGGISH"),
                            (15, "FIXED"),
                            (20, "CANNOT_BE_ASSESSED"),
                        ],
                        default=0,
                    ),
                ),
                (
                    "right_pupil_light_reaction_detail",
                    models.TextField(blank=True, default=None, null=True),
                ),
                (
                    "glasgow_eye_open",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(4),
                        ],
                    ),
                ),
                (
                    "glasgow_verbal_response",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                    ),
                ),
                (
                    "glasgow_motor_response",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(6),
                        ],
                    ),
                ),
                (
                    "glasgow_total_calculated",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(3),
                            django.core.validators.MaxValueValidator(15),
                        ],
                    ),
                ),
                (
                    "limb_response_upper_extremity_right",
                    models.IntegerField(
                        choices=[
                            (0, "UNKNOWN"),
                            (5, "STRONG"),
                            (10, "MODERATE"),
                            (15, "WEAK"),
                            (20, "FLEXION"),
                            (25, "EXTENSION"),
                            (30, "NONE"),
                        ],
                        default=0,
                    ),
                ),
                (
                    "limb_response_upper_extremity_left",
                    models.IntegerField(
                        choices=[
                            (0, "UNKNOWN"),
                            (5, "STRONG"),
                            (10, "MODERATE"),
                            (15, "WEAK"),
                            (20, "FLEXION"),
                            (25, "EXTENSION"),
                            (30, "NONE"),
                        ],
                        default=0,
                    ),
                ),
                (
                    "limb_response_lower_extremity_left",
                    models.IntegerField(
                        choices=[
                            (0, "UNKNOWN"),
                            (5, "STRONG"),
                            (10, "MODERATE"),
                            (15, "WEAK"),
                            (20, "FLEXION"),
                            (25, "EXTENSION"),
                            (30, "NONE"),
                        ],
                        default=0,
                    ),
                ),
                (
                    "limb_response_lower_extremity_right",
                    models.IntegerField(
                        choices=[
                            (0, "UNKNOWN"),
                            (5, "STRONG"),
                            (10, "MODERATE"),
                            (15, "WEAK"),
                            (20, "FLEXION"),
                            (25, "EXTENSION"),
                            (30, "NONE"),
                        ],
                        default=0,
                    ),
                ),
                (
                    "bp",
                    django.contrib.postgres.fields.jsonb.JSONField(
                        default=dict,
                        validators=[
                            care.utils.models.validators.JSONFieldSchemaValidator(
                                {
                                    "$schema": "http://json-schema.org/draft-07/schema#",
                                    "additionalProperties": False,
                                    "properties": {
                                        "diastolic": {"type": "number"},
                                        "mean": {"type": "number"},
                                        "systolic": {"type": "number"},
                                    },
                                    "type": "object",
                                }
                            )
                        ],
                    ),
                ),
                (
                    "pulse",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(200),
                        ],
                    ),
                ),
                (
                    "resp",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(10),
                            django.core.validators.MaxValueValidator(70),
                        ],
                    ),
                ),
                (
                    "rhythm",
                    models.IntegerField(
                        choices=[(0, "UNKNOWN"), (5, "REGULAR"), (10, "IRREGULAR")],
                        default=0,
                    ),
                ),
                (
                    "rhythm_detail",
                    models.TextField(blank=True, default=None, null=True),
                ),
                (
                    "ventilator_interface",
                    models.IntegerField(
                        choices=[
                            (0, "UNKNOWN"),
                            (5, "INVASIVE"),
                            (10, "NON_INVASIVE"),
                            (15, "OXYGEN_SUPPORT"),
                        ],
                        default=0,
                    ),
                ),
                (
                    "ventilator_mode",
                    models.IntegerField(
                        choices=[
                            (0, "UNKNOWN"),
                            (5, "VCV"),
                            (10, "PCV"),
                            (15, "PRVC"),
                            (20, "APRV"),
                            (25, "VC_SIMV"),
                            (30, "PC_SIMV"),
                            (40, "PRVC_SIMV"),
                            (45, "ASV"),
                            (50, "PSV"),
                        ],
                        default=0,
                    ),
                ),
                (
                    "ventilator_peep",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=None,
                        max_digits=4,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(30),
                        ],
                    ),
                ),
                (
                    "ventilator_pip",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                (
                    "ventilator_mean_airway_pressure",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(40),
                        ],
                    ),
                ),
                (
                    "ventilator_resp_rate",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                (
                    "ventilator_pressure_support",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(40),
                        ],
                    ),
                ),
                (
                    "ventilator_tidal_volume",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(1000),
                        ],
                    ),
                ),
                (
                    "ventilator_oxygen_modality",
                    models.IntegerField(
                        choices=[
                            (0, "UNKNOWN"),
                            (5, "NASAL_PRONGS"),
                            (10, "SIMPLE_FACE_MASK"),
                            (15, "NON_REBREATHING_MASK"),
                            (20, "HIGH_FLOW_NASAL_CANNULA"),
                        ],
                        default=0,
                    ),
                ),
                (
                    "ventilator_oxygen_modality_oxygen_rate",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(50),
                        ],
                    ),
                ),
                (
                    "ventilator_oxygen_modality_flow_rate",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(70),
                        ],
                    ),
                ),
                (
                    "ventilator_fi02",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(21),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                (
                    "ventilator_spo2",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                (
                    "etco2",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(200),
                        ],
                    ),
                ),
                (
                    "bilateral_air_entry",
                    models.BooleanField(blank=True, default=None, null=True),
                ),
                (
                    "pain",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(10),
                        ],
                    ),
                ),
                (
                    "pain_scale_enhanced",
                    django.contrib.postgres.fields.jsonb.JSONField(
                        default=list,
                        validators=[
                            care.utils.models.validators.JSONFieldSchemaValidator(
                                {
                                    "$schema": "http://json-schema.org/draft-07/schema#",
                                    "items": [
                                        {
                                            "additionalProperties": False,
                                            "properties": {
                                                "description": {"type": "string"},
                                                "region": {"type": "string"},
                                                "scale": {
                                                    "maximum": 5,
                                                    "minimum": 1,
                                                    "type": "number",
                                                },
                                            },
                                            "required": ["region", "scale"],
                                            "type": "object",
                                        }
                                    ],
                                    "type": "array",
                                }
                            )
                        ],
                    ),
                ),
                (
                    "ph",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=None,
                        max_digits=4,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(10),
                        ],
                    ),
                ),
                (
                    "pco2",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(10),
                            django.core.validators.MaxValueValidator(200),
                        ],
                    ),
                ),
                (
                    "po2",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(10),
                            django.core.validators.MaxValueValidator(400),
                        ],
                    ),
                ),
                (
                    "hco3",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=None,
                        max_digits=4,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(5),
                            django.core.validators.MaxValueValidator(80),
                        ],
                    ),
                ),
                (
                    "base_excess",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(-20),
                            django.core.validators.MaxValueValidator(20),
                        ],
                    ),
                ),
                (
                    "lactate",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=None,
                        max_digits=4,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(20),
                        ],
                    ),
                ),
                (
                    "sodium",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=None,
                        max_digits=5,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(100),
                            django.core.validators.MaxValueValidator(170),
                        ],
                    ),
                ),
                (
                    "potassium",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=None,
                        max_digits=4,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(10),
                        ],
                    ),
                ),
                (
                    "blood_sugar_level",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(700),
                        ],
                    ),
                ),
                (
                    "insulin_intake_dose",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=None,
                        max_digits=5,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                (
                    "insulin_intake_frequency",
                    models.IntegerField(
                        choices=[(0, "UNKNOWN"), (5, "OD"), (10, "BD"), (15, "TD")],
                        default=0,
                    ),
                ),
                (
                    "infusions",
                    django.contrib.postgres.fields.jsonb.JSONField(
                        default=list,
                        validators=[
                            care.utils.models.validators.JSONFieldSchemaValidator(
                                {
                                    "$schema": "http://json-schema.org/draft-07/schema#",
                                    "items": [
                                        {
                                            "additionalProperties": False,
                                            "properties": {
                                                "conc_unit": {"type": "string"},
                                                "concentration": {"type": "number"},
                                                "name": {"type": "string"},
                                                "quantity": {"type": "number"},
                                            },
                                            "required": ["name", "quantity"],
                                            "type": "object",
                                        }
                                    ],
                                    "type": "array",
                                }
                            )
                        ],
                    ),
                ),
                (
                    "iv_fluids",
                    django.contrib.postgres.fields.jsonb.JSONField(
                        default=list,
                        validators=[
                            care.utils.models.validators.JSONFieldSchemaValidator(
                                {
                                    "$schema": "http://json-schema.org/draft-07/schema#",
                                    "items": [
                                        {
                                            "additionalProperties": False,
                                            "properties": {
                                                "name": {"type": "string"},
                                                "quantity": {"type": "number"},
                                            },
                                            "required": ["name", "quantity"],
                                            "type": "object",
                                        }
                                    ],
                                    "type": "array",
                                }
                            )
                        ],
                    ),
                ),
                (
                    "feeds",
                    django.contrib.postgres.fields.jsonb.JSONField(
                        default=list,
                        validators=[
                            care.utils.models.validators.JSONFieldSchemaValidator(
                                {
                                    "$schema": "http://json-schema.org/draft-07/schema#",
                                    "items": [
                                        {
                                            "additionalProperties": False,
                                            "properties": {
                                                "calories": {"type": "number"},
                                                "name": {"type": "string"},
                                                "quantity": {"type": "number"},
                                            },
                                            "required": ["name", "quantity"],
                                            "type": "object",
                                        }
                                    ],
                                    "type": "array",
                                }
                            )
                        ],
                    ),
                ),
                (
                    "total_intake_calculated",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=None,
                        max_digits=6,
                        null=True,
                    ),
                ),
                (
                    "output",
                    django.contrib.postgres.fields.jsonb.JSONField(
                        default=list,
                        validators=[
                            care.utils.models.validators.JSONFieldSchemaValidator(
                                {
                                    "$schema": "http://json-schema.org/draft-07/schema#",
                                    "items": [
                                        {
                                            "additionalProperties": False,
                                            "properties": {
                                                "name": {"type": "string"},
                                                "quantity": {"type": "number"},
                                            },
                                            "required": ["name", "quantity"],
                                            "type": "object",
                                        }
                                    ],
                                    "type": "array",
                                }
                            )
                        ],
                    ),
                ),
                (
                    "total_output_calculated",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=None,
                        max_digits=6,
                        null=True,
                    ),
                ),
                (
                    "dialysis_fluid_balance",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(5000),
                        ],
                    ),
                ),
                (
                    "dialysis_net_balance",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(5000),
                        ],
                    ),
                ),
                (
                    "pressure_sore",
                    django.contrib.postgres.fields.jsonb.JSONField(
                        default=list,
                        validators=[
                            care.utils.models.validators.JSONFieldSchemaValidator(
                                {
                                    "$schema": "http://json-schema.org/draft-07/schema#",
                                    "items": [
                                        {
                                            "additionalProperties": False,
                                            "properties": {
                                                "base_score": {"type": "number"},
                                                "description": {"type": "string"},
                                                "exudate_amount": {
                                                    "enum": [
                                                        "None",
                                                        "Light",
                                                        "Moderate",
                                                        "Heavy",
                                                    ]
                                                },
                                                "length": {"type": "number"},
                                                "push_score": {"type": "number"},
                                                "region": {"type": "string"},
                                                "scale": {
                                                    "maximum": 5,
                                                    "minimum": 1,
                                                    "type": "number",
                                                },
                                                "tissue_type": {
                                                    "enum": [
                                                        "Closed",
                                                        "Epithelial",
                                                        "Granulation",
                                                        "Slough",
                                                        "Necrotic",
                                                    ]
                                                },
                                                "width": {"type": "number"},
                                            },
                                            "required": [],
                                            "type": "object",
                                        }
                                    ],
                                    "type": "array",
                                }
                            )
                        ],
                    ),
                ),
                (
                    "nursing",
                    django.contrib.postgres.fields.jsonb.JSONField(
                        default=list,
                        validators=[
                            care.utils.models.validators.JSONFieldSchemaValidator(
                                {
                                    "$schema": "http://json-schema.org/draft-07/schema#",
                                    "items": [
                                        {
                                            "additionalProperties": False,
                                            "properties": {
                                                "description": {"type": "string"},
                                                "procedure": {"type": "string"},
                                            },
                                            "required": ["procedure", "description"],
                                            "type": "object",
                                        }
                                    ],
                                    "type": "array",
                                }
                            )
                        ],
                    ),
                ),
                (
                    "medicine_administration",
                    django.contrib.postgres.fields.jsonb.JSONField(default=list),
                ),
                (
                    "meta",
                    django.contrib.postgres.fields.jsonb.JSONField(
                        default=dict,
                        validators=[
                            care.utils.models.validators.JSONFieldSchemaValidator(
                                {
                                    "$schema": "http://json-schema.org/draft-07/schema#",
                                    "additionalProperties": False,
                                    "properties": {"dialysis": {"type": "boolean"}},
                                    "type": "object",
                                }
                            )
                        ],
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Facility",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("name", models.CharField(max_length=1000)),
                ("is_active", models.BooleanField(default=True)),
                ("verified", models.BooleanField(default=False)),
                (
                    "facility_type",
                    models.IntegerField(
                        choices=[
                            (1, "Educational Inst"),
                            (2, "Private Hospital"),
                            (3, "Other"),
                            (4, "Hostel"),
                            (5, "Hotel"),
                            (6, "Lodge"),
                            (7, "TeleMedicine"),
                            (8, "Govt Hospital"),
                            (9, "Labs"),
                            (800, "Primary Health Centres"),
                            (801, "24x7 Public Health Centres"),
                            (802, "Family Health Centres"),
                            (803, "Community Health Centres"),
                            (820, "Urban Primary Health Center"),
                            (830, "Taluk Hospitals"),
                            (831, "Taluk Headquarters Hospitals"),
                            (840, "Women and Child Health Centres"),
                            (850, "General hospitals"),
                            (860, "District Hospitals"),
                            (870, "Govt Medical College Hospitals"),
                            (900, "Co-operative hospitals"),
                            (910, "Autonomous healthcare facility"),
                            (950, "Corona Testing Labs"),
                            (1000, "Corona Care Centre"),
                            (1010, "COVID-19 Domiciliary Care Center"),
                            (1100, "First Line Treatment Centre"),
                            (1200, "Second Line Treatment Center"),
                            (1300, "Shifting Centre"),
                            (1400, "Covid Management Center"),
                            (1500, "Request Approving Center"),
                            (1510, "Request Fulfilment Center"),
                            (1600, "District War Room"),
                        ]
                    ),
                ),
                ("kasp_empanelled", models.BooleanField(default=False)),
                (
                    "features",
                    models.CharField(
                        blank=True,
                        choices=[
                            (1, "CT Scan Facility"),
                            (2, "Maternity Care"),
                            (3, "X-Ray facility"),
                            (4, "Neonatal care"),
                            (5, "Operation theater"),
                            (6, "Blood Bank"),
                        ],
                        max_length=11,
                        null=True,
                    ),
                ),
                (
                    "longitude",
                    models.DecimalField(
                        blank=True, decimal_places=16, max_digits=22, null=True
                    ),
                ),
                (
                    "latitude",
                    models.DecimalField(
                        blank=True, decimal_places=16, max_digits=22, null=True
                    ),
                ),
                ("pincode", models.IntegerField(default=None, null=True)),
                ("address", models.TextField()),
                ("oxygen_capacity", models.IntegerField(default=0)),
                ("type_b_cylinders", models.IntegerField(default=0)),
                ("type_c_cylinders", models.IntegerField(default=0)),
                ("type_d_cylinders", models.IntegerField(default=0)),
                ("expected_oxygen_requirement", models.IntegerField(default=0)),
                ("expected_type_b_cylinders", models.IntegerField(default=0)),
                ("expected_type_c_cylinders", models.IntegerField(default=0)),
                ("expected_type_d_cylinders", models.IntegerField(default=0)),
                (
                    "phone_number",
                    models.CharField(
                        blank=True,
                        max_length=14,
                        validators=[
                            django.core.validators.RegexValidator(
                                code="invalid_mobile",
                                message="Please Enter 10/11 digit mobile number or landline as 0<std code><phone number>",
                                regex="^((\\+91|91|0)[\\- ]{0,1})?[456789]\\d{9}$",
                            )
                        ],
                    ),
                ),
                ("corona_testing", models.BooleanField(default=False)),
                (
                    "cover_image_url",
                    models.CharField(
                        blank=True, default=None, max_length=500, null=True
                    ),
                ),
                (
                    "middleware_address",
                    models.CharField(default=None, max_length=200, null=True),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "district",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="users.District",
                    ),
                ),
                (
                    "local_body",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="users.LocalBody",
                    ),
                ),
                (
                    "state",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="users.State",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Facilities",
            },
            bases=(
                models.Model,
                care.facility.models.mixins.permissions.facility.FacilityPermissionMixin,
            ),
        ),
        migrations.CreateModel(
            name="FacilityInventoryItem",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=1000)),
                ("description", models.TextField(blank=True)),
                ("min_quantity", models.FloatField()),
            ],
        ),
        migrations.CreateModel(
            name="FacilityInventoryItemTag",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name="FacilityInventoryUnit",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name="Inventory",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "quantitiy",
                    models.IntegerField(
                        default=0,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "facility",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="facility.Facility",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Inventories",
            },
        ),
        migrations.CreateModel(
            name="InventoryItem",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("name", models.CharField(max_length=1000)),
                ("description", models.TextField()),
                (
                    "minimum_stock",
                    models.IntegerField(
                        default=0,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                ("unit", models.CharField(max_length=20)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="InvestigationSession",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="MetaICD11Diagnosis",
            fields=[
                (
                    "id",
                    models.CharField(max_length=255, primary_key=True, serialize=False),
                ),
                ("_id", models.IntegerField()),
                ("average_depth", models.IntegerField()),
                ("is_adopted_child", models.BooleanField()),
                ("parent_id", models.CharField(max_length=255, null=True)),
                ("class_kind", models.CharField(max_length=255)),
                ("is_leaf", models.BooleanField()),
                ("label", models.CharField(max_length=255)),
                (
                    "breadth_value",
                    models.DecimalField(decimal_places=22, max_digits=24),
                ),
            ],
            options={
                "db_table": "meta_icd11_diagnosis",
            },
        ),
        migrations.CreateModel(
            name="PatientConsultation",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "ip_no",
                    models.CharField(blank=True, default="", max_length=100, null=True),
                ),
                (
                    "op_no",
                    models.CharField(blank=True, default="", max_length=100, null=True),
                ),
                ("diagnosis", models.TextField(blank=True, default="", null=True)),
                (
                    "icd11_provisional_diagnoses",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=100),
                        blank=True,
                        default=[],
                        null=True,
                        size=None,
                    ),
                ),
                (
                    "icd11_diagnoses",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=100),
                        blank=True,
                        default=[],
                        null=True,
                        size=None,
                    ),
                ),
                (
                    "symptoms",
                    models.CharField(
                        blank=True,
                        choices=[
                            (1, "ASYMPTOMATIC"),
                            (2, "FEVER"),
                            (3, "SORE THROAT"),
                            (4, "COUGH"),
                            (5, "BREATHLESSNESS"),
                            (6, "MYALGIA"),
                            (7, "ABDOMINAL DISCOMFORT"),
                            (8, "VOMITING"),
                            (9, "OTHERS"),
                            (11, "SPUTUM"),
                            (12, "NAUSEA"),
                            (13, "CHEST PAIN"),
                            (14, "HEMOPTYSIS"),
                            (15, "NASAL DISCHARGE"),
                            (16, "BODY ACHE"),
                            (17, "DIARRHOEA"),
                            (18, "PAIN"),
                            (19, "PEDAL EDEMA"),
                            (20, "WOUND"),
                            (21, "CONSTIPATION"),
                            (22, "HEAD ACHE"),
                            (23, "BLEEDING"),
                            (24, "DIZZINESS"),
                        ],
                        default=1,
                        max_length=59,
                        null=True,
                    ),
                ),
                ("other_symptoms", models.TextField(blank=True, default="")),
                ("symptoms_onset_date", models.DateTimeField(blank=True, null=True)),
                (
                    "deprecated_covid_category",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("ASYM", "ASYMPTOMATIC"),
                            ("Mild", "Category-A"),
                            ("Moderate", "Category-B"),
                            ("Severe", "Category-C"),
                            (None, "UNCLASSIFIED"),
                        ],
                        default=None,
                        max_length=8,
                        null=True,
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("Comfort", "Comfort Care"),
                            ("Stable", "Stable"),
                            ("Moderate", "Abnormal"),
                            ("Critical", "Critical"),
                        ],
                        max_length=8,
                        null=True,
                    ),
                ),
                ("examination_details", models.TextField(blank=True, null=True)),
                ("history_of_present_illness", models.TextField(blank=True, null=True)),
                ("prescribed_medication", models.TextField(blank=True, null=True)),
                ("consultation_notes", models.TextField(blank=True, null=True)),
                ("course_in_facility", models.TextField(blank=True, null=True)),
                (
                    "investigation",
                    django.contrib.postgres.fields.jsonb.JSONField(default=dict),
                ),
                (
                    "prescriptions",
                    django.contrib.postgres.fields.jsonb.JSONField(default=dict),
                ),
                (
                    "procedure",
                    django.contrib.postgres.fields.jsonb.JSONField(default=dict),
                ),
                (
                    "suggestion",
                    models.CharField(
                        choices=[
                            ("HI", "HOME ISOLATION"),
                            ("A", "ADMISSION"),
                            ("R", "REFERRAL"),
                            ("OP", "OP CONSULTATION"),
                            ("DC", "DOMICILIARY CARE"),
                            ("DD", "DECLARE DEATH"),
                        ],
                        max_length=4,
                    ),
                ),
                (
                    "consultation_status",
                    models.IntegerField(
                        choices=[
                            (0, "UNKNOWN"),
                            (1, "BROUGHT_DEAD"),
                            (2, "TRANSFERRED_FROM_WARD"),
                            (3, "TRANSFERRED_FROM_ICU"),
                            (4, "REFERRED_FROM_OTHER_HOSPITAL"),
                            (5, "OUT_PATIENT"),
                        ],
                        default=0,
                    ),
                ),
                ("review_interval", models.IntegerField(default=-1)),
                (
                    "referred_to_external",
                    models.TextField(blank=True, default="", null=True),
                ),
                ("admitted", models.BooleanField(default=False)),
                ("admission_date", models.DateTimeField(blank=True, null=True)),
                ("discharge_date", models.DateTimeField(blank=True, null=True)),
                (
                    "discharge_reason",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("REC", "Recovered"),
                            ("REF", "Referred"),
                            ("EXP", "Expired"),
                            ("LAMA", "LAMA"),
                        ],
                        default=None,
                        max_length=4,
                        null=True,
                    ),
                ),
                (
                    "discharge_notes",
                    models.TextField(blank=True, default="", null=True),
                ),
                (
                    "discharge_prescription",
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=dict, null=True
                    ),
                ),
                (
                    "discharge_prn_prescription",
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=dict, null=True
                    ),
                ),
                ("death_datetime", models.DateTimeField(blank=True, null=True)),
                (
                    "death_confirmed_doctor",
                    models.TextField(blank=True, default="", null=True),
                ),
                ("bed_number", models.CharField(blank=True, max_length=100, null=True)),
                ("is_kasp", models.BooleanField(default=False)),
                (
                    "kasp_enabled_date",
                    models.DateTimeField(blank=True, default=None, null=True),
                ),
                ("is_telemedicine", models.BooleanField(default=False)),
                ("last_updated_by_telemedicine", models.BooleanField(default=False)),
                ("verified_by", models.TextField(blank=True, default="", null=True)),
                (
                    "height",
                    models.FloatField(
                        default=None,
                        null=True,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="Patient's Height in CM",
                    ),
                ),
                (
                    "weight",
                    models.FloatField(
                        default=None,
                        null=True,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="Patient's Weight in KG",
                    ),
                ),
                (
                    "HBA1C",
                    models.FloatField(
                        default=None,
                        null=True,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="HBA1C parameter for reference to current blood sugar levels",
                    ),
                ),
                ("operation", models.TextField(blank=True, default=None, null=True)),
                (
                    "special_instruction",
                    models.TextField(blank=True, default=None, null=True),
                ),
                (
                    "intubation_history",
                    django.contrib.postgres.fields.jsonb.JSONField(default=list),
                ),
                (
                    "prn_prescription",
                    django.contrib.postgres.fields.jsonb.JSONField(default=dict),
                ),
                (
                    "discharge_advice",
                    django.contrib.postgres.fields.jsonb.JSONField(default=dict),
                ),
                (
                    "assigned_to",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="patient_assigned_to",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_user",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "current_bed",
                    models.ForeignKey(
                        blank=True,
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="facility.ConsultationBed",
                    ),
                ),
                (
                    "facility",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="consultations",
                        to="facility.Facility",
                    ),
                ),
                (
                    "last_daily_round",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="facility.DailyRound",
                    ),
                ),
                (
                    "last_edited_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="last_edited_user",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            bases=(
                models.Model,
                care.facility.models.mixins.permissions.patient.ConsultationRelatedPermissionMixin,
            ),
        ),
        migrations.CreateModel(
            name="PatientInvestigationGroup",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("name", models.CharField(max_length=500)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="PatientMetaInfo",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "occupation",
                    models.IntegerField(
                        choices=[
                            (1, "STUDENT"),
                            (2, "MEDICAL_WORKER"),
                            (3, "GOVT_EMPLOYEE"),
                            (4, "PRIVATE_EMPLOYEE"),
                            (5, "HOME_MAKER"),
                            (6, "WORKING_ABROAD"),
                            (7, "OTHERS"),
                        ]
                    ),
                ),
                ("head_of_household", models.BooleanField()),
            ],
        ),
        migrations.CreateModel(
            name="PatientMobileOTP",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("is_used", models.BooleanField(default=False)),
                (
                    "phone_number",
                    models.CharField(
                        max_length=14,
                        validators=[
                            django.core.validators.RegexValidator(
                                code="invalid_mobile",
                                message="Please Enter 10/11 digit mobile number or landline as 0<std code><phone number>",
                                regex="^((\\+91|91|0)[\\- ]{0,1})?[456789]\\d{9}$",
                            )
                        ],
                    ),
                ),
                ("otp", models.CharField(max_length=10)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="PatientRegistration",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "source",
                    models.IntegerField(
                        choices=[(10, "CARE"), (20, "COVID_TRACKER"), (30, "STAY")],
                        default=10,
                    ),
                ),
                ("name", models.CharField(default="", max_length=200)),
                ("age", models.PositiveIntegerField(blank=True, null=True)),
                (
                    "gender",
                    models.IntegerField(
                        choices=[(1, "Male"), (2, "Female"), (3, "Non-binary")]
                    ),
                ),
                (
                    "phone_number",
                    models.CharField(
                        default="",
                        max_length=14,
                        validators=[
                            django.core.validators.RegexValidator(
                                code="invalid_mobile",
                                message="Please Enter 10/11 digit mobile number or landline as 0<std code><phone number>",
                                regex="^((\\+91|91|0)[\\- ]{0,1})?[456789]\\d{9}$",
                            )
                        ],
                    ),
                ),
                (
                    "emergency_phone_number",
                    models.CharField(
                        default="",
                        max_length=14,
                        validators=[
                            django.core.validators.RegexValidator(
                                code="invalid_mobile",
                                message="Please Enter 10/11 digit mobile number or landline as 0<std code><phone number>",
                                regex="^((\\+91|91|0)[\\- ]{0,1})?[456789]\\d{9}$",
                            )
                        ],
                    ),
                ),
                ("address", models.TextField(default="")),
                ("permanent_address", models.TextField(default="")),
                ("pincode", models.IntegerField(blank=True, default=0, null=True)),
                ("date_of_birth", models.DateField(default=None, null=True)),
                ("year_of_birth", models.IntegerField(default=0, null=True)),
                (
                    "nationality",
                    models.CharField(
                        default="",
                        max_length=255,
                        verbose_name="Nationality of Patient",
                    ),
                ),
                (
                    "passport_no",
                    models.CharField(
                        default="",
                        max_length=255,
                        verbose_name="Passport Number of Foreign Patients",
                    ),
                ),
                (
                    "is_medical_worker",
                    models.BooleanField(
                        default=False, verbose_name="Is the Patient a Medical Worker"
                    ),
                ),
                (
                    "blood_group",
                    models.CharField(
                        choices=[
                            ("A+", "A+"),
                            ("A-", "A-"),
                            ("B+", "B+"),
                            ("B-", "B-"),
                            ("AB+", "AB+"),
                            ("AB-", "AB-"),
                            ("O+", "O+"),
                            ("O-", "O-"),
                            ("UNK", "UNKNOWN"),
                        ],
                        max_length=4,
                        null=True,
                        verbose_name="Blood Group of Patient",
                    ),
                ),
                (
                    "contact_with_confirmed_carrier",
                    models.BooleanField(
                        default=False,
                        verbose_name="Confirmed Contact with a Covid19 Carrier",
                    ),
                ),
                (
                    "contact_with_suspected_carrier",
                    models.BooleanField(
                        default=False,
                        verbose_name="Suspected Contact with a Covid19 Carrier",
                    ),
                ),
                ("estimated_contact_date", models.DateTimeField(blank=True, null=True)),
                (
                    "past_travel",
                    models.BooleanField(
                        default=False,
                        verbose_name="Travelled to Any Foreign Countries in the last 28 Days",
                    ),
                ),
                (
                    "countries_travelled_old",
                    models.TextField(
                        blank=True,
                        editable=False,
                        null=True,
                        verbose_name="Countries Patient has Travelled to",
                    ),
                ),
                (
                    "countries_travelled",
                    django.contrib.postgres.fields.JSONField(
                        blank=True,
                        null=True,
                        verbose_name="Countries Patient has Travelled to",
                    ),
                ),
                (
                    "date_of_return",
                    models.DateTimeField(
                        blank=True,
                        null=True,
                        verbose_name="Return Date from the Last Country if Travelled",
                    ),
                ),
                (
                    "allergies",
                    models.TextField(
                        blank=True, default="", verbose_name="Patient's Known Allergies"
                    ),
                ),
                (
                    "present_health",
                    models.TextField(
                        blank=True,
                        default="",
                        verbose_name="Patient's Current Health Details",
                    ),
                ),
                (
                    "ongoing_medication",
                    models.TextField(
                        blank=True,
                        default="",
                        verbose_name="Already pescribed medication if any",
                    ),
                ),
                (
                    "has_SARI",
                    models.BooleanField(
                        default=False, verbose_name="Does the Patient Suffer from SARI"
                    ),
                ),
                (
                    "is_antenatal",
                    models.BooleanField(
                        default=None,
                        verbose_name="Does the patient require Prenatal Care ?",
                    ),
                ),
                (
                    "ward_old",
                    models.CharField(
                        default="", max_length=255, verbose_name="Ward of Patient"
                    ),
                ),
                (
                    "is_migrant_worker",
                    models.BooleanField(
                        default=False, verbose_name="Is Patient a Migrant Worker"
                    ),
                ),
                (
                    "disease_status",
                    models.IntegerField(
                        blank=True,
                        choices=[
                            (1, "SUSPECTED"),
                            (2, "POSITIVE"),
                            (3, "NEGATIVE"),
                            (4, "RECOVERY"),
                            (5, "RECOVERED"),
                            (6, "EXPIRED"),
                        ],
                        default=1,
                        verbose_name="Disease Status",
                    ),
                ),
                (
                    "number_of_aged_dependents",
                    models.IntegerField(
                        blank=True,
                        default=0,
                        verbose_name="Number of people aged above 60 living with the patient",
                    ),
                ),
                (
                    "number_of_chronic_diseased_dependents",
                    models.IntegerField(
                        blank=True,
                        default=0,
                        verbose_name="Number of people who have chronic diseases living with the patient",
                    ),
                ),
                (
                    "action",
                    models.IntegerField(
                        blank=True,
                        choices=[
                            (10, "NO_ACTION"),
                            (20, "PENDING"),
                            (30, "SPECIALIST_REQUIRED"),
                            (40, "PLAN_FOR_HOME_CARE"),
                            (50, "FOLLOW_UP_NOT_REQUIRED"),
                            (60, "COMPLETE"),
                            (70, "REVIEW"),
                            (80, "NOT_REACHABLE"),
                        ],
                        default=10,
                        null=True,
                    ),
                ),
                (
                    "review_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Patient's next review time"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Not active when discharged, or removed from the watchlist",
                    ),
                ),
                (
                    "patient_search_id",
                    models.IntegerField(help_text="FKey to PatientSearch", null=True),
                ),
                (
                    "date_of_receipt_of_information",
                    models.DateTimeField(
                        blank=True,
                        null=True,
                        verbose_name="Patient's information received date",
                    ),
                ),
                (
                    "test_id",
                    models.CharField(blank=True, default="", max_length=100, null=True),
                ),
                (
                    "date_of_test",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Patient's test Date"
                    ),
                ),
                ("srf_id", models.CharField(blank=True, default="", max_length=200)),
                (
                    "test_type",
                    models.IntegerField(
                        choices=[
                            (10, "UNK"),
                            (20, "ANTIGEN"),
                            (30, "RTPCR"),
                            (40, "CBNAAT"),
                            (50, "TRUENAT"),
                            (60, "RTLAMP"),
                            (70, "POCPCR"),
                        ],
                        default=10,
                    ),
                ),
                ("allow_transfer", models.BooleanField(default=False)),
                (
                    "will_donate_blood",
                    models.BooleanField(
                        default=None,
                        null=True,
                        verbose_name="Is Patient Willing to donate Blood",
                    ),
                ),
                (
                    "fit_for_blood_donation",
                    models.BooleanField(
                        default=None,
                        null=True,
                        verbose_name="Is Patient fit for donating Blood",
                    ),
                ),
                (
                    "village",
                    models.CharField(
                        blank=True,
                        default=None,
                        max_length=255,
                        null=True,
                        verbose_name="Vilalge Name of Patient (IDSP Req)",
                    ),
                ),
                (
                    "designation_of_health_care_worker",
                    models.CharField(
                        blank=True,
                        default=None,
                        max_length=255,
                        null=True,
                        verbose_name="Designation of Health Care Worker (IDSP Req)",
                    ),
                ),
                (
                    "instituion_of_health_care_worker",
                    models.CharField(
                        blank=True,
                        default=None,
                        max_length=255,
                        null=True,
                        verbose_name="Institution of Healtcare Worker (IDSP Req)",
                    ),
                ),
                (
                    "transit_details",
                    models.CharField(
                        blank=True,
                        default=None,
                        max_length=255,
                        null=True,
                        verbose_name="Transit Details (IDSP Req)",
                    ),
                ),
                (
                    "frontline_worker",
                    models.CharField(
                        blank=True,
                        default=None,
                        max_length=255,
                        null=True,
                        verbose_name="Front Line Worker (IDSP Req)",
                    ),
                ),
                (
                    "date_of_result",
                    models.DateTimeField(
                        blank=True,
                        default=None,
                        null=True,
                        verbose_name="Patient's result Date",
                    ),
                ),
                (
                    "number_of_primary_contacts",
                    models.IntegerField(
                        blank=True,
                        default=None,
                        null=True,
                        verbose_name="Number of Primary Contacts",
                    ),
                ),
                (
                    "number_of_secondary_contacts",
                    models.IntegerField(
                        blank=True,
                        default=None,
                        null=True,
                        verbose_name="Number of Secondary Contacts",
                    ),
                ),
                (
                    "is_vaccinated",
                    models.BooleanField(
                        default=False,
                        verbose_name="Is the Patient Vaccinated Against COVID-19",
                    ),
                ),
                (
                    "number_of_doses",
                    models.PositiveIntegerField(
                        default=0,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(3),
                        ],
                    ),
                ),
                (
                    "vaccine_name",
                    models.CharField(
                        choices=[
                            ("CoviShield", "COVISHIELD"),
                            ("Covaxin", "COVAXIN"),
                            ("Sputnik", "SPUTNIK"),
                            ("Moderna", "MODERNA"),
                            ("Pfizer", "PFIZER"),
                            ("Janssen", "JANSSEN"),
                            ("Sinovac", "SINOVAC"),
                        ],
                        default=None,
                        max_length=15,
                        null=True,
                    ),
                ),
                (
                    "covin_id",
                    models.CharField(
                        blank=True,
                        default=None,
                        max_length=15,
                        null=True,
                        verbose_name="COVID-19 Vaccination ID",
                    ),
                ),
                (
                    "last_vaccinated_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Date Last Vaccinated"
                    ),
                ),
                (
                    "cluster_name",
                    models.CharField(
                        blank=True,
                        default=None,
                        max_length=255,
                        null=True,
                        verbose_name="Name/ Cluster of Contact",
                    ),
                ),
                (
                    "is_declared_positive",
                    models.BooleanField(
                        default=None,
                        null=True,
                        verbose_name="Is Patient Declared Positive",
                    ),
                ),
                (
                    "date_declared_positive",
                    models.DateTimeField(
                        blank=True,
                        null=True,
                        verbose_name="Date Patient is Declared Positive",
                    ),
                ),
                (
                    "assigned_to",
                    models.ForeignKey(
                        blank="True",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="root_patient_assigned_to",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="patient_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "district",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="users.District",
                    ),
                ),
                (
                    "facility",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="facility.Facility",
                    ),
                ),
                (
                    "last_consultation",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="facility.PatientConsultation",
                    ),
                ),
                (
                    "last_edited",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="patient_last_edited_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "local_body",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="users.LocalBody",
                    ),
                ),
                (
                    "meta_info",
                    models.OneToOneField(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="facility.PatientMetaInfo",
                    ),
                ),
                (
                    "nearest_facility",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="nearest_facility",
                        to="facility.Facility",
                    ),
                ),
                (
                    "state",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="users.State",
                    ),
                ),
                (
                    "ward",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="users.Ward",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(
                models.Model,
                care.facility.models.mixins.permissions.patient.PatientPermissionMixin,
            ),
        ),
        migrations.CreateModel(
            name="PatientSample",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "sample_type",
                    models.IntegerField(
                        choices=[
                            (0, "UNKNOWN"),
                            (1, "BA/ETA"),
                            (2, "TS/NPS/NS"),
                            (3, "Blood in EDTA"),
                            (4, "Acute Sera"),
                            (5, "Covalescent sera"),
                            (6, "Biopsy"),
                            (7, "AMR"),
                            (8, "Communicable Diseases"),
                            (9, "OTHER TYPE"),
                        ],
                        default=0,
                    ),
                ),
                ("sample_type_other", models.TextField(default="")),
                ("has_sari", models.BooleanField(default=False)),
                ("has_ari", models.BooleanField(default=False)),
                (
                    "doctor_name",
                    models.CharField(default="NO DOCTOR SPECIFIED", max_length=255),
                ),
                ("diagnosis", models.TextField(default="")),
                ("diff_diagnosis", models.TextField(default="")),
                ("etiology_identified", models.TextField(default="")),
                ("is_atypical_presentation", models.BooleanField(default=False)),
                ("atypical_presentation", models.TextField(default="")),
                ("is_unusual_course", models.BooleanField(default=False)),
                (
                    "icmr_category",
                    models.IntegerField(
                        choices=[
                            (0, "Cat 0"),
                            (10, "Cat 1"),
                            (20, "Cat 2"),
                            (30, "Cat 3"),
                            (40, "Cat 4"),
                            (50, "Cat 5a"),
                            (60, "Cat 5b"),
                        ],
                        default=0,
                    ),
                ),
                ("icmr_label", models.CharField(default="", max_length=200)),
                (
                    "status",
                    models.IntegerField(
                        choices=[
                            (1, "REQUEST_SUBMITTED"),
                            (2, "APPROVED"),
                            (3, "DENIED"),
                            (4, "SENT_TO_COLLECTON_CENTRE"),
                            (5, "RECEIVED_AND_FORWARED"),
                            (6, "RECEIVED_AT_LAB"),
                            (7, "COMPLETED"),
                        ],
                        default=1,
                    ),
                ),
                (
                    "result",
                    models.IntegerField(
                        choices=[
                            (1, "POSITIVE"),
                            (2, "NEGATIVE"),
                            (3, "AWAITING"),
                            (4, "INVALID"),
                        ],
                        default=3,
                    ),
                ),
                ("fast_track", models.TextField(default="")),
                ("date_of_sample", models.DateTimeField(blank=True, null=True)),
                ("date_of_result", models.DateTimeField(blank=True, null=True)),
                (
                    "consultation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="facility.PatientConsultation",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="samples_created",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "last_edited_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="last_edited_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="facility.PatientRegistration",
                    ),
                ),
                (
                    "testing_facility",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="facility.Facility",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="ResourceRequest",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("emergency", models.BooleanField(default=False)),
                ("title", models.CharField(max_length=255)),
                ("reason", models.TextField(blank=True, default="")),
                (
                    "refering_facility_contact_name",
                    models.TextField(blank=True, default=""),
                ),
                (
                    "refering_facility_contact_number",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=14,
                        validators=[
                            django.core.validators.RegexValidator(
                                code="invalid_mobile",
                                message="Please Enter 10/11 digit mobile number or landline as 0<std code><phone number>",
                                regex="^((\\+91|91|0)[\\- ]{0,1})?[456789]\\d{9}$",
                            )
                        ],
                    ),
                ),
                (
                    "status",
                    models.IntegerField(
                        choices=[
                            (10, "PENDING"),
                            (15, "ON HOLD"),
                            (20, "APPROVED"),
                            (30, "REJECTED"),
                            (55, "TRANSPORTATION TO BE ARRANGED"),
                            (70, "TRANSFER IN PROGRESS"),
                            (80, "COMPLETED"),
                        ],
                        default=10,
                    ),
                ),
                (
                    "category",
                    models.IntegerField(
                        choices=[(100, "OXYGEN"), (200, "SUPPLIES")], default=100
                    ),
                ),
                (
                    "sub_category",
                    models.IntegerField(
                        choices=[
                            (110, "LIQUID OXYGEN"),
                            (120, "B TYPE OXYGEN CYLINDER"),
                            (130, "C TYPE OXYGEN CYLINDER"),
                            (140, "JUMBO D TYPE OXYGEN CYLINDER"),
                            (1000, "UNSPECIFIED"),
                        ],
                        default=1000,
                    ),
                ),
                ("priority", models.IntegerField(blank=True, default=None, null=True)),
                ("requested_quantity", models.IntegerField(default=0)),
                ("assigned_quantity", models.IntegerField(default=0)),
                ("is_assigned_to_user", models.BooleanField(default=False)),
                (
                    "approving_facility",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resource_approving_facility",
                        to="facility.Facility",
                    ),
                ),
                (
                    "assigned_facility",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resource_assigned_facility",
                        to="facility.Facility",
                    ),
                ),
                (
                    "assigned_to",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resource_request_assigned_to",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resource_request_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "last_edited_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resource_request_last_edited_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "orgin_facility",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="resource_requesting_facility",
                        to="facility.Facility",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Room",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("num", models.CharField(max_length=1000)),
                (
                    "floor",
                    models.IntegerField(
                        default=0,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "beds_capacity",
                    models.IntegerField(
                        default=0,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "occupied_beds",
                    models.IntegerField(
                        default=0,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "room_type",
                    models.IntegerField(
                        choices=[
                            (0, "Total"),
                            (2, "Hostel"),
                            (3, "Single Room with Attached Bathroom"),
                            (40, "KASP Beds"),
                            (50, "KASP ICU beds"),
                            (60, "KASP Oxygen beds"),
                            (70, "KASP Ventilator beds"),
                            (1, "General Bed"),
                            (10, "ICU"),
                            (20, "Ventilator"),
                            (30, "Covid Beds"),
                            (100, "Covid Ventilators"),
                            (110, "Covid ICU"),
                            (120, "Covid Oxygen beds"),
                            (150, "Oxygen beds"),
                        ]
                    ),
                ),
                (
                    "building",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="facility.Building",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="ShiftingRequest",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "assigned_facility_type",
                    models.IntegerField(
                        blank=True,
                        choices=[
                            (1, "Educational Inst"),
                            (2, "Private Hospital"),
                            (3, "Other"),
                            (4, "Hostel"),
                            (5, "Hotel"),
                            (6, "Lodge"),
                            (7, "TeleMedicine"),
                            (8, "Govt Hospital"),
                            (9, "Labs"),
                            (800, "Primary Health Centres"),
                            (801, "24x7 Public Health Centres"),
                            (802, "Family Health Centres"),
                            (803, "Community Health Centres"),
                            (820, "Urban Primary Health Center"),
                            (830, "Taluk Hospitals"),
                            (831, "Taluk Headquarters Hospitals"),
                            (840, "Women and Child Health Centres"),
                            (850, "General hospitals"),
                            (860, "District Hospitals"),
                            (870, "Govt Medical College Hospitals"),
                            (900, "Co-operative hospitals"),
                            (910, "Autonomous healthcare facility"),
                            (950, "Corona Testing Labs"),
                            (1000, "Corona Care Centre"),
                            (1010, "COVID-19 Domiciliary Care Center"),
                            (1100, "First Line Treatment Centre"),
                            (1200, "Second Line Treatment Center"),
                            (1300, "Shifting Centre"),
                            (1400, "Covid Management Center"),
                            (1500, "Request Approving Center"),
                            (1510, "Request Fulfilment Center"),
                            (1600, "District War Room"),
                        ],
                        default=None,
                        null=True,
                    ),
                ),
                (
                    "assigned_facility_external",
                    models.TextField(blank=True, default="", null=True),
                ),
                ("emergency", models.BooleanField(default=False)),
                ("is_up_shift", models.BooleanField(default=False)),
                ("reason", models.TextField(blank=True, default="")),
                ("vehicle_preference", models.TextField(blank=True, default="")),
                (
                    "preferred_vehicle_choice",
                    models.IntegerField(
                        blank=True,
                        choices=[
                            (10, "D Level Ambulance"),
                            (20, "All double chambered Ambulance with EMT"),
                            (30, "Ambulance without EMT"),
                            (40, "Car"),
                            (50, "Auto-rickshaw"),
                        ],
                        default=None,
                        null=True,
                    ),
                ),
                ("comments", models.TextField(blank=True, default="")),
                (
                    "refering_facility_contact_name",
                    models.TextField(blank=True, default=""),
                ),
                (
                    "refering_facility_contact_number",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=14,
                        validators=[
                            django.core.validators.RegexValidator(
                                code="invalid_mobile",
                                message="Please Enter 10/11 digit mobile number or landline as 0<std code><phone number>",
                                regex="^((\\+91|91|0)[\\- ]{0,1})?[456789]\\d{9}$",
                            )
                        ],
                    ),
                ),
                ("is_kasp", models.BooleanField(default=False)),
                (
                    "status",
                    models.IntegerField(
                        choices=[
                            (10, "PENDING"),
                            (15, "ON HOLD"),
                            (20, "APPROVED"),
                            (30, "REJECTED"),
                            (40, "DESTINATION APPROVED"),
                            (50, "DESTINATION REJECTED"),
                            (55, "TRANSPORTATION TO BE ARRANGED"),
                            (60, "PATIENT TO BE PICKED UP"),
                            (70, "TRANSFER IN PROGRESS"),
                            (80, "COMPLETED"),
                            (90, "PATIENT EXPIRED"),
                            (100, "CANCELLED"),
                        ],
                        default=10,
                    ),
                ),
                (
                    "breathlessness_level",
                    models.IntegerField(
                        blank=True,
                        choices=[
                            (10, "NOT SPECIFIED"),
                            (15, "NOT BREATHLESS"),
                            (20, "MILD"),
                            (30, "MODERATE"),
                            (40, "SEVERE"),
                        ],
                        default=10,
                        null=True,
                    ),
                ),
                ("is_assigned_to_user", models.BooleanField(default=False)),
                ("ambulance_driver_name", models.TextField(blank=True, default="")),
                (
                    "ambulance_phone_number",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=14,
                        validators=[
                            django.core.validators.RegexValidator(
                                code="invalid_mobile",
                                message="Please Enter 10/11 digit mobile number or landline as 0<std code><phone number>",
                                regex="^((\\+91|91|0)[\\- ]{0,1})?[456789]\\d{9}$",
                            )
                        ],
                    ),
                ),
                ("ambulance_number", models.TextField(blank=True, default="")),
                (
                    "assigned_facility",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="assigned_facility",
                        to="facility.Facility",
                    ),
                ),
                (
                    "assigned_to",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="shifting_assigned_to",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="shifting_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "last_edited_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="shifting_last_edited_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "orgin_facility",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="requesting_facility",
                        to="facility.Facility",
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="patient",
                        to="facility.PatientRegistration",
                    ),
                ),
                (
                    "shifting_approving_facility",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="shifting_approving_facility",
                        to="facility.Facility",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="UserDefaultAssetLocation",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "location",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="facility.AssetLocation",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="StaffRoomAllocation",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "room",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="facility.Room"
                    ),
                ),
                (
                    "staff",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="ShiftingRequestComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("comment", models.TextField(blank=True, default="")),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "request",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="facility.ShiftingRequest",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="ResourceRequestComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("comment", models.TextField(blank=True, default="")),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "request",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="facility.ResourceRequest",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="PrescriptionSupplier",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "scheme",
                    models.IntegerField(
                        choices=[
                            (10, "GOVERNMENT_SUPPLY"),
                            (30, "DONATION"),
                            (40, "PAID_BY_PATIENT"),
                        ],
                        default=10,
                    ),
                ),
                (
                    "status",
                    models.IntegerField(
                        choices=[
                            (10, "PENDING"),
                            (30, "INITIATED"),
                            (40, "COMPLETED"),
                            (50, "DEFERRED"),
                        ],
                        default=10,
                    ),
                ),
                ("supplier", models.TextField(blank=True, default="")),
                ("remarks", models.TextField(blank=True, default="")),
                (
                    "consultation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="patient_consultation",
                        to="facility.PatientConsultation",
                    ),
                ),
                (
                    "facility",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="facility.Facility",
                    ),
                ),
                (
                    "updated_user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Prescription",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "prescription_type",
                    models.CharField(
                        choices=[("DISCHARGE", "DISCHARGE"), ("REGULAR", "REGULAR")],
                        default="REGULAR",
                        max_length=100,
                    ),
                ),
                ("medicine", models.CharField(max_length=1023)),
                (
                    "route",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("ORAL", "Oral"),
                            ("IV", "IV"),
                            ("IM", "IM"),
                            ("SC", "S/C"),
                        ],
                        max_length=100,
                        null=True,
                    ),
                ),
                ("dosage", models.CharField(blank=True, max_length=100, null=True)),
                ("is_prn", models.BooleanField(default=False)),
                (
                    "frequency",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("STAT", "Immediately"),
                            ("OD", "once daily"),
                            ("HS", "Night only"),
                            ("BD", "Twice daily"),
                            ("TID", "8th hourly"),
                            ("QID", "6th hourly"),
                            ("Q4H", "4th hourly"),
                            ("QOD", "Alternate day"),
                            ("QWK", "Once a week"),
                        ],
                        max_length=100,
                        null=True,
                    ),
                ),
                ("days", models.IntegerField(blank=True, null=True)),
                ("indicator", models.TextField(blank=True, null=True)),
                ("max_dosage", models.CharField(blank=True, max_length=100, null=True)),
                ("min_hours_between_doses", models.IntegerField(blank=True, null=True)),
                ("notes", models.TextField(blank=True, default="")),
                (
                    "meta",
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=dict
                    ),
                ),
                ("discontinued", models.BooleanField(default=False)),
                ("discontinued_reason", models.TextField(blank=True, default="")),
                ("discontinued_date", models.DateTimeField(blank=True, null=True)),
                ("is_migrated", models.BooleanField(default=False)),
                (
                    "consultation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="facility.PatientConsultation",
                    ),
                ),
                (
                    "prescribed_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="PatientTeleConsultation",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "symptoms",
                    models.CharField(
                        choices=[
                            (1, "ASYMPTOMATIC"),
                            (2, "FEVER"),
                            (3, "SORE THROAT"),
                            (4, "COUGH"),
                            (5, "BREATHLESSNESS"),
                            (6, "MYALGIA"),
                            (7, "ABDOMINAL DISCOMFORT"),
                            (8, "VOMITING"),
                            (9, "OTHERS"),
                            (11, "SPUTUM"),
                            (12, "NAUSEA"),
                            (13, "CHEST PAIN"),
                            (14, "HEMOPTYSIS"),
                            (15, "NASAL DISCHARGE"),
                            (16, "BODY ACHE"),
                            (17, "DIARRHOEA"),
                            (18, "PAIN"),
                            (19, "PEDAL EDEMA"),
                            (20, "WOUND"),
                            (21, "CONSTIPATION"),
                            (22, "HEAD ACHE"),
                            (23, "BLEEDING"),
                            (24, "DIZZINESS"),
                        ],
                        max_length=59,
                    ),
                ),
                ("other_symptoms", models.TextField(blank=True, null=True)),
                (
                    "reason",
                    models.TextField(
                        blank=True, null=True, verbose_name="Reason for calling"
                    ),
                ),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="facility.PatientRegistration",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="PatientSearch",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("patient_id", models.IntegerField()),
                ("name", models.CharField(max_length=120)),
                (
                    "gender",
                    models.IntegerField(
                        choices=[(1, "Male"), (2, "Female"), (3, "Non-binary")]
                    ),
                ),
                ("phone_number", models.CharField(max_length=14)),
                ("date_of_birth", models.DateField(null=True)),
                ("year_of_birth", models.IntegerField()),
                ("state_id", models.IntegerField()),
                (
                    "patient_external_id",
                    models.CharField(default="", max_length=100),
                ),
                ("allow_transfer", models.BooleanField(default=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "facility",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="facility.Facility",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="PatientSampleFlow",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "status",
                    models.IntegerField(
                        choices=[
                            (1, "REQUEST_SUBMITTED"),
                            (2, "APPROVED"),
                            (3, "DENIED"),
                            (4, "SENT_TO_COLLECTON_CENTRE"),
                            (5, "RECEIVED_AND_FORWARED"),
                            (6, "RECEIVED_AT_LAB"),
                            (7, "COMPLETED"),
                        ]
                    ),
                ),
                ("notes", models.CharField(max_length=255)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "patient_sample",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="facility.PatientSample",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="PatientNotes",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("note", models.TextField(blank=True, default="")),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "facility",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="facility.Facility",
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="facility.PatientRegistration",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(
                models.Model,
                care.facility.models.mixins.permissions.patient.ConsultationRelatedPermissionMixin,
            ),
        ),
        migrations.CreateModel(
            name="PatientInvestigation",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("name", models.CharField(max_length=500)),
                ("unit", models.TextField(blank=True, null=True)),
                ("ideal_value", models.TextField(blank=True, null=True)),
                ("min_value", models.FloatField(blank=True, default=None, null=True)),
                ("max_value", models.FloatField(blank=True, default=None, null=True)),
                (
                    "investigation_type",
                    models.CharField(
                        choices=[
                            ("Float", "Float"),
                            ("String", "String"),
                            ("Choice", "Choice"),
                        ],
                        default=None,
                        max_length=10,
                        null=True,
                    ),
                ),
                ("choices", models.TextField(blank=True, null=True)),
                (
                    "groups",
                    models.ManyToManyField(to="facility.PatientInvestigationGroup"),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="PatientExternalTest",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("srf_id", models.CharField(max_length=255)),
                ("name", models.CharField(max_length=1000)),
                ("age", models.IntegerField()),
                ("age_in", models.CharField(max_length=20)),
                ("gender", models.CharField(max_length=10)),
                ("address", models.TextField()),
                ("mobile_number", models.CharField(max_length=15)),
                ("is_repeat", models.BooleanField()),
                ("patient_status", models.CharField(max_length=15)),
                ("source", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "patient_category",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("lab_name", models.CharField(max_length=255)),
                ("test_type", models.CharField(max_length=255)),
                ("sample_type", models.CharField(max_length=255)),
                ("result", models.CharField(max_length=255)),
                ("sample_collection_date", models.DateField(blank=True, null=True)),
                ("result_date", models.DateField(blank=True, null=True)),
                ("patient_created", models.BooleanField(default=False)),
                (
                    "district",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="users.District"
                    ),
                ),
                (
                    "local_body",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="users.LocalBody",
                    ),
                ),
                (
                    "ward",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="users.Ward",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="PatientContactDetails",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "relation_with_patient",
                    models.IntegerField(
                        choices=[
                            (1, "FAMILY_MEMBER"),
                            (2, "FRIEND"),
                            (3, "RELATIVE"),
                            (4, "NEIGHBOUR"),
                            (5, "TRAVEL_TOGETHER"),
                            (6, "WHILE_AT_HOSPITAL"),
                            (7, "WHILE_AT_SHOP"),
                            (8, "WHILE_AT_OFFICE_OR_ESTABLISHMENT"),
                            (9, "WORSHIP_PLACE"),
                            (10, "OTHERS"),
                        ]
                    ),
                ),
                (
                    "mode_of_contact",
                    models.IntegerField(
                        choices=[
                            (1, "TOUCHED_BODY_FLUIDS"),
                            (2, "DIRECT_PHYSICAL_CONTACT"),
                            (3, "CLEANED_USED_ITEMS"),
                            (4, "LIVE_IN_SAME_HOUSEHOLD"),
                            (5, "CLOSE_CONTACT_WITHOUT_PRECAUTION"),
                            (6, "CO_PASSENGER_AEROPLANE"),
                            (7, "HEALTH_CARE_WITH_PPE"),
                            (8, "SHARED_SAME_SPACE_WITHOUT_HIGH_EXPOSURE"),
                            (9, "TRAVELLED_TOGETHER_WITHOUT_HIGH_EXPOSURE"),
                        ]
                    ),
                ),
                ("date_of_first_contact", models.DateField(null=True)),
                ("date_of_last_contact", models.DateField(null=True)),
                (
                    "is_primary",
                    models.BooleanField(help_text="If false, then secondary contact"),
                ),
                (
                    "condition_of_contact_is_symptomatic",
                    models.BooleanField(
                        help_text="While in contact, did the patient showing symptoms"
                    ),
                ),
                ("deleted", models.BooleanField(default=False)),
                (
                    "patient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="contacted_patients",
                        to="facility.PatientRegistration",
                    ),
                ),
                (
                    "patient_in_contact",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="contacts",
                        to="facility.PatientRegistration",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="patientconsultation",
            name="patient",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="consultations",
                to="facility.PatientRegistration",
            ),
        ),
        migrations.AddField(
            model_name="patientconsultation",
            name="referred_to",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="referred_patients",
                to="facility.Facility",
            ),
        ),
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "medium_sent",
                    models.IntegerField(
                        choices=[(0, "SYSTEM"), (100, "SMS"), (200, "WHATSAPP")],
                        default=0,
                    ),
                ),
                ("read_at", models.DateTimeField(blank=True, null=True)),
                (
                    "event_type",
                    models.IntegerField(
                        choices=[(50, "SYSTEM_GENERATED"), (100, "CUSTOM_MESSAGE")],
                        default=50,
                    ),
                ),
                (
                    "event",
                    models.IntegerField(
                        choices=[
                            (0, "MESSAGE"),
                            (20, "PATIENT_CREATED"),
                            (30, "PATIENT_UPDATED"),
                            (40, "PATIENT_DELETED"),
                            (50, "PATIENT_CONSULTATION_CREATED"),
                            (60, "PATIENT_CONSULTATION_UPDATED"),
                            (70, "PATIENT_CONSULTATION_DELETED"),
                            (80, "INVESTIGATION_SESSION_CREATED"),
                            (90, "INVESTIGATION_UPDATED"),
                            (100, "PATIENT_FILE_UPLOAD_CREATED"),
                            (110, "CONSULTATION_FILE_UPLOAD_CREATED"),
                            (120, "PATIENT_CONSULTATION_UPDATE_CREATED"),
                            (130, "PATIENT_CONSULTATION_UPDATE_UPDATED"),
                            (140, "PATIENT_CONSULTATION_ASSIGNMENT"),
                            (200, "SHIFTING_UPDATED"),
                        ],
                        default=0,
                    ),
                ),
                ("message", models.TextField(default=None, max_length=2000, null=True)),
                (
                    "caused_objects",
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=dict, null=True
                    ),
                ),
                (
                    "caused_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="notification_caused_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "intended_for",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="notification_intended_for",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="MedicineAdministration",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("notes", models.TextField(blank=True, default="")),
                (
                    "administered_date",
                    models.DateTimeField(default=django.utils.timezone.now),
                ),
                (
                    "administered_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "prescription",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="administrations",
                        to="facility.Prescription",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="LocalBodyScopedSummary",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("created_date", models.DateTimeField(auto_now_add=True, null=True)),
                ("modified_date", models.DateTimeField(auto_now=True, null=True)),
                (
                    "s_type",
                    models.CharField(
                        choices=[("PatientSummary", "PatientSummary")], max_length=100
                    ),
                ),
                (
                    "data",
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=dict, null=True
                    ),
                ),
                (
                    "lsg",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="users.LocalBody",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="InvestigationValue",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("value", models.FloatField(blank=True, default=None, null=True)),
                ("notes", models.TextField(blank=True, default=None, null=True)),
                (
                    "consultation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="facility.PatientConsultation",
                    ),
                ),
                (
                    "group",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="facility.PatientInvestigationGroup",
                    ),
                ),
                (
                    "investigation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="facility.PatientInvestigation",
                    ),
                ),
                (
                    "session",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="facility.InvestigationSession",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="InventoryLog",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "prev_count",
                    models.IntegerField(
                        default=0,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "new_count",
                    models.IntegerField(
                        default=0,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "inventory",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="facility.Inventory",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AddField(
            model_name="inventory",
            name="item",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="facility.InventoryItem"
            ),
        ),
        migrations.CreateModel(
            name="HospitalDoctors",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "area",
                    models.IntegerField(
                        choices=[
                            (1, "General Medicine"),
                            (2, "Pulmonology"),
                            (3, "Critical Care"),
                            (4, "Paediatrics"),
                            (5, "Other Speciality"),
                        ]
                    ),
                ),
                ("count", models.IntegerField()),
                (
                    "facility",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="facility.Facility",
                    ),
                ),
            ],
            bases=(
                models.Model,
                care.facility.models.mixins.permissions.facility.FacilityRelatedPermissionMixin,
            ),
        ),
        migrations.CreateModel(
            name="HistoricalPatientRegistration",
            fields=[
                (
                    "id",
                    models.IntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                ("external_id", models.UUIDField(db_index=True, default=uuid.uuid4)),
                (
                    "created_date",
                    models.DateTimeField(
                        blank=True, db_index=True, editable=False, null=True
                    ),
                ),
                (
                    "modified_date",
                    models.DateTimeField(
                        blank=True, db_index=True, editable=False, null=True
                    ),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "source",
                    models.IntegerField(
                        choices=[(10, "CARE"), (20, "COVID_TRACKER"), (30, "STAY")],
                        default=10,
                    ),
                ),
                ("name", models.CharField(default="", max_length=200)),
                ("age", models.PositiveIntegerField(blank=True, null=True)),
                (
                    "gender",
                    models.IntegerField(
                        choices=[(1, "Male"), (2, "Female"), (3, "Non-binary")]
                    ),
                ),
                (
                    "phone_number",
                    models.CharField(
                        default="",
                        max_length=14,
                        validators=[
                            django.core.validators.RegexValidator(
                                code="invalid_mobile",
                                message="Please Enter 10/11 digit mobile number or landline as 0<std code><phone number>",
                                regex="^((\\+91|91|0)[\\- ]{0,1})?[456789]\\d{9}$",
                            )
                        ],
                    ),
                ),
                (
                    "emergency_phone_number",
                    models.CharField(
                        default="",
                        max_length=14,
                        validators=[
                            django.core.validators.RegexValidator(
                                code="invalid_mobile",
                                message="Please Enter 10/11 digit mobile number or landline as 0<std code><phone number>",
                                regex="^((\\+91|91|0)[\\- ]{0,1})?[456789]\\d{9}$",
                            )
                        ],
                    ),
                ),
                ("address", models.TextField(default="")),
                ("permanent_address", models.TextField(default="")),
                ("pincode", models.IntegerField(blank=True, default=0, null=True)),
                ("date_of_birth", models.DateField(default=None, null=True)),
                ("year_of_birth", models.IntegerField(default=0, null=True)),
                (
                    "nationality",
                    models.CharField(
                        default="",
                        max_length=255,
                        verbose_name="Nationality of Patient",
                    ),
                ),
                (
                    "passport_no",
                    models.CharField(
                        default="",
                        max_length=255,
                        verbose_name="Passport Number of Foreign Patients",
                    ),
                ),
                (
                    "is_medical_worker",
                    models.BooleanField(
                        default=False, verbose_name="Is the Patient a Medical Worker"
                    ),
                ),
                (
                    "blood_group",
                    models.CharField(
                        choices=[
                            ("A+", "A+"),
                            ("A-", "A-"),
                            ("B+", "B+"),
                            ("B-", "B-"),
                            ("AB+", "AB+"),
                            ("AB-", "AB-"),
                            ("O+", "O+"),
                            ("O-", "O-"),
                            ("UNK", "UNKNOWN"),
                        ],
                        max_length=4,
                        null=True,
                        verbose_name="Blood Group of Patient",
                    ),
                ),
                (
                    "contact_with_confirmed_carrier",
                    models.BooleanField(
                        default=False,
                        verbose_name="Confirmed Contact with a Covid19 Carrier",
                    ),
                ),
                (
                    "contact_with_suspected_carrier",
                    models.BooleanField(
                        default=False,
                        verbose_name="Suspected Contact with a Covid19 Carrier",
                    ),
                ),
                ("estimated_contact_date", models.DateTimeField(blank=True, null=True)),
                (
                    "past_travel",
                    models.BooleanField(
                        default=False,
                        verbose_name="Travelled to Any Foreign Countries in the last 28 Days",
                    ),
                ),
                (
                    "countries_travelled_old",
                    models.TextField(
                        blank=True,
                        editable=False,
                        null=True,
                        verbose_name="Countries Patient has Travelled to",
                    ),
                ),
                (
                    "countries_travelled",
                    django.contrib.postgres.fields.JSONField(
                        blank=True,
                        null=True,
                        verbose_name="Countries Patient has Travelled to",
                    ),
                ),
                (
                    "date_of_return",
                    models.DateTimeField(
                        blank=True,
                        null=True,
                        verbose_name="Return Date from the Last Country if Travelled",
                    ),
                ),
                (
                    "allergies",
                    models.TextField(
                        blank=True, default="", verbose_name="Patient's Known Allergies"
                    ),
                ),
                (
                    "present_health",
                    models.TextField(
                        blank=True,
                        default="",
                        verbose_name="Patient's Current Health Details",
                    ),
                ),
                (
                    "ongoing_medication",
                    models.TextField(
                        blank=True,
                        default="",
                        verbose_name="Already pescribed medication if any",
                    ),
                ),
                (
                    "has_SARI",
                    models.BooleanField(
                        default=False, verbose_name="Does the Patient Suffer from SARI"
                    ),
                ),
                (
                    "is_antenatal",
                    models.BooleanField(
                        default=None,
                        verbose_name="Does the patient require Prenatal Care ?",
                    ),
                ),
                (
                    "ward_old",
                    models.CharField(
                        default="", max_length=255, verbose_name="Ward of Patient"
                    ),
                ),
                (
                    "is_migrant_worker",
                    models.BooleanField(
                        default=False, verbose_name="Is Patient a Migrant Worker"
                    ),
                ),
                (
                    "disease_status",
                    models.IntegerField(
                        blank=True,
                        choices=[
                            (1, "SUSPECTED"),
                            (2, "POSITIVE"),
                            (3, "NEGATIVE"),
                            (4, "RECOVERY"),
                            (5, "RECOVERED"),
                            (6, "EXPIRED"),
                        ],
                        default=1,
                        verbose_name="Disease Status",
                    ),
                ),
                (
                    "number_of_aged_dependents",
                    models.IntegerField(
                        blank=True,
                        default=0,
                        verbose_name="Number of people aged above 60 living with the patient",
                    ),
                ),
                (
                    "number_of_chronic_diseased_dependents",
                    models.IntegerField(
                        blank=True,
                        default=0,
                        verbose_name="Number of people who have chronic diseases living with the patient",
                    ),
                ),
                (
                    "action",
                    models.IntegerField(
                        blank=True,
                        choices=[
                            (10, "NO_ACTION"),
                            (20, "PENDING"),
                            (30, "SPECIALIST_REQUIRED"),
                            (40, "PLAN_FOR_HOME_CARE"),
                            (50, "FOLLOW_UP_NOT_REQUIRED"),
                            (60, "COMPLETE"),
                            (70, "REVIEW"),
                            (80, "NOT_REACHABLE"),
                        ],
                        default=10,
                        null=True,
                    ),
                ),
                (
                    "review_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Patient's next review time"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Not active when discharged, or removed from the watchlist",
                    ),
                ),
                (
                    "date_of_receipt_of_information",
                    models.DateTimeField(
                        blank=True,
                        null=True,
                        verbose_name="Patient's information received date",
                    ),
                ),
                (
                    "test_id",
                    models.CharField(blank=True, default="", max_length=100, null=True),
                ),
                (
                    "date_of_test",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Patient's test Date"
                    ),
                ),
                ("srf_id", models.CharField(blank=True, default="", max_length=200)),
                (
                    "test_type",
                    models.IntegerField(
                        choices=[
                            (10, "UNK"),
                            (20, "ANTIGEN"),
                            (30, "RTPCR"),
                            (40, "CBNAAT"),
                            (50, "TRUENAT"),
                            (60, "RTLAMP"),
                            (70, "POCPCR"),
                        ],
                        default=10,
                    ),
                ),
                ("allow_transfer", models.BooleanField(default=False)),
                (
                    "will_donate_blood",
                    models.BooleanField(
                        default=None,
                        null=True,
                        verbose_name="Is Patient Willing to donate Blood",
                    ),
                ),
                (
                    "fit_for_blood_donation",
                    models.BooleanField(
                        default=None,
                        null=True,
                        verbose_name="Is Patient fit for donating Blood",
                    ),
                ),
                (
                    "village",
                    models.CharField(
                        blank=True,
                        default=None,
                        max_length=255,
                        null=True,
                        verbose_name="Vilalge Name of Patient (IDSP Req)",
                    ),
                ),
                (
                    "designation_of_health_care_worker",
                    models.CharField(
                        blank=True,
                        default=None,
                        max_length=255,
                        null=True,
                        verbose_name="Designation of Health Care Worker (IDSP Req)",
                    ),
                ),
                (
                    "instituion_of_health_care_worker",
                    models.CharField(
                        blank=True,
                        default=None,
                        max_length=255,
                        null=True,
                        verbose_name="Institution of Healtcare Worker (IDSP Req)",
                    ),
                ),
                (
                    "transit_details",
                    models.CharField(
                        blank=True,
                        default=None,
                        max_length=255,
                        null=True,
                        verbose_name="Transit Details (IDSP Req)",
                    ),
                ),
                (
                    "frontline_worker",
                    models.CharField(
                        blank=True,
                        default=None,
                        max_length=255,
                        null=True,
                        verbose_name="Front Line Worker (IDSP Req)",
                    ),
                ),
                (
                    "date_of_result",
                    models.DateTimeField(
                        blank=True,
                        default=None,
                        null=True,
                        verbose_name="Patient's result Date",
                    ),
                ),
                (
                    "number_of_primary_contacts",
                    models.IntegerField(
                        blank=True,
                        default=None,
                        null=True,
                        verbose_name="Number of Primary Contacts",
                    ),
                ),
                (
                    "number_of_secondary_contacts",
                    models.IntegerField(
                        blank=True,
                        default=None,
                        null=True,
                        verbose_name="Number of Secondary Contacts",
                    ),
                ),
                (
                    "is_vaccinated",
                    models.BooleanField(
                        default=False,
                        verbose_name="Is the Patient Vaccinated Against COVID-19",
                    ),
                ),
                (
                    "number_of_doses",
                    models.PositiveIntegerField(
                        default=0,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(3),
                        ],
                    ),
                ),
                (
                    "vaccine_name",
                    models.CharField(
                        choices=[
                            ("CoviShield", "COVISHIELD"),
                            ("Covaxin", "COVAXIN"),
                            ("Sputnik", "SPUTNIK"),
                            ("Moderna", "MODERNA"),
                            ("Pfizer", "PFIZER"),
                            ("Janssen", "JANSSEN"),
                            ("Sinovac", "SINOVAC"),
                        ],
                        default=None,
                        max_length=15,
                        null=True,
                    ),
                ),
                (
                    "covin_id",
                    models.CharField(
                        blank=True,
                        default=None,
                        max_length=15,
                        null=True,
                        verbose_name="COVID-19 Vaccination ID",
                    ),
                ),
                (
                    "last_vaccinated_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Date Last Vaccinated"
                    ),
                ),
                (
                    "cluster_name",
                    models.CharField(
                        blank=True,
                        default=None,
                        max_length=255,
                        null=True,
                        verbose_name="Name/ Cluster of Contact",
                    ),
                ),
                (
                    "is_declared_positive",
                    models.BooleanField(
                        default=None,
                        null=True,
                        verbose_name="Is Patient Declared Positive",
                    ),
                ),
                (
                    "date_declared_positive",
                    models.DateTimeField(
                        blank=True,
                        null=True,
                        verbose_name="Date Patient is Declared Positive",
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField()),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "assigned_to",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "district",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="users.District",
                    ),
                ),
                (
                    "facility",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="facility.Facility",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "last_consultation",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="facility.PatientConsultation",
                    ),
                ),
                (
                    "last_edited",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "local_body",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="users.LocalBody",
                    ),
                ),
                (
                    "nearest_facility",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="facility.Facility",
                    ),
                ),
                (
                    "state",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="users.State",
                    ),
                ),
                (
                    "ward",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="users.Ward",
                    ),
                ),
            ],
            options={
                "verbose_name": "historical patient registration",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": "history_date",
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="HistoricalFacilityCapacity",
            fields=[
                (
                    "id",
                    models.IntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                ("external_id", models.UUIDField(db_index=True, default=uuid.uuid4)),
                (
                    "created_date",
                    models.DateTimeField(
                        blank=True, db_index=True, editable=False, null=True
                    ),
                ),
                (
                    "modified_date",
                    models.DateTimeField(
                        blank=True, db_index=True, editable=False, null=True
                    ),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "room_type",
                    models.IntegerField(
                        choices=[
                            (0, "Total"),
                            (2, "Hostel"),
                            (3, "Single Room with Attached Bathroom"),
                            (40, "KASP Beds"),
                            (50, "KASP ICU beds"),
                            (60, "KASP Oxygen beds"),
                            (70, "KASP Ventilator beds"),
                            (1, "General Bed"),
                            (10, "ICU"),
                            (20, "Ventilator"),
                            (30, "Covid Beds"),
                            (100, "Covid Ventilators"),
                            (110, "Covid ICU"),
                            (120, "Covid Oxygen beds"),
                            (150, "Oxygen beds"),
                        ]
                    ),
                ),
                (
                    "total_capacity",
                    models.IntegerField(
                        default=0,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "current_capacity",
                    models.IntegerField(
                        default=0,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField()),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "facility",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="facility.Facility",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "historical facility capacity",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": "history_date",
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="FileUpload",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("name", models.CharField(max_length=2000)),
                ("internal_name", models.CharField(max_length=2000)),
                ("associating_id", models.CharField(max_length=100)),
                ("upload_completed", models.BooleanField(default=False)),
                ("is_archived", models.BooleanField(default=False)),
                ("archive_reason", models.TextField(blank=True)),
                ("archived_datetime", models.DateTimeField(blank=True, null=True)),
                (
                    "file_type",
                    models.IntegerField(
                        choices=[
                            (1, "PATIENT"),
                            (2, "CONSULTATION"),
                            (3, "SAMPLE_MANAGEMENT"),
                            (4, "CLAIM"),
                            (5, "DISCHARGE_SUMMARY"),
                        ],
                        default=1,
                    ),
                ),
                (
                    "file_category",
                    models.CharField(
                        choices=[
                            ("UNSPECIFIED", "UNSPECIFIED"),
                            ("XRAY", "XRAY"),
                            ("AUDIO", "AUDIO"),
                            ("IDENTITY_PROOF", "IDENTITY_PROOF"),
                        ],
                        default="UNSPECIFIED",
                        max_length=100,
                    ),
                ),
                (
                    "archived_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="archived_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "uploaded_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="uploaded_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="FacilityVolunteer",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "facility",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="facility.Facility",
                    ),
                ),
                (
                    "volunteer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="FacilityUser",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="created_users",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "facility",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="facility.Facility",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="FacilityStaff",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "facility",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="facility.Facility",
                    ),
                ),
                (
                    "staff",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="FacilityRelatedSummary",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("created_date", models.DateTimeField(auto_now_add=True, null=True)),
                ("modified_date", models.DateTimeField(auto_now=True, null=True)),
                (
                    "s_type",
                    models.CharField(
                        choices=[
                            ("FacilityCapacity", "FacilityCapacity"),
                            ("PatientSummary", "PatientSummary"),
                            ("TestSummary", "TestSummary"),
                            ("TriageSummary", "TriageSummary"),
                        ],
                        max_length=100,
                    ),
                ),
                (
                    "data",
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=dict, null=True
                    ),
                ),
                (
                    "facility",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="facility.Facility",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="FacilityPatientStatsHistory",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("entry_date", models.DateField()),
                ("num_patients_visited", models.IntegerField(default=0)),
                ("num_patients_home_quarantine", models.IntegerField(default=0)),
                ("num_patients_isolation", models.IntegerField(default=0)),
                ("num_patient_referred", models.IntegerField(default=0)),
                ("num_patient_confirmed_positive", models.IntegerField(default=0)),
                (
                    "facility",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="facility.Facility",
                    ),
                ),
            ],
            bases=(
                models.Model,
                care.facility.models.mixins.permissions.facility.FacilityRelatedPermissionMixin,
            ),
        ),
        migrations.CreateModel(
            name="FacilityLocalGovtBody",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "district",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="users.District",
                    ),
                ),
                (
                    "facility",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="facility.Facility",
                    ),
                ),
                (
                    "local_body",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="users.LocalBody",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="FacilityInventoryUnitConverter",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("multiplier", models.FloatField()),
                (
                    "from_unit",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="from_unit",
                        to="facility.FacilityInventoryUnit",
                    ),
                ),
                (
                    "to_unit",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="to_unit",
                        to="facility.FacilityInventoryUnit",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="FacilityInventorySummary",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("quantity", models.FloatField(default=0)),
                ("is_low", models.BooleanField(default=False)),
                (
                    "facility",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="facility.Facility",
                    ),
                ),
                (
                    "item",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="facility.FacilityInventoryItem",
                    ),
                ),
            ],
            bases=(
                models.Model,
                care.facility.models.mixins.permissions.facility.FacilityRelatedPermissionMixin,
            ),
        ),
        migrations.CreateModel(
            name="FacilityInventoryMinQuantity",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("min_quantity", models.FloatField(default=0)),
                (
                    "facility",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="facility.Facility",
                    ),
                ),
                (
                    "item",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="facility.FacilityInventoryItem",
                    ),
                ),
            ],
            bases=(
                models.Model,
                care.facility.models.mixins.permissions.facility.FacilityRelatedPermissionMixin,
            ),
        ),
        migrations.CreateModel(
            name="FacilityInventoryLog",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("current_stock", models.FloatField(default=0)),
                ("quantity_in_default_unit", models.FloatField(default=0)),
                ("quantity", models.FloatField(default=0)),
                ("is_incoming", models.BooleanField()),
                ("probable_accident", models.BooleanField(default=False)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "facility",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="facility.Facility",
                    ),
                ),
                (
                    "item",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="facility.FacilityInventoryItem",
                    ),
                ),
                (
                    "unit",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="facility.FacilityInventoryUnit",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(
                models.Model,
                care.facility.models.mixins.permissions.facility.FacilityRelatedPermissionMixin,
            ),
        ),
        migrations.AddField(
            model_name="facilityinventoryitem",
            name="allowed_units",
            field=models.ManyToManyField(
                related_name="allowed_units", to="facility.FacilityInventoryUnit"
            ),
        ),
        migrations.AddField(
            model_name="facilityinventoryitem",
            name="default_unit",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="default_unit",
                to="facility.FacilityInventoryUnit",
            ),
        ),
        migrations.AddField(
            model_name="facilityinventoryitem",
            name="tags",
            field=models.ManyToManyField(to="facility.FacilityInventoryItemTag"),
        ),
        migrations.CreateModel(
            name="FacilityInventoryBurnRate",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("burn_rate", models.FloatField(default=0)),
                ("current_stock", models.FloatField(default=0)),
                (
                    "facility",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="facility.Facility",
                    ),
                ),
                (
                    "item",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="facility.FacilityInventoryItem",
                    ),
                ),
            ],
            bases=(
                models.Model,
                care.facility.models.mixins.permissions.facility.FacilityRelatedPermissionMixin,
            ),
        ),
        migrations.CreateModel(
            name="FacilityDefaultAssetLocation",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "facility",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="facility.Facility",
                    ),
                ),
                (
                    "location",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="facility.AssetLocation",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="FacilityCapacity",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "room_type",
                    models.IntegerField(
                        choices=[
                            (0, "Total"),
                            (2, "Hostel"),
                            (3, "Single Room with Attached Bathroom"),
                            (40, "KASP Beds"),
                            (50, "KASP ICU beds"),
                            (60, "KASP Oxygen beds"),
                            (70, "KASP Ventilator beds"),
                            (1, "General Bed"),
                            (10, "ICU"),
                            (20, "Ventilator"),
                            (30, "Covid Beds"),
                            (100, "Covid Ventilators"),
                            (110, "Covid ICU"),
                            (120, "Covid Oxygen beds"),
                            (150, "Oxygen beds"),
                        ]
                    ),
                ),
                (
                    "total_capacity",
                    models.IntegerField(
                        default=0,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "current_capacity",
                    models.IntegerField(
                        default=0,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "facility",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="facility.Facility",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Facility Capacities",
            },
            bases=(
                models.Model,
                care.facility.models.mixins.permissions.facility.FacilityRelatedPermissionMixin,
            ),
        ),
        migrations.AddField(
            model_name="facility",
            name="users",
            field=models.ManyToManyField(
                related_name="facilities",
                through="facility.FacilityUser",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="facility",
            name="ward",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="users.Ward",
            ),
        ),
        migrations.CreateModel(
            name="DistrictScopedSummary",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("created_date", models.DateTimeField(auto_now_add=True, null=True)),
                ("modified_date", models.DateTimeField(auto_now=True, null=True)),
                (
                    "s_type",
                    models.CharField(
                        choices=[("PatientSummary", "PatientSummary")], max_length=100
                    ),
                ),
                (
                    "data",
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=dict, null=True
                    ),
                ),
                (
                    "district",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="users.District",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Disease",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "disease",
                    models.IntegerField(
                        choices=[
                            (1, "NO"),
                            (2, "Diabetes"),
                            (3, "Heart Disease"),
                            (4, "HyperTension"),
                            (5, "Kidney Diseases"),
                            (6, "Lung Diseases/Asthma"),
                            (7, "Cancer"),
                            (8, "OTHER"),
                        ]
                    ),
                ),
                ("details", models.TextField(blank=True, null=True)),
                ("deleted", models.BooleanField(default=False)),
                (
                    "patient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="medical_history",
                        to="facility.PatientRegistration",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="dailyround",
            name="consultation",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="daily_rounds",
                to="facility.PatientConsultation",
            ),
        ),
        migrations.AddField(
            model_name="dailyround",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="update_created_user",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="dailyround",
            name="last_edited_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="update_last_edited_user",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="consultationbed",
            name="consultation",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                to="facility.PatientConsultation",
            ),
        ),
        migrations.AddField(
            model_name="building",
            name="facility",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="facility.Facility"
            ),
        ),
        migrations.AddField(
            model_name="bed",
            name="facility",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="facility.Facility"
            ),
        ),
        migrations.AddField(
            model_name="bed",
            name="location",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="facility.AssetLocation"
            ),
        ),
        migrations.CreateModel(
            name="AssetTransaction",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "asset",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="facility.Asset"
                    ),
                ),
                (
                    "from_location",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="from_location",
                        to="facility.AssetLocation",
                    ),
                ),
                (
                    "performed_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "to_location",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="to_location",
                        to="facility.AssetLocation",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AddField(
            model_name="assetlocation",
            name="facility",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="facility.Facility"
            ),
        ),
        migrations.AddField(
            model_name="assetbed",
            name="bed",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="facility.Bed"
            ),
        ),
        migrations.AddField(
            model_name="asset",
            name="current_location",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="facility.AssetLocation"
            ),
        ),
        migrations.CreateModel(
            name="AmbulanceDriver",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("name", models.CharField(max_length=255)),
                (
                    "phone_number",
                    models.CharField(
                        max_length=14,
                        validators=[
                            django.core.validators.RegexValidator(
                                code="invalid_mobile",
                                message="Please Enter 10/11 digit mobile number or landline as 0<std code><phone number>",
                                regex="^((\\+91|91|0)[\\- ]{0,1})?[456789]\\d{9}$",
                            )
                        ],
                    ),
                ),
                ("is_smart_phone", models.BooleanField()),
                (
                    "ambulance",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="facility.Ambulance",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="PatientConsultationICMR",
            fields=[],
            options={
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("facility.patientconsultation",),
        ),
        migrations.CreateModel(
            name="PatientIcmr",
            fields=[],
            options={
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("facility.patientregistration",),
        ),
        migrations.CreateModel(
            name="PatientSampleICMR",
            fields=[],
            options={
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("facility.patientsample",),
        ),
        migrations.AddIndex(
            model_name="shiftingrequest",
            index=models.Index(
                fields=["status", "deleted"], name="facility_sh_status_2f7458_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="resourcerequest",
            index=models.Index(
                fields=["status", "deleted"], name="facility_re_status_52e9a4_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="patientsearch",
            index=models.Index(
                fields=["year_of_birth", "date_of_birth", "phone_number"],
                name="facility_pa_year_of_cf9f3e_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="patientsearch",
            index=models.Index(
                fields=["year_of_birth", "phone_number"],
                name="facility_pa_year_of_c04a3d_idx",
            ),
        ),
        migrations.AddConstraint(
            model_name="patientconsultation",
            constraint=models.CheckConstraint(
                condition=models.Q(
                    models.Q(_negated=True, suggestion="R"),
                    ("referred_to__isnull", False),
                    ("referred_to_external__isnull", False),
                    _connector="OR",
                ),
                name="if_referral_suggested",
            ),
        ),
        migrations.AddConstraint(
            model_name="patientconsultation",
            constraint=models.CheckConstraint(
                condition=models.Q(
                    ("admitted", False),
                    ("admission_date__isnull", False),
                    _connector="OR",
                ),
                name="if_admitted",
            ),
        ),
        migrations.AddIndex(
            model_name="localbodyscopedsummary",
            index=models.Index(
                fields=["-modified_date"], name="facility_lo_modifie_a60b84_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="localbodyscopedsummary",
            index=models.Index(
                fields=["-created_date"], name="facility_lo_created_0e69e8_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="localbodyscopedsummary",
            index=models.Index(fields=["s_type"], name="facility_lo_s_type_f5b38c_idx"),
        ),
        migrations.AddIndex(
            model_name="localbodyscopedsummary",
            index=models.Index(
                fields=["-created_date", "s_type"],
                name="facility_lo_created_d5c5c0_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="investigationsession",
            index=models.Index(
                fields=["-created_date"], name="facility_in_created_ea2255_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="facilityuser",
            unique_together={("facility", "user")},
        ),
        migrations.AddIndex(
            model_name="facilityrelatedsummary",
            index=models.Index(
                fields=["-modified_date"], name="facility_fa_modifie_552c15_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="facilityrelatedsummary",
            index=models.Index(
                fields=["-created_date"], name="facility_fa_created_adc5be_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="facilityrelatedsummary",
            index=models.Index(fields=["s_type"], name="facility_fa_s_type_7b0ae3_idx"),
        ),
        migrations.AddIndex(
            model_name="facilityrelatedsummary",
            index=models.Index(
                fields=["-created_date", "s_type"],
                name="facility_fa_created_81979e_idx",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="facilitypatientstatshistory",
            unique_together={("facility", "entry_date")},
        ),
        migrations.AddConstraint(
            model_name="facilitylocalgovtbody",
            constraint=models.CheckConstraint(
                condition=models.Q(
                    ("local_body__isnull", False),
                    ("district__isnull", False),
                    _connector="OR",
                ),
                name="cons_facilitylocalgovtbody_only_one_null",
            ),
        ),
        migrations.AddIndex(
            model_name="facilityinventoryburnrate",
            index=models.Index(
                fields=["facility", "item"], name="facility_fa_facilit_e2046a_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="facilityinventoryburnrate",
            unique_together={("facility", "item")},
        ),
        migrations.AddIndex(
            model_name="districtscopedsummary",
            index=models.Index(
                fields=["-modified_date"], name="facility_di_modifie_09d187_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="districtscopedsummary",
            index=models.Index(
                fields=["-created_date"], name="facility_di_created_74064b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="districtscopedsummary",
            index=models.Index(fields=["s_type"], name="facility_di_s_type_7410ec_idx"),
        ),
        migrations.AddIndex(
            model_name="districtscopedsummary",
            index=models.Index(
                fields=["-created_date", "s_type"],
                name="facility_di_created_45003b_idx",
            ),
        ),
        migrations.AddConstraint(
            model_name="asset",
            constraint=models.UniqueConstraint(
                condition=models.Q(qr_code_id__isnull=False),
                fields=("qr_code_id",),
                name="qr_code_unique_when_not_null",
            ),
        ),
    ]
