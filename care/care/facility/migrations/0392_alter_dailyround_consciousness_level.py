# Generated by Django 4.2.5 on 2023-10-18 04:32

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0391_merge_20231016_1845"),
    ]

    def migrate_consciousness_level(apps, schema_editor):
        DailyRound = apps.get_model("facility", "DailyRound")
        # Migrate `CANNOT_BE_ASSESSED` to `UNKNOWN`
        DailyRound.objects.filter(consciousness_level=25).update(consciousness_level=0)

    operations = [
        migrations.RunPython(
            migrate_consciousness_level, reverse_code=migrations.RunPython.noop
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="consciousness_level",
            field=models.IntegerField(
                choices=[
                    (0, "UNKNOWN"),
                    (5, "ALERT"),
                    (10, "RESPONDS_TO_VOICE"),
                    (15, "RESPONDS_TO_PAIN"),
                    (20, "UNRESPONSIVE"),
                    (25, "AGITATED_OR_CONFUSED"),
                    (30, "ONSET_OF_AGITATION_AND_CONFUSION"),
                ],
                default=0,
            ),
        ),
    ]
