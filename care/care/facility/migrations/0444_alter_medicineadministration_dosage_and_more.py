# Generated by Django 4.2.8 on 2024-07-05 10:47

from django.db import migrations, models

import care.utils.models.validators


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0443_remove_patientconsultation_consent_records_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="medicineadministration",
            name="dosage",
            field=models.CharField(
                blank=True,
                max_length=100,
                null=True,
                validators=[
                    care.utils.models.validators.DenominationValidator(
                        allow_floats=True,
                        max_amount=5000,
                        min_amount=0.0001,
                        precision=4,
                        units={
                            "ampule(s)",
                            "mg",
                            "g",
                            "tsp",
                            "drop(s)",
                            "mcg",
                            "unit(s)",
                            "ml",
                        },
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="prescription",
            name="base_dosage",
            field=models.CharField(
                blank=True,
                max_length=100,
                null=True,
                validators=[
                    care.utils.models.validators.DenominationValidator(
                        allow_floats=True,
                        max_amount=5000,
                        min_amount=0.0001,
                        precision=4,
                        units={
                            "ampule(s)",
                            "mg",
                            "g",
                            "tsp",
                            "drop(s)",
                            "mcg",
                            "unit(s)",
                            "ml",
                        },
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="prescription",
            name="max_dosage",
            field=models.CharField(
                blank=True,
                max_length=100,
                null=True,
                validators=[
                    care.utils.models.validators.DenominationValidator(
                        allow_floats=True,
                        max_amount=5000,
                        min_amount=0.0001,
                        precision=4,
                        units={
                            "ampule(s)",
                            "mg",
                            "g",
                            "tsp",
                            "drop(s)",
                            "mcg",
                            "unit(s)",
                            "ml",
                        },
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="prescription",
            name="route",
            field=models.CharField(
                blank=True,
                choices=[
                    ("ORAL", "Oral"),
                    ("IV", "IV"),
                    ("IM", "IM"),
                    ("SC", "S/C"),
                    ("INHALATION", "Inhalation"),
                    ("NASOGASTRIC", "Nasogastric/Gastrostomy tube"),
                    ("INTRATHECAL", "intrathecal injection"),
                    ("TRANSDERMAL", "Transdermal"),
                    ("RECTAL", "Rectal"),
                    ("SUBLINGUAL", "Sublingual"),
                ],
                max_length=100,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="prescription",
            name="target_dosage",
            field=models.CharField(
                blank=True,
                max_length=100,
                null=True,
                validators=[
                    care.utils.models.validators.DenominationValidator(
                        allow_floats=True,
                        max_amount=5000,
                        min_amount=0.0001,
                        precision=4,
                        units={
                            "ampule(s)",
                            "mg",
                            "g",
                            "tsp",
                            "drop(s)",
                            "mcg",
                            "unit(s)",
                            "ml",
                        },
                    )
                ],
            ),
        ),
    ]
