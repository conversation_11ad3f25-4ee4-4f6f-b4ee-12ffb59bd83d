# Generated by Django 4.2.2 on 2023-08-24 06:32

import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


def move_last_serviced_on_and_notes(apps, schema_editor):
    Asset = apps.get_model("facility", "Asset")
    AssetService = apps.get_model("facility", "AssetService")

    asset_services = []
    asset_linked_last_services = []
    for asset in Asset.objects.all():
        if asset.last_serviced_on or asset.notes:
            service_record = AssetService(
                asset=asset, serviced_on=asset.last_serviced_on, note=asset.notes
            )
            asset_services.append(service_record)
            asset.last_service = service_record
            asset_linked_last_services.append(asset)

    AssetService.objects.bulk_create(asset_services)
    Asset.objects.bulk_update(asset_linked_last_services, ["last_service"])


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        (
            "facility",
            "0381_merge_ip_op_no",
        ),
    ]

    operations = [
        migrations.CreateModel(
            name="AssetService",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("serviced_on", models.DateField(default=None, null=True)),
                ("note", models.TextField(blank=True, default="", null=True)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="AssetServiceEdit",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("edited_on", models.DateTimeField(auto_now_add=True)),
                ("serviced_on", models.DateField()),
                ("note", models.TextField()),
                (
                    "asset_service",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="edits",
                        to="facility.assetservice",
                    ),
                ),
                (
                    "edited_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-edited_on"],
            },
        ),
        migrations.AddField(
            model_name="assetservice",
            name="asset",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="facility.asset"
            ),
        ),
        migrations.AddField(
            model_name="asset",
            name="last_service",
            field=models.ForeignKey(
                default=None,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="last_service",
                to="facility.assetservice",
            ),
        ),
        migrations.RunPython(
            code=move_last_serviced_on_and_notes,
            reverse_code=django.db.migrations.operations.special.RunPython.noop,
        ),
        migrations.RemoveField(
            model_name="asset",
            name="last_serviced_on",
        ),
        migrations.RemoveField(
            model_name="asset",
            name="notes",
        ),
    ]
