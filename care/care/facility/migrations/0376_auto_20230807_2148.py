# Generated by Django 4.2.2 on 2023-08-07 16:18

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0375_alter_dailyround_resp"),
    ]

    def reset_expired_fields_for_non_expired_discharged_consultations(
        apps, schema_editor
    ):
        PatientConsultation = apps.get_model("facility", "PatientConsultation")
        PatientConsultation.objects.exclude(
            discharge_reason__isnull=True,
            discharge_reason="EXP",
        ).update(
            death_datetime=None,
            death_confirmed_doctor=None,
        )

    operations = [
        migrations.RunPython(
            reset_expired_fields_for_non_expired_discharged_consultations,
            reverse_code=migrations.RunPython.noop,
        ),
    ]
