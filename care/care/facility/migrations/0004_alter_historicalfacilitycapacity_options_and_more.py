# Generated by Django 4.2.2 on 2023-06-14 08:36

import django.contrib.postgres.fields
from django.db import migrations, models

import care.utils.models.validators


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0003_auto_20230614_1048"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="historicalfacilitycapacity",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical facility capacity",
                "verbose_name_plural": "historical Facility Capacities",
            },
        ),
        migrations.AlterModelOptions(
            name="historicalpatientregistration",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical patient registration",
                "verbose_name_plural": "historical patient registrations",
            },
        ),
        migrations.AlterField(
            model_name="ambulance",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="ambulancedriver",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="asset",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="asset",
            name="meta",
            field=models.JSONField(
                blank=True,
                default=dict,
                validators=[
                    care.utils.models.validators.JSONFieldSchemaValidator(
                        {
                            "$schema": "http://json-schema.org/draft-07/schema#",
                            "anyOf": [
                                {"$ref": "#/definitions/onvif"},
                                {"$ref": "#/definitions/hl7monitor"},
                                {"$ref": "#/definitions/empty"},
                            ],
                            "definitions": {
                                "empty": {
                                    "additionalProperties": False,
                                    "type": "object",
                                },
                                "hl7monitor": {
                                    "additionalProperties": False,
                                    "properties": {
                                        "asset_type": {"type": "string"},
                                        "insecure_connection": {"type": "boolean"},
                                        "local_ip_address": {"type": "string"},
                                        "middleware_hostname": {"type": "string"},
                                    },
                                    "required": ["local_ip_address"],
                                    "type": "object",
                                },
                                "onvif": {
                                    "additionalProperties": False,
                                    "properties": {
                                        "asset_type": {"type": "string"},
                                        "camera_access_key": {"type": "string"},
                                        "camera_type": {"type": "string"},
                                        "insecure_connection": {"type": "boolean"},
                                        "local_ip_address": {"type": "string"},
                                        "middleware_hostname": {"type": "string"},
                                    },
                                    "required": [
                                        "local_ip_address",
                                        "camera_access_key",
                                    ],
                                    "type": "object",
                                },
                                "ventilator": {
                                    "additionalProperties": False,
                                    "properties": {
                                        "asset_type": {"type": "string"},
                                        "insecure_connection": {"type": "boolean"},
                                        "local_ip_address": {"type": "string"},
                                        "middleware_hostname": {"type": "string"},
                                    },
                                    "required": ["local_ip_address"],
                                    "type": "object",
                                },
                            },
                        }
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="assetbed",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="assetbed",
            name="meta",
            field=models.JSONField(blank=True, default=dict),
        ),
        migrations.AlterField(
            model_name="assetlocation",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="assettransaction",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="bed",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="bed",
            name="meta",
            field=models.JSONField(blank=True, default=dict),
        ),
        migrations.AlterField(
            model_name="building",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="consultationbed",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="consultationbed",
            name="meta",
            field=models.JSONField(blank=True, default=dict),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="bp",
            field=models.JSONField(
                default=dict,
                validators=[
                    care.utils.models.validators.JSONFieldSchemaValidator(
                        {
                            "$schema": "http://json-schema.org/draft-07/schema#",
                            "additionalProperties": False,
                            "properties": {
                                "diastolic": {"type": "number"},
                                "mean": {"type": "number"},
                                "systolic": {"type": "number"},
                            },
                            "type": "object",
                        }
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="feeds",
            field=models.JSONField(
                default=list,
                validators=[
                    care.utils.models.validators.JSONFieldSchemaValidator(
                        {
                            "$schema": "http://json-schema.org/draft-07/schema#",
                            "items": [
                                {
                                    "additionalProperties": False,
                                    "properties": {
                                        "calories": {"type": "number"},
                                        "name": {"type": "string"},
                                        "quantity": {"type": "number"},
                                    },
                                    "required": ["name", "quantity"],
                                    "type": "object",
                                }
                            ],
                            "type": "array",
                        }
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="infusions",
            field=models.JSONField(
                default=list,
                validators=[
                    care.utils.models.validators.JSONFieldSchemaValidator(
                        {
                            "$schema": "http://json-schema.org/draft-07/schema#",
                            "items": [
                                {
                                    "additionalProperties": False,
                                    "properties": {
                                        "conc_unit": {"type": "string"},
                                        "concentration": {"type": "number"},
                                        "name": {"type": "string"},
                                        "quantity": {"type": "number"},
                                    },
                                    "required": ["name", "quantity"],
                                    "type": "object",
                                }
                            ],
                            "type": "array",
                        }
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="iv_fluids",
            field=models.JSONField(
                default=list,
                validators=[
                    care.utils.models.validators.JSONFieldSchemaValidator(
                        {
                            "$schema": "http://json-schema.org/draft-07/schema#",
                            "items": [
                                {
                                    "additionalProperties": False,
                                    "properties": {
                                        "name": {"type": "string"},
                                        "quantity": {"type": "number"},
                                    },
                                    "required": ["name", "quantity"],
                                    "type": "object",
                                }
                            ],
                            "type": "array",
                        }
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="medication_given",
            field=models.JSONField(default=dict),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="medicine_administration",
            field=models.JSONField(default=list),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="meta",
            field=models.JSONField(
                default=dict,
                validators=[
                    care.utils.models.validators.JSONFieldSchemaValidator(
                        {
                            "$schema": "http://json-schema.org/draft-07/schema#",
                            "additionalProperties": False,
                            "properties": {"dialysis": {"type": "boolean"}},
                            "type": "object",
                        }
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="nursing",
            field=models.JSONField(
                default=list,
                validators=[
                    care.utils.models.validators.JSONFieldSchemaValidator(
                        {
                            "$schema": "http://json-schema.org/draft-07/schema#",
                            "items": [
                                {
                                    "additionalProperties": False,
                                    "properties": {
                                        "description": {"type": "string"},
                                        "procedure": {"type": "string"},
                                    },
                                    "required": ["procedure", "description"],
                                    "type": "object",
                                }
                            ],
                            "type": "array",
                        }
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="output",
            field=models.JSONField(
                default=list,
                validators=[
                    care.utils.models.validators.JSONFieldSchemaValidator(
                        {
                            "$schema": "http://json-schema.org/draft-07/schema#",
                            "items": [
                                {
                                    "additionalProperties": False,
                                    "properties": {
                                        "name": {"type": "string"},
                                        "quantity": {"type": "number"},
                                    },
                                    "required": ["name", "quantity"],
                                    "type": "object",
                                }
                            ],
                            "type": "array",
                        }
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="pain_scale_enhanced",
            field=models.JSONField(
                default=list,
                validators=[
                    care.utils.models.validators.JSONFieldSchemaValidator(
                        {
                            "$schema": "http://json-schema.org/draft-07/schema#",
                            "items": [
                                {
                                    "additionalProperties": False,
                                    "properties": {
                                        "description": {"type": "string"},
                                        "region": {"type": "string"},
                                        "scale": {
                                            "maximum": 5,
                                            "minimum": 1,
                                            "type": "number",
                                        },
                                    },
                                    "required": ["region", "scale"],
                                    "type": "object",
                                }
                            ],
                            "type": "array",
                        }
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="pressure_sore",
            field=models.JSONField(
                default=list,
                validators=[
                    care.utils.models.validators.JSONFieldSchemaValidator(
                        {
                            "$schema": "http://json-schema.org/draft-07/schema#",
                            "items": [
                                {
                                    "additionalProperties": False,
                                    "properties": {
                                        "base_score": {"type": "number"},
                                        "description": {"type": "string"},
                                        "exudate_amount": {
                                            "enum": [
                                                "None",
                                                "Light",
                                                "Moderate",
                                                "Heavy",
                                            ]
                                        },
                                        "length": {"type": "number"},
                                        "push_score": {"type": "number"},
                                        "region": {"type": "string"},
                                        "scale": {
                                            "maximum": 5,
                                            "minimum": 1,
                                            "type": "number",
                                        },
                                        "tissue_type": {
                                            "enum": [
                                                "Closed",
                                                "Epithelial",
                                                "Granulation",
                                                "Slough",
                                                "Necrotic",
                                            ]
                                        },
                                        "width": {"type": "number"},
                                    },
                                    "required": [],
                                    "type": "object",
                                }
                            ],
                            "type": "array",
                        }
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="disease",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="districtscopedsummary",
            name="data",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name="facility",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="facilitycapacity",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="facilitydefaultassetlocation",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="facilityinventoryburnrate",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="facilityinventoryitem",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="facilityinventoryitemtag",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="facilityinventorylog",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="facilityinventoryminquantity",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="facilityinventorysummary",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="facilityinventoryunit",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="facilityinventoryunitconverter",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="facilitylocalgovtbody",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="facilitypatientstatshistory",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="facilityrelatedsummary",
            name="data",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name="facilitystaff",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="facilityuser",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="facilityvolunteer",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="fileupload",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="historicalfacilitycapacity",
            name="history_date",
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name="historicalfacilitycapacity",
            name="id",
            field=models.BigIntegerField(
                auto_created=True, blank=True, db_index=True, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="historicalpatientregistration",
            name="countries_travelled",
            field=models.JSONField(
                blank=True, null=True, verbose_name="Countries Patient has Travelled to"
            ),
        ),
        migrations.AlterField(
            model_name="historicalpatientregistration",
            name="history_date",
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name="historicalpatientregistration",
            name="id",
            field=models.BigIntegerField(
                auto_created=True, blank=True, db_index=True, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="hospitaldoctors",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="inventory",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="inventoryitem",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="inventorylog",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="investigationsession",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="investigationvalue",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="localbodyscopedsummary",
            name="data",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name="medicineadministration",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="notification",
            name="caused_objects",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name="notification",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="patientconsultation",
            name="discharge_advice",
            field=models.JSONField(default=dict),
        ),
        migrations.AlterField(
            model_name="patientconsultation",
            name="discharge_prescription",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name="patientconsultation",
            name="discharge_prn_prescription",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name="patientconsultation",
            name="icd11_diagnoses",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(max_length=100),
                blank=True,
                default=list,
                null=True,
                size=None,
            ),
        ),
        migrations.AlterField(
            model_name="patientconsultation",
            name="icd11_provisional_diagnoses",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(max_length=100),
                blank=True,
                default=list,
                null=True,
                size=None,
            ),
        ),
        migrations.AlterField(
            model_name="patientconsultation",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="patientconsultation",
            name="intubation_history",
            field=models.JSONField(default=list),
        ),
        migrations.AlterField(
            model_name="patientconsultation",
            name="investigation",
            field=models.JSONField(default=dict),
        ),
        migrations.AlterField(
            model_name="patientconsultation",
            name="prescriptions",
            field=models.JSONField(default=dict),
        ),
        migrations.AlterField(
            model_name="patientconsultation",
            name="prn_prescription",
            field=models.JSONField(default=dict),
        ),
        migrations.AlterField(
            model_name="patientconsultation",
            name="procedure",
            field=models.JSONField(default=dict),
        ),
        migrations.AlterField(
            model_name="patientcontactdetails",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="patientexternaltest",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="patientinvestigation",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="patientinvestigationgroup",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="patientmetainfo",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="patientmobileotp",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="patientnotes",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="patientregistration",
            name="countries_travelled",
            field=models.JSONField(
                blank=True, null=True, verbose_name="Countries Patient has Travelled to"
            ),
        ),
        migrations.AlterField(
            model_name="patientregistration",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="patientsample",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="patientsampleflow",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="patientteleconsultation",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="prescription",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="prescription",
            name="meta",
            field=models.JSONField(blank=True, default=dict),
        ),
        migrations.AlterField(
            model_name="prescriptionsupplier",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="resourcerequest",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="resourcerequestcomment",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="room",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="shiftingrequest",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="shiftingrequestcomment",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="staffroomallocation",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="userdefaultassetlocation",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
    ]
