# Generated by Django 4.2.8 on 2023-12-26 06:17

from django.db import migrations, models
from django.db.models import F, Window
from django.db.models.functions import RowNumber


def copy_bed_name(apps, schema_editor):
    Bed = apps.get_model("facility", "Bed")
    Bed.objects.update(old_name=models.F("name"))


def reverse_copy_bed_name(apps, schema_editor):
    Bed = apps.get_model("facility", "Bed")
    Bed.objects.filter(old_name__isnull=False).update(name=models.F("old_name"))


def fix_duplicate_bed_names(apps, schema_editor):
    Bed = apps.get_model("facility", "Bed")

    window = Window(
        expression=RowNumber(),
        partition_by=[F("location"), F("lname")],
        order_by=F("id").asc(),
    )

    beds = Bed.objects.annotate(
        lname=models.functions.Lower("name"), row_number=window
    ).filter(row_number__gt=1)

    for bed in beds:
        bed.name = f"{bed.name} ({bed.row_number - 1})"

    Bed.objects.bulk_update(beds, ["name"], batch_size=2000)


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0404_merge_20231220_2227"),
    ]

    operations = [
        migrations.AddField(
            model_name="bed",
            name="old_name",
            field=models.CharField(blank=True, max_length=1024, null=True),
        ),
        migrations.RunPython(copy_bed_name, reverse_copy_bed_name),
        migrations.RunPython(fix_duplicate_bed_names, migrations.RunPython.noop),
    ]
