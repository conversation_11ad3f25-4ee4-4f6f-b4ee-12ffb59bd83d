# Generated by Django 4.2.10 on 2024-08-13 10:36

from django.core.paginator import Paginator
from django.db import migrations


class Migration(migrations.Migration):
    def forward_rename_dailyround_events(apps, schema_editor):
        EventType = apps.get_model("facility", "EventType")
        PatientConsultationEvent = apps.get_model(
            "facility", "PatientConsultationEvent"
        )

        event_type = EventType.objects.filter(name="RESPIRATORY_SUPPORT")
        if not event_type.exists():
            return
        event_type_id = event_type.first().id

        paginator = Paginator(
            PatientConsultationEvent.objects.filter(
                object_model="DailyRound",
                event_type_id=event_type_id,
                value__has_key="ventilator_fi02",
            ).order_by("id"),
            1000,
        )

        for page_number in paginator.page_range:
            bulk = []
            for instance in paginator.page(page_number).object_list:
                instance.value["ventilator_fio2"] = instance.value.pop(
                    "ventilator_fi02"
                )
                bulk.append(instance)
            PatientConsultationEvent.objects.bulk_update(bulk, ["value"])

    def reverse_rename_dailyround_events(apps, schema_editor):
        EventType = apps.get_model("facility", "EventType")
        PatientConsultationEvent = apps.get_model(
            "facility", "PatientConsultationEvent"
        )

        event_type = EventType.objects.filter(name="RESPIRATORY_SUPPORT")
        if not event_type.exists():
            return
        event_type_id = event_type.first().id

        paginator = Paginator(
            PatientConsultationEvent.objects.filter(
                object_model="DailyRound",
                event_type_id=event_type_id,
                value__has_key="ventilator_fio2",
            ).order_by("id"),
            1000,
        )

        for page_number in paginator.page_range:
            bulk = []
            for instance in paginator.page(page_number).object_list:
                instance.value["ventilator_fi02"] = instance.value.pop(
                    "ventilator_fio2"
                )
                bulk.append(instance)
            PatientConsultationEvent.objects.bulk_update(bulk, ["value"])

    dependencies = [
        ("facility", "0447_patientconsultationevent_taken_at"),
    ]

    operations = [
        migrations.RenameField(
            model_name="dailyround",
            old_name="ventilator_fi02",
            new_name="ventilator_fio2",
        ),
        migrations.RunPython(
            forward_rename_dailyround_events,
            reverse_code=reverse_rename_dailyround_events,
        ),
    ]
