# Generated by Django 4.2.10 on 2024-07-04 16:20


import django.contrib.postgres.fields
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    def migrate_has_consents(apps, schema_editor):
        FileUpload = apps.get_model("facility", "FileUpload")
        PatientConsent = apps.get_model("facility", "PatientConsent")

        consents = PatientConsent.objects.filter(archived=False)
        for consent in consents:
            consultation = consent.consultation
            consent_types = (
                PatientConsent.objects.filter(consultation=consultation, archived=False)
                .annotate(
                    str_external_id=models.functions.Cast(
                        "external_id", models.CharField()
                    )
                )
                .annotate(
                    has_files=models.Exists(
                        FileUpload.objects.filter(
                            associating_id=models.OuterRef("str_external_id"),
                            file_type=7,
                            is_archived=False,
                        )
                    )
                )
                .filter(has_files=True)
                .distinct("type")
                .values_list("type", flat=True)
            )
            consultation.has_consents = list(consent_types)
            consultation.save()

    dependencies = [
        ("facility", "0443_remove_patientconsultation_consent_records_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="patientconsultation",
            name="has_consents",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.IntegerField(
                    choices=[
                        (1, "Consent for Admission"),
                        (2, "Patient Code Status"),
                        (3, "Consent for Procedure"),
                        (4, "High Risk Consent"),
                        (5, "Others"),
                    ]
                ),
                default=list,
                size=None,
            ),
        ),
        migrations.AlterField(
            model_name="patientconsent",
            name="consultation",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="consents",
                to="facility.patientconsultation",
            ),
        ),
        migrations.RunPython(
            migrate_has_consents, reverse_code=migrations.RunPython.noop
        ),
    ]
