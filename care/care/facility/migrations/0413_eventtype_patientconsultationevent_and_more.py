# Generated by Django 4.2.10 on 2024-02-12 10:31

import django.contrib.postgres.fields
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

import care.utils.event_utils
import care.utils.ulid.models
import care.utils.ulid.ulid


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("facility", "0412_availabilityrecord_unique_constraint"),
    ]

    operations = [
        migrations.CreateModel(
            name="EventType",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(db_index=True, max_length=100, unique=True)),
                ("description", models.TextField(blank=True, null=True)),
                ("model", models.CharField(db_index=True, max_length=50)),
                (
                    "fields",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=50),
                        default=list,
                        size=None,
                    ),
                ),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                ("is_active", models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name="PatientConsultationEvent",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    care.utils.ulid.models.ULIDField(
                        default=care.utils.ulid.ulid.ULID, editable=False, unique=True
                    ),
                ),
                ("created_date", models.DateTimeField(db_index=True)),
                ("object_model", models.CharField(db_index=True, max_length=50)),
                ("object_id", models.IntegerField()),
                ("is_latest", models.BooleanField(default=True)),
                (
                    "meta",
                    models.JSONField(
                        default=dict, encoder=care.utils.event_utils.CustomJSONEncoder
                    ),
                ),
                (
                    "value",
                    models.JSONField(
                        default=dict, encoder=care.utils.event_utils.CustomJSONEncoder
                    ),
                ),
                (
                    "change_type",
                    models.CharField(
                        choices=[
                            ("CREATED", "Created"),
                            ("UPDATED", "Updated"),
                            ("DELETED", "Deleted"),
                        ],
                        default="CREATED",
                        max_length=10,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_date"],
            },
        ),
        migrations.RemoveConstraint(
            model_name="availabilityrecord",
            name="object_external_id_timestamp_unique",
        ),
        migrations.AddConstraint(
            model_name="availabilityrecord",
            constraint=models.UniqueConstraint(
                fields=("object_external_id", "timestamp"),
                name="object_external_id_timestamp",
            ),
        ),
        migrations.AddField(
            model_name="patientconsultationevent",
            name="caused_by",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="patientconsultationevent",
            name="consultation",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="events",
                to="facility.patientconsultation",
            ),
        ),
        migrations.AddField(
            model_name="patientconsultationevent",
            name="event_type",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="facility.eventtype"
            ),
        ),
        migrations.AddField(
            model_name="eventtype",
            name="parent",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="children",
                to="facility.eventtype",
            ),
        ),
        migrations.AddIndex(
            model_name="patientconsultationevent",
            index=models.Index(
                fields=["consultation", "is_latest"],
                name="facility_pa_consult_7b22fe_idx",
            ),
        ),
    ]
