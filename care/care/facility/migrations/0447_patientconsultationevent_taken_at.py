# Generated by Django 4.2.10 on 2024-07-02 10:44

from django.db import migrations, models


def backfill_taken_at(apps, schema_editor):
    PatientConsultationEvent = apps.get_model("facility", "PatientConsultationEvent")
    PatientConsultationEvent.objects.filter(taken_at__isnull=True).update(
        taken_at=models.F("created_date")
    )


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0446_alter_notification_event"),
    ]

    operations = [
        migrations.AddField(
            model_name="patientconsultationevent",
            name="taken_at",
            field=models.DateTimeField(db_index=True, null=True),
        ),
        migrations.RunPython(backfill_taken_at, reverse_code=migrations.RunPython.noop),
        migrations.AlterField(
            model_name="patientconsultationevent",
            name="taken_at",
            field=models.DateTimeField(db_index=True),
        ),
    ]
