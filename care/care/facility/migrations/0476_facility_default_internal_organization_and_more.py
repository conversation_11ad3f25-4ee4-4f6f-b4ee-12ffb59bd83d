# Generated by Django 5.1.3 on 2025-01-04 18:09

import django.contrib.postgres.fields
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('emr', '0001_initial'),
        ('facility', '0475_merge_20241223_2352'),
    ]

    operations = [
        migrations.AddField(
            model_name='facility',
            name='default_internal_organization',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='default_facilities', to='emr.facilityorganization'),
        ),
        migrations.AddField(
            model_name='facility',
            name='geo_organization',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='emr.organization'),
        ),
        migrations.AddField(
            model_name='facility',
            name='geo_organization_cache',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(), default=list, size=None),
        ),
        migrations.AddField(
            model_name='facility',
            name='internal_organization_cache',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(), default=list, size=None),
        ),
        migrations.AddField(
            model_name='facility',
            name='is_public',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='historicalpatientregistration',
            name='geo_organization',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='emr.organization'),
        ),
        migrations.AddField(
            model_name='historicalpatientregistration',
            name='organization_cache',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(), default=list, size=None),
        ),
        migrations.AddField(
            model_name='patientregistration',
            name='geo_organization',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='emr.organization'),
        ),
        migrations.AddField(
            model_name='patientregistration',
            name='organization_cache',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(), default=list, size=None),
        ),
    ]
