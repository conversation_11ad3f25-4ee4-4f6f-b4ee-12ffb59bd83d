# Generated by Django 4.2.10 on 2024-08-23 10:57

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0451_merge_20240823_1642"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="patientconsultation",
            name="deprecated_diagnosis",
        ),
        migrations.RemoveField(
            model_name="patientconsultation",
            name="deprecated_icd11_diagnoses",
        ),
        migrations.RemoveField(
            model_name="patientconsultation",
            name="deprecated_icd11_principal_diagnosis",
        ),
        migrations.RemoveField(
            model_name="patientconsultation",
            name="deprecated_icd11_provisional_diagnoses",
        ),
        migrations.RemoveField(
            model_name="patientconsultation",
            name="discharge_advice",
        ),
        migrations.RemoveField(
            model_name="patientconsultation",
            name="discharge_prescription",
        ),
        migrations.RemoveField(
            model_name="patientconsultation",
            name="discharge_prn_prescription",
        ),
        migrations.RemoveField(
            model_name="patientconsultation",
            name="prn_prescription",
        ),
        migrations.RemoveField(
            model_name="dailyround",
            name="deprecated_additional_symptoms",
        ),
        migrations.RemoveField(
            model_name="dailyround",
            name="deprecated_other_symptoms",
        ),
        migrations.RemoveField(
            model_name="patientconsultation",
            name="deprecated_other_symptoms",
        ),
        migrations.RemoveField(
            model_name="patientconsultation",
            name="deprecated_symptoms",
        ),
        migrations.RemoveField(
            model_name="patientconsultation",
            name="deprecated_symptoms_onset_date",
        ),
    ]
