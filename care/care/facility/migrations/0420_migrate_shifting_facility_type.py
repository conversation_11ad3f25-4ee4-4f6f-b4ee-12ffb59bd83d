# Generated by Django 4.2.10 on 2024-03-14 10:18

from django.db import migrations


def update_facility_types(apps, schema_editor):
    Facility = apps.get_model("facility", "ShiftingRequest")
    facilities_to_update = {
        801: 800,  # 24x7 Public Health Centres to Primary Health Centres
        820: 800,  # Urban Primary Health Center to Primary Health Centres
        831: 830,  # Taluk Headquarters Hospitals to Taluk Hospitals
        850: 860,  # General hospitals to District Hospitals
        900: 910,  # Co-operative hospitals to Autonomous healthcare facility
        950: 870,  # Corona Testing Labs to Govt. Labs
        1000: 3,  # Corona Care Centre to Other
        8: 870,  # Govt Hospital to Govt Medical College Hospitals
    }

    for old_id, new_id in facilities_to_update.items():
        Facility.objects.filter(assigned_facility_type=old_id).update(
            assigned_facility_type=new_id
        )


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0419_alter_patientconsultation_patient_no"),
    ]

    operations = [
        migrations.RunPython(update_facility_types, migrations.RunPython.noop),
    ]
