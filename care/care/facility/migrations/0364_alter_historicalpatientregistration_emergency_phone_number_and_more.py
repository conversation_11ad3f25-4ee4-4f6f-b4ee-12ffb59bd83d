# Generated by Django 4.2.2 on 2023-06-26 12:09

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0363_auto_20230621_1857"),
    ]

    operations = [
        migrations.<PERSON>er<PERSON><PERSON>(
            model_name="historicalpatientregistration",
            name="emergency_phone_number",
            field=models.CharField(
                default="",
                max_length=14,
                validators=[
                    django.core.validators.RegexValidator(
                        code="invalid_mobile",
                        message="Please Enter 10/11 digit mobile number or landline as 0<std code><phone number>",
                        regex="^(?:(?:(?:\\+|0{0,2})91|0{0,2})(?:\\()?\\d{3}(?:\\))?[\\-]?\\d{3}[\\-]?\\d{4})$",
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="historicalpatientregistration",
            name="phone_number",
            field=models.Char<PERSON><PERSON>(
                default="",
                max_length=14,
                validators=[
                    django.core.validators.RegexValidator(
                        code="invalid_mobile",
                        message="Please Enter 10/11 digit mobile number or landline as 0<std code><phone number>",
                        regex="^(?:(?:(?:\\+|0{0,2})91|0{0,2})(?:\\()?\\d{3}(?:\\))?[\\-]?\\d{3}[\\-]?\\d{4})$",
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="patientmobileotp",
            name="phone_number",
            field=models.CharField(
                max_length=14,
                validators=[
                    django.core.validators.RegexValidator(
                        code="invalid_mobile",
                        message="Please Enter 10/11 digit mobile number or landline as 0<std code><phone number>",
                        regex="^(?:(?:(?:\\+|0{0,2})91|0{0,2})(?:\\()?\\d{3}(?:\\))?[\\-]?\\d{3}[\\-]?\\d{4})$",
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="patientregistration",
            name="emergency_phone_number",
            field=models.CharField(
                default="",
                max_length=14,
                validators=[
                    django.core.validators.RegexValidator(
                        code="invalid_mobile",
                        message="Please Enter 10/11 digit mobile number or landline as 0<std code><phone number>",
                        regex="^(?:(?:(?:\\+|0{0,2})91|0{0,2})(?:\\()?\\d{3}(?:\\))?[\\-]?\\d{3}[\\-]?\\d{4})$",
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="patientregistration",
            name="phone_number",
            field=models.CharField(
                default="",
                max_length=14,
                validators=[
                    django.core.validators.RegexValidator(
                        code="invalid_mobile",
                        message="Please Enter 10/11 digit mobile number or landline as 0<std code><phone number>",
                        regex="^(?:(?:(?:\\+|0{0,2})91|0{0,2})(?:\\()?\\d{3}(?:\\))?[\\-]?\\d{3}[\\-]?\\d{4})$",
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="resourcerequest",
            name="refering_facility_contact_number",
            field=models.CharField(
                blank=True,
                default="",
                max_length=14,
                validators=[
                    django.core.validators.RegexValidator(
                        code="invalid_mobile",
                        message="Please Enter 10/11 digit mobile number or landline as 0<std code><phone number>",
                        regex="^(?:(?:(?:\\+|0{0,2})91|0{0,2})(?:\\()?\\d{3}(?:\\))?[\\-]?\\d{3}[\\-]?\\d{4})$",
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="shiftingrequest",
            name="ambulance_phone_number",
            field=models.CharField(
                blank=True,
                default="",
                max_length=14,
                validators=[
                    django.core.validators.RegexValidator(
                        code="invalid_mobile",
                        message="Please Enter 10/11 digit mobile number or landline as 0<std code><phone number>",
                        regex="^(?:(?:(?:\\+|0{0,2})91|0{0,2})(?:\\()?\\d{3}(?:\\))?[\\-]?\\d{3}[\\-]?\\d{4})$",
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="shiftingrequest",
            name="refering_facility_contact_number",
            field=models.CharField(
                blank=True,
                default="",
                max_length=14,
                validators=[
                    django.core.validators.RegexValidator(
                        code="invalid_mobile",
                        message="Please Enter 10/11 digit mobile number or landline as 0<std code><phone number>",
                        regex="^(?:(?:(?:\\+|0{0,2})91|0{0,2})(?:\\()?\\d{3}(?:\\))?[\\-]?\\d{3}[\\-]?\\d{4})$",
                    )
                ],
            ),
        ),
    ]
