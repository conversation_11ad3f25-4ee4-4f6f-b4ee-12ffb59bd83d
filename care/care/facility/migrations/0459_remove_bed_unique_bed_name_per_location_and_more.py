# Generated by Django 4.2.10 on 2024-09-19 14:44

import django.db.models.functions.text
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("facility", "0458_facilityflag_facilityflag_unique_facility_flag"),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="bed",
            name="unique_bed_name_per_location",
        ),
        migrations.AddConstraint(
            model_name="bed",
            constraint=models.UniqueConstraint(
                django.db.models.functions.text.Lower("name"),
                models.F("location"),
                condition=models.Q(("deleted", False)),
                name="unique_bed_name_per_location",
            ),
        ),
    ]
