# Generated by Django 4.2.10 on 2024-02-26 20:00

from django.db import migrations
from django.db.models import F, Window
from django.db.models.functions import RowNumber


def fix_duplicate_patient_numbers(apps, schema_editor):
    PatientConsultation = apps.get_model("facility", "PatientConsultation")

    PatientConsultation.objects.filter(patient_no__regex=r"^\s*$").update(
        patient_no=None
    )

    window = Window(
        expression=RowNumber(),
        partition_by=[F("patient_no"), F("facility")],
        order_by=F("id").asc(),
    )

    consultations = PatientConsultation.objects.annotate(row_number=window).filter(
        row_number__gt=1, patient_no__isnull=False
    )

    for consultation in consultations:
        consultation.patient_no = (
            f"{consultation.patient_no}_{consultation.row_number - 1}"
        )

    PatientConsultation.objects.bulk_update(
        consultations, ["patient_no"], batch_size=2000
    )


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0414_remove_bed_old_name"),
    ]

    operations = [
        migrations.RunPython(fix_duplicate_patient_numbers, migrations.RunPython.noop),
    ]
