# Generated by Django 4.2.2 on 2023-07-04 11:16

from django.db import migrations
from django.db.models import F


class Migration(migrations.Migration):
    def move_non_admission_ip_to_op(apps, schema_editor):
        PatientConsultation = apps.get_model("facility", "PatientConsultation")
        PatientConsultation.objects.exclude(suggestion="A").filter(
            ip_no__isnull=False, admission_date__isnull=True
        ).update(op_no=F("ip_no"), ip_no=None)

    dependencies = [
        ("facility", "0366_auto_20230627_1806"),
    ]

    operations = [
        migrations.RunPython(
            move_non_admission_ip_to_op, reverse_code=migrations.RunPython.noop
        )
    ]
