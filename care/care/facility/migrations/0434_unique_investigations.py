# Generated by Django 4.2.10 on 2024-03-30 09:46

from django.db import migrations, models
from django.db.models import F, Window
from django.db.models.functions import RowNumber


def fix_duplicate_investigation_names(apps, schema_editor):
    PatientInvestigation = apps.get_model("facility", "PatientInvestigation")

    window = Window(
        expression=RowNumber(),
        partition_by=[F("name")],
        order_by=F("id").asc(),
    )

    investigations = PatientInvestigation.objects.annotate(row_number=window).filter(
        row_number__gt=1
    )

    for investigation in investigations:
        investigation.name = f"{investigation.name}_{investigation.row_number - 1}"

    PatientInvestigation.objects.bulk_update(investigations, ["name"], batch_size=2000)


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0433_alter_hospitaldoctors_area"),
    ]

    operations = [
        migrations.RunPython(fix_duplicate_investigation_names),
        migrations.AlterField(
            model_name="patientinvestigation",
            name="name",
            field=models.Char<PERSON>ield(max_length=500, unique=True),
        ),
    ]
