# Generated by Django 4.2.2 on 2023-10-20 10:15

from django.db import migrations, models
from django.db.models import F


def convert_negative_to_positive(apps, schema_editor):
    HospitalDoctors = apps.get_model("facility", "HospitalDoctors")
    HospitalDoctors.objects.filter(count__lt=0).update(count=F("count") * -1)


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0392_alter_dailyround_consciousness_level"),
    ]

    operations = [
        migrations.RunPython(convert_negative_to_positive),
        migrations.AlterField(
            model_name="hospitaldoctors",
            name="count",
            field=models.PositiveIntegerField(),
        ),
    ]
