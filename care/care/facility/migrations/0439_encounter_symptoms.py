# Generated by Django 4.2.10 on 2024-05-17 10:52

import uuid

import django.db.models.deletion
from django.conf import settings
from django.core.paginator import Paginator
from django.db import migrations, models

import care.facility.models.mixins.permissions.patient


def backfill_symptoms_table(apps, schema_editor):
    EncounterSymptom = apps.get_model("facility", "EncounterSymptom")
    PatientConsultation = apps.get_model("facility", "PatientConsultation")
    DailyRound = apps.get_model("facility", "DailyRound")

    paginator = Paginator(PatientConsultation.objects.all().order_by("id"), 100)
    for page_number in paginator.page_range:
        bulk = []
        for consultation in paginator.page(page_number).object_list:
            consultation_symptoms_set = set()
            for symptom in consultation.deprecated_symptoms:
                try:
                    symptom_id = int(symptom)
                    if symptom_id == 1:
                        # Asymptomatic
                        continue
                    if symptom_id == 9:
                        # Other symptom
                        if not consultation.deprecated_other_symptoms:
                            # invalid other symptom
                            continue
                        consultation_symptoms_set.add(
                            consultation.deprecated_other_symptoms.lower()
                        )
                    else:
                        consultation_symptoms_set.add(symptom_id)
                    bulk.append(
                        EncounterSymptom(
                            symptom=symptom_id,
                            other_symptom=(
                                consultation.deprecated_other_symptoms
                                if symptom_id == 9  # Other symptom
                                else ""
                            ),
                            onset_date=consultation.deprecated_symptoms_onset_date
                            or consultation.encounter_date,
                            created_date=consultation.created_date,
                            created_by=consultation.created_by,
                            consultation=consultation,
                            is_migrated=True,
                        )
                    )
                except ValueError:
                    print(
                        f"Invalid Symptom {symptom} for Consultation {consultation.id}"
                    )

            for daily_round in DailyRound.objects.filter(consultation=consultation):
                for symptom in daily_round.deprecated_additional_symptoms:
                    try:
                        symptom_id = int(symptom)
                        if symptom_id == 1:
                            # Asymptomatic
                            continue
                        if symptom_id == 9:
                            # Other symptom
                            if not daily_round.deprecated_other_symptoms:
                                # invalid other symptom
                                continue
                            if (
                                daily_round.deprecated_other_symptoms.lower()
                                in consultation_symptoms_set
                            ):
                                # Skip if symptom already exists
                                continue
                            consultation_symptoms_set.add(
                                daily_round.deprecated_other_symptoms.lower()
                            )
                        elif symptom_id in consultation_symptoms_set:
                            # Skip if symptom already exists
                            continue
                        else:
                            consultation_symptoms_set.add(symptom_id)

                        bulk.append(
                            EncounterSymptom(
                                symptom=symptom_id,
                                other_symptom=(
                                    daily_round.deprecated_other_symptoms
                                    if symptom_id == 9  # Other symptom
                                    else ""
                                ),
                                onset_date=daily_round.created_date,
                                created_date=daily_round.created_date,
                                created_by=daily_round.created_by,
                                consultation=daily_round.consultation,
                                is_migrated=True,
                            )
                        )
                    except ValueError:
                        print(
                            f"Invalid Symptom {symptom} for DailyRound {daily_round.id}"
                        )
        EncounterSymptom.objects.bulk_create(bulk)


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("facility", "0438_alter_dailyround_patient_category_and_more"),
    ]

    operations = [
        migrations.RenameField(
            model_name="dailyround",
            old_name="additional_symptoms",
            new_name="deprecated_additional_symptoms",
        ),
        migrations.RenameField(
            model_name="dailyround",
            old_name="other_symptoms",
            new_name="deprecated_other_symptoms",
        ),
        migrations.RenameField(
            model_name="patientconsultation",
            old_name="other_symptoms",
            new_name="deprecated_other_symptoms",
        ),
        migrations.RenameField(
            model_name="patientconsultation",
            old_name="symptoms",
            new_name="deprecated_symptoms",
        ),
        migrations.RenameField(
            model_name="patientconsultation",
            old_name="symptoms_onset_date",
            new_name="deprecated_symptoms_onset_date",
        ),
        migrations.CreateModel(
            name="EncounterSymptom",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "symptom",
                    models.SmallIntegerField(
                        choices=[
                            (9, "Others"),
                            (2, "Fever"),
                            (3, "Sore Throat"),
                            (4, "Cough"),
                            (5, "Breathlessness"),
                            (6, "Myalgia"),
                            (7, "Abdominal Discomfort"),
                            (8, "Vomiting"),
                            (11, "Sputum"),
                            (12, "Nausea"),
                            (13, "Chest Pain"),
                            (14, "Hemoptysis"),
                            (15, "Nasal Discharge"),
                            (16, "Body Ache"),
                            (17, "Diarrhoea"),
                            (18, "Pain"),
                            (19, "Pedal Edema"),
                            (20, "Wound"),
                            (21, "Constipation"),
                            (22, "Headache"),
                            (23, "Bleeding"),
                            (24, "Dizziness"),
                            (25, "Chills"),
                            (26, "General Weakness"),
                            (27, "Irritability"),
                            (28, "Confusion"),
                            (29, "Abdominal Pain"),
                            (30, "Joint Pain"),
                            (31, "Redness Of Eyes"),
                            (32, "Anorexia"),
                            (33, "New Loss Of Taste"),
                            (34, "New Loss Of Smell"),
                        ]
                    ),
                ),
                ("other_symptom", models.CharField(blank=True, default="")),
                ("onset_date", models.DateTimeField(null=False, blank=False)),
                ("cure_date", models.DateTimeField(blank=True, null=True)),
                (
                    "clinical_impression_status",
                    models.CharField(
                        choices=[
                            ("in-progress", "In Progress"),
                            ("completed", "Completed"),
                            ("entered-in-error", "Entered in Error"),
                        ],
                        default="in-progress",
                        max_length=255,
                    ),
                ),
                (
                    "consultation",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="symptoms",
                        to="facility.patientconsultation",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "is_migrated",
                    models.BooleanField(
                        default=False,
                        help_text="This field is to throw caution to data that was previously ported over",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(
                models.Model,
                care.facility.models.mixins.permissions.patient.ConsultationRelatedPermissionMixin,
            ),
        ),
        migrations.RunPython(backfill_symptoms_table, migrations.RunPython.noop),
    ]
