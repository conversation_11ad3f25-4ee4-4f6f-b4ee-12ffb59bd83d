# Generated by Django 4.2.2 on 2023-09-27 09:38

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("facility", "0388_goal_goalentry_goalproperty_goalpropertyentry"),
    ]

    operations = [
        migrations.AddField(
            model_name="medicineadministration",
            name="archived_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="medicineadministration",
            name="archived_on",
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]
