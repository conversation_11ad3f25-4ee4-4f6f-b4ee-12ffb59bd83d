# Generated by Django 4.2.6 on 2023-12-11 14:00

from django.db import migrations
from django.db.models import Case, Value, When


def migrate_to_temp_field(apps, schema_editor):
    PatientConsultation = apps.get_model("facility", "PatientConsultation")
    mapping = {
        "REC": 1,
        "REF": 2,
        "EXP": 3,
        "LAMA": 4,
        "": -1,
    }

    cases = [When(discharge_reason=k, then=Value(v)) for k, v in mapping.items()]
    PatientConsultation.objects.filter(discharge_reason__isnull=False).update(
        new_discharge_reason=Case(*cases, default=Value(-1))
    )


def reverse_migration(apps, schema_editor):
    PatientConsultation = apps.get_model("facility", "PatientConsultation")
    reverse_mapping = {1: "REC", 2: "REF", 3: "EXP", 4: "LAMA", -1: ""}

    cases = [
        When(discharge_reason=k, then=Value(v)) for k, v in reverse_mapping.items()
    ]
    PatientConsultation.objects.filter(new_discharge_reason__isnull=False).update(
        discharge_reason=Case(*cases, default=Value(""))
    )


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0404_merge_20231220_2227"),
    ]

    operations = [
        migrations.RunPython(migrate_to_temp_field, reverse_code=reverse_migration),
    ]
