# Generated by Django 4.2.2 on 2023-09-25 13:00

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0387_merge_20230911_2303"),
    ]

    operations = [
        migrations.CreateModel(
            name="ICD11Diagnosis",
            fields=[
                ("id", models.BigIntegerField(primary_key=True, serialize=False)),
                ("icd11_id", models.Char<PERSON>ield(max_length=255, unique=True)),
                ("label", models.Char<PERSON>ield(max_length=255)),
                (
                    "class_kind",
                    models.Char<PERSON>ield(
                        choices=[
                            ("chapter", "Chapter"),
                            ("block", "Block"),
                            ("category", "Category"),
                        ],
                        max_length=255,
                    ),
                ),
                ("is_leaf", models.BooleanField()),
                ("average_depth", models.IntegerField()),
                ("is_adopted_child", models.BooleanField()),
                (
                    "breadth_value",
                    models.Decimal<PERSON>ield(decimal_places=22, max_digits=24),
                ),
                ("meta_hidden", models.<PERSON><PERSON><PERSON><PERSON><PERSON>(default=False)),
                ("meta_chapter", models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ("meta_chapter_short", models.CharField(max_length=255, null=True)),
                ("meta_root_block", models.CharField(max_length=255, null=True)),
                ("meta_root_category", models.CharField(max_length=255, null=True)),
                (
                    "parent",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="facility.icd11diagnosis",
                    ),
                ),
            ],
        ),
    ]
