# Generated by Django 4.2.8 on 2024-03-13 07:03

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0423_patientconsultation_consent_records_and_more"),
    ]

    def populate_patientregistration_death_datetime(apps, schema_editor):
        PatientRegistration = apps.get_model("facility", "PatientRegistration")

        patients = (
            PatientRegistration.objects.only("last_consultation")
            .filter(last_consultation__death_datetime__isnull=False)
            .annotate(new_death_datetime=models.F("last_consultation__death_datetime"))
        )

        for patient in patients:
            patient.death_datetime = patient.new_death_datetime

        PatientRegistration.objects.bulk_update(
            patients, ["death_datetime"], batch_size=1000
        )

    operations = [
        migrations.RemoveField(
            model_name="historicalpatientregistration",
            name="age",
        ),
        migrations.RemoveField(
            model_name="patientregistration",
            name="age",
        ),
        migrations.AddField(
            model_name="historicalpatientregistration",
            name="death_datetime",
            field=models.DateTimeField(default=None, null=True),
        ),
        migrations.AddField(
            model_name="patientregistration",
            name="death_datetime",
            field=models.DateTimeField(default=None, null=True),
        ),
        migrations.AlterField(
            model_name="historicalpatientregistration",
            name="year_of_birth",
            field=models.IntegerField(
                null=True, validators=[django.core.validators.MinValueValidator(1900)]
            ),
        ),
        migrations.AlterField(
            model_name="patientregistration",
            name="year_of_birth",
            field=models.IntegerField(
                null=True, validators=[django.core.validators.MinValueValidator(1900)]
            ),
        ),
        migrations.RunPython(
            populate_patientregistration_death_datetime,
            migrations.RunPython.noop,
        ),
    ]
