# Generated by Django 4.2.5 on 2023-11-14 06:23

import datetime

from django.db import migrations, models
from django.db.models import Duration<PERSON><PERSON>, ExpressionWrapper, F
from django.db.models.functions import TruncDay
from django.utils import timezone


class Migration(migrations.Migration):
    dependencies = [
        (
            "facility",
            "0394_rename_consultation_status_patientconsultation_route_to_facility_and_more",
        ),
    ]

    def clean_admission_date(apps, schema_editor):
        """
        Clean admission_date field to be 00:00:00 IST

        For example:

        `2023-10-06 06:00:00 +05:30 IST` (`2023-10-06 00:30:00 +00:00 UTC`) would be updated to
        `2023-10-06 00:00:00 +05:30 IST` (`2023-10-05 18:30:00 +00:00 UTC`)

        Equivalent to the following SQL:

        ```sql
        UPDATE facility_patientconsultation
        SET admission_date =
            timezone('IST', admission_date) AT TIME ZONE 'UTC' +
            (date_trunc('day', timezone('IST', admission_date)) - timezone('IST', admission_date)) +
            (interval '-5 hours -30 minutes')
        WHERE admission_date IS NOT NULL;
        ```
        """

        current_timezone = timezone.get_current_timezone()
        tz_offset = timezone.timedelta(
            minutes=current_timezone.utcoffset(datetime.datetime.utcnow()).seconds / 60
        )

        PatientConsultation = apps.get_model("facility", "PatientConsultation")
        PatientConsultation.objects.filter(admission_date__isnull=False).update(
            admission_date=ExpressionWrapper(
                # Convert the admission_date to UTC by subtracting the current offset
                F("admission_date") - tz_offset +
                # Get the day part of the admission_date and subtract the actual admission_date from it
                (TruncDay(F("admission_date")) - F("admission_date")),
                output_field=DurationField(),
            )
        )

    def migrate_route_to_facility(apps, schema_editor):
        PatientConsultation = apps.get_model("facility", "PatientConsultation")
        qs = PatientConsultation.objects.all()

        # Unknown -> None
        qs.filter(route_to_facility=0).update(route_to_facility=None)
        # Brought Dead/Outpatient -> Outpatient/Emergency Room
        qs.filter(models.Q(route_to_facility=1) | models.Q(route_to_facility=5)).update(
            route_to_facility=10
        )
        # Transferred from Ward/ICU -> Internal Transfer within facility
        qs.filter(models.Q(route_to_facility=2) | models.Q(route_to_facility=3)).update(
            route_to_facility=30
        )
        # Referred from other hospital -> Referred from another facility
        qs.filter(route_to_facility=4).update(route_to_facility=20)

    operations = [
        migrations.RunPython(
            clean_admission_date, reverse_code=migrations.RunPython.noop
        ),
        migrations.AlterField(
            model_name="patientconsultation",
            name="route_to_facility",
            field=models.SmallIntegerField(
                blank=True,
                choices=[
                    (None, "(Unknown)"),
                    (10, "Outpatient/Emergency Room"),
                    (20, "Referred from another facility"),
                    (30, "Internal Transfer within the facility"),
                ],
                null=True,
            ),
        ),
        migrations.RunPython(
            migrate_route_to_facility, reverse_code=migrations.RunPython.noop
        ),
    ]
