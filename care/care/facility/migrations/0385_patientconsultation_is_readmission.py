# Generated by Django 4.2.2 on 2023-09-01 16:22

from datetime import timedelta

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0384_patientconsultation_verified_by"),
    ]

    def fill_is_readmission(apps, schema_editor):
        PatientConsultation = apps.get_model("facility", "PatientConsultation")

        bulk_update_list = []
        qs = PatientConsultation.objects.filter(
            suggestion="A", admission_date__isnull=False
        )

        def is_readmission(consultation):
            # readmission=True if last_consultation.discharge_date is within 30 days of admission_date
            return qs.filter(
                patient=consultation.patient,
                created_date__lt=consultation.created_date,
                discharge_date__isnull=False,
                discharge_date__gte=consultation.admission_date - timedelta(days=30),
            ).exists()

        for consultation in qs:
            if is_readmission(consultation):
                consultation.is_readmission = True
                bulk_update_list.append(consultation)

        PatientConsultation.objects.bulk_update(
            bulk_update_list, ["is_readmission"], batch_size=1000
        )

    operations = [
        migrations.AddField(
            model_name="patientconsultation",
            name="is_readmission",
            field=models.BooleanField(default=False),
        ),
        migrations.RunPython(
            fill_is_readmission, reverse_code=migrations.RunPython.noop
        ),
    ]
