# Generated by Django 4.2.2 on 2023-09-11 11:56

from django.db import migrations
from django.db.models import Count


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0385_alter_patientconsultation_verified_by"),
    ]

    def remove_duplicate_assetbeds(apps, schema_editor):
        AssetBed = apps.get_model("facility", "AssetBed")

        queryset = AssetBed.objects.filter(
            deleted=False, asset__asset_class="HL7MONITOR"
        )

        to_delete = []

        # get all assetbeds that are not deleted and asset class is HL7MONITOR
        # and group by duplicate (asset, bed) pairs, and delete all but the
        # first one.
        for assetbed in (
            queryset.values("asset", "bed")
            .annotate(Count("id"))
            .filter(id__count__gt=1)
        ):
            to_delete.extend(
                queryset.filter(asset=assetbed["asset"], bed=assetbed["bed"])
                .order_by("modified_date")
                .values_list("id", flat=True)[1:]
            )

        AssetBed.objects.filter(id__in=to_delete).update(deleted=True)

    operations = [
        migrations.RunPython(
            remove_duplicate_assetbeds,
            reverse_code=migrations.RunPython.noop,
        )
    ]
