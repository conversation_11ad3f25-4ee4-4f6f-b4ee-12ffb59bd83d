# Generated by Django 4.2.7 on 2023-11-23 12:42

from django.db import migrations, models


def link_patient_notes_to_consultation(apps, schema_editor):
    PatientConsultation = apps.get_model("facility", "PatientConsultation")
    PatientNotes = apps.get_model("facility", "PatientNotes")

    consultations = PatientConsultation.objects.order_by("created_date").filter(
        patient__in=models.Subquery(PatientNotes.objects.values("patient_id"))
    )

    for consultation in consultations:
        notes = PatientNotes.objects.order_by("created_date").filter(
            patient_id=consultation.patient_id,
            created_date__gte=consultation.created_date,
        )
        if consultation.discharge_reason:
            notes = notes.filter(
                created_date__lte=consultation.modified_date,
            )

        notes.update(consultation=consultation)


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0398_patientnotes_consultation"),
    ]

    operations = [
        migrations.RunPython(
            code=link_patient_notes_to_consultation,
            reverse_code=migrations.RunPython.noop,
        ),
    ]
