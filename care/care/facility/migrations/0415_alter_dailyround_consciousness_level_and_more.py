# Generated by Django 4.2.8 on 2024-01-20 10:02

from django.db import migrations, models
from django.db.models import Q


def forwards_func(apps, schema_editor):
    DailyRound = apps.get_model("facility", "DailyRound")
    DailyRound.objects.filter(consciousness_level=0).update(consciousness_level=None)
    DailyRound.objects.filter(left_pupil_light_reaction=0).update(
        left_pupil_light_reaction=None
    )
    DailyRound.objects.filter(right_pupil_light_reaction=0).update(
        right_pupil_light_reaction=None
    )
    DailyRound.objects.filter(limb_response_upper_extremity_left=0).update(
        limb_response_upper_extremity_left=None
    )
    DailyRound.objects.filter(limb_response_lower_extremity_right=0).update(
        limb_response_lower_extremity_right=None
    )
    DailyRound.objects.filter(rhythm=0).update(rhythm=None)
    DailyRound.objects.filter(ventilator_mode=0).update(ventilator_mode=None)
    DailyRound.objects.filter(ventilator_interface=0).update(ventilator_interface=None)
    DailyRound.objects.filter(ventilator_oxygen_modality=0).update(
        ventilator_oxygen_modality=None
    )
    DailyRound.objects.filter(insulin_intake_frequency=0).update(
        insulin_intake_frequency=None
    )
    DailyRound.objects.filter(Q(bp__systolic__lt=0) | Q(bp__diastolic__lt=0)).update(
        bp={"systolic": None, "diastolic": None}
    )


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0414_remove_bed_old_name"),
    ]

    operations = [
        migrations.AlterField(
            model_name="dailyround",
            name="consciousness_level",
            field=models.IntegerField(
                choices=[
                    (0, "UNKNOWN"),
                    (5, "ALERT"),
                    (10, "RESPONDS_TO_VOICE"),
                    (15, "RESPONDS_TO_PAIN"),
                    (20, "UNRESPONSIVE"),
                    (25, "AGITATED_OR_CONFUSED"),
                    (30, "ONSET_OF_AGITATION_AND_CONFUSION"),
                ],
                default=None,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="left_pupil_light_reaction",
            field=models.IntegerField(
                choices=[
                    (0, "UNKNOWN"),
                    (5, "BRISK"),
                    (10, "SLUGGISH"),
                    (15, "FIXED"),
                    (20, "CANNOT_BE_ASSESSED"),
                ],
                default=None,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="limb_response_lower_extremity_left",
            field=models.IntegerField(
                choices=[
                    (0, "UNKNOWN"),
                    (5, "STRONG"),
                    (10, "MODERATE"),
                    (15, "WEAK"),
                    (20, "FLEXION"),
                    (25, "EXTENSION"),
                    (30, "NONE"),
                ],
                default=None,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="limb_response_lower_extremity_right",
            field=models.IntegerField(
                choices=[
                    (0, "UNKNOWN"),
                    (5, "STRONG"),
                    (10, "MODERATE"),
                    (15, "WEAK"),
                    (20, "FLEXION"),
                    (25, "EXTENSION"),
                    (30, "NONE"),
                ],
                default=None,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="limb_response_upper_extremity_left",
            field=models.IntegerField(
                choices=[
                    (0, "UNKNOWN"),
                    (5, "STRONG"),
                    (10, "MODERATE"),
                    (15, "WEAK"),
                    (20, "FLEXION"),
                    (25, "EXTENSION"),
                    (30, "NONE"),
                ],
                default=None,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="limb_response_upper_extremity_right",
            field=models.IntegerField(
                choices=[
                    (0, "UNKNOWN"),
                    (5, "STRONG"),
                    (10, "MODERATE"),
                    (15, "WEAK"),
                    (20, "FLEXION"),
                    (25, "EXTENSION"),
                    (30, "NONE"),
                ],
                default=None,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="rhythm",
            field=models.IntegerField(
                choices=[(0, "UNKNOWN"), (5, "REGULAR"), (10, "IRREGULAR")],
                default=None,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="right_pupil_light_reaction",
            field=models.IntegerField(
                choices=[
                    (0, "UNKNOWN"),
                    (5, "BRISK"),
                    (10, "SLUGGISH"),
                    (15, "FIXED"),
                    (20, "CANNOT_BE_ASSESSED"),
                ],
                default=None,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="ventilator_interface",
            field=models.IntegerField(
                choices=[
                    (0, "UNKNOWN"),
                    (5, "INVASIVE"),
                    (10, "NON_INVASIVE"),
                    (15, "OXYGEN_SUPPORT"),
                ],
                default=None,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="ventilator_mode",
            field=models.IntegerField(
                choices=[
                    (0, "UNKNOWN"),
                    (5, "VCV"),
                    (10, "PCV"),
                    (15, "PRVC"),
                    (20, "APRV"),
                    (25, "VC_SIMV"),
                    (30, "PC_SIMV"),
                    (40, "PRVC_SIMV"),
                    (45, "ASV"),
                    (50, "PSV"),
                ],
                default=None,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="ventilator_oxygen_modality",
            field=models.IntegerField(
                choices=[
                    (0, "UNKNOWN"),
                    (5, "NASAL_PRONGS"),
                    (10, "SIMPLE_FACE_MASK"),
                    (15, "NON_REBREATHING_MASK"),
                    (20, "HIGH_FLOW_NASAL_CANNULA"),
                ],
                default=None,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="dailyround",
            name="insulin_intake_frequency",
            field=models.IntegerField(
                choices=[(0, "UNKNOWN"), (5, "OD"), (10, "BD"), (15, "TD")],
                default=None,
                null=True,
            ),
        ),
        migrations.RunPython(forwards_func, migrations.RunPython.noop),
    ]
