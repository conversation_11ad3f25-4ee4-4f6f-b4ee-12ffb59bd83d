# Generated by Django 4.2.10 on 2024-05-21 12:29

from django.db import migrations, models


class Migration(migrations.Migration):
    def rename_categories_in_events(apps, schema_editor):
        PatientConsultationEvent = apps.get_model(
            "facility", "PatientConsultationEvent"
        )

        PatientConsultationEvent.objects.filter(
            event_type__name="CATEGORY", value__category="Stable"
        ).update(value={"category": "Mild"})
        PatientConsultationEvent.objects.filter(
            event_type__name="PATIENT_CATEGORY", value__category="Stable"
        ).update(value={"patient_category": "Mild"})
        PatientConsultationEvent.objects.filter(
            event_type__name="CATEGORY", value__category="Abnormal"
        ).update(value={"category": "Moderate"})
        PatientConsultationEvent.objects.filter(
            event_type__name="PATIENT_CATEGORY", value__category="Abnormal"
        ).update(value={"patient_category": "Moderate"})

    dependencies = [
        ("facility", "0437_alter_dailyround_rounds_type"),
    ]

    operations = [
        migrations.AlterField(
            model_name="dailyround",
            name="patient_category",
            field=models.CharField(
                choices=[
                    ("Comfort", "Comfort Care"),
                    ("Stable", "Mild"),
                    ("Moderate", "Moderate"),
                    ("Critical", "Critical"),
                ],
                max_length=8,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="patientconsultation",
            name="category",
            field=models.CharField(
                choices=[
                    ("Comfort", "Comfort Care"),
                    ("Stable", "Mild"),
                    ("Moderate", "Moderate"),
                    ("Critical", "Critical"),
                ],
                max_length=8,
                null=True,
            ),
        ),
    ]
