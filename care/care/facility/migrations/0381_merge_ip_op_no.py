# Generated by Django 4.2.2 on 2023-08-22 11:54

from django.core.paginator import Paginator
from django.db import migrations
from django.db.models import Cha<PERSON><PERSON><PERSON>, Q


def merge_ip_op_no(apps, schema_editor):
    PatientConsultation = apps.get_model("facility", "PatientConsultation")

    patients = (
        PatientConsultation.objects.filter(Q(op_no__isnull=False))
        .exclude(op_no="")
        .only("op_no", "patient_no")
        .order_by("id")
    )

    paginator = Paginator(patients, 1000)
    for page_number in paginator.page_range:
        bulk = []
        for patient in paginator.page(page_number).object_list:
            if not patient.op_no.strip():
                continue
            if patient.patient_no:
                patient.patient_no = f"{patient.patient_no} | {patient.op_no}"
            else:
                patient.patient_no = patient.op_no
            bulk.append(patient)

        PatientConsultation.objects.bulk_update(bulk, ["patient_no"])


class Migration(migrations.Migration):
    dependencies = [
        (
            "facility",
            "0380_patientnotes_user_type",
        ),
    ]

    operations = [
        migrations.RenameField(
            model_name="patientconsultation",
            old_name="ip_no",
            new_name="patient_no",
        ),
        migrations.RunPython(merge_ip_op_no, migrations.RunPython.noop),
        migrations.RemoveField(model_name="patientconsultation", name="op_no"),
        migrations.AlterField(
            model_name="patientconsultation",
            name="patient_no",
            field=CharField(
                blank=True,
                default="",
                help_text="Patient's unique number in the facility. IP number for inpatients and OP number for outpatients.",
                max_length=100,
                null=True,
            ),
        ),
    ]
