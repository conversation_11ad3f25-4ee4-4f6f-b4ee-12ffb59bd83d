# Generated by Django 5.1.3 on 2024-12-13 12:55

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('facility', '0467_alter_hospitaldoctors_area'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='resourcerequest',
            name='assigned_quantity',
        ),
        migrations.RemoveField(
            model_name='resourcerequest',
            name='requested_quantity',
        ),
        migrations.RemoveField(
            model_name='resourcerequest',
            name='sub_category',
        ),
        migrations.AddField(
            model_name='resourcerequest',
            name='related_patient',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='facility.patientregistration'),
        ),
        migrations.AlterField(
            model_name='resourcerequest',
            name='category',
            field=models.IntegerField(choices=[(10, 'PATIENT_CARE'), (20, 'COMFORT_DEVICES'), (30, 'MEDICINES'), (100, 'OTHERS'), (200, 'SUPPLIES')], default=100),
        ),
    ]
