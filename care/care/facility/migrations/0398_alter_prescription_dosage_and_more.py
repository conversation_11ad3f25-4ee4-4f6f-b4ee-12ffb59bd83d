# Generated by Django 4.2.5 on 2023-12-06 13:06

from django.db import migrations, models

import care.utils.models.validators


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0397_truncate_discharge_time"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="prescription",
            name="dosage",
            field=models.CharField(
                blank=True,
                max_length=100,
                null=True,
                validators=[
                    care.utils.models.validators.DenominationValidator(
                        allow_floats=True,
                        max_amount=5000,
                        min_amount=0.0001,
                        precision=4,
                        units={"mg", "g", "ml", "tsp", "ampule(s)", "drop(s)"},
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="prescription",
            name="max_dosage",
            field=models.CharField(
                blank=True,
                max_length=100,
                null=True,
                validators=[
                    care.utils.models.validators.DenominationValidator(
                        allow_floats=True,
                        max_amount=5000,
                        min_amount=0.0001,
                        precision=4,
                        units={"mg", "g", "ml", "tsp", "ampule(s)", "drop(s)"},
                    )
                ],
            ),
        ),
    ]
