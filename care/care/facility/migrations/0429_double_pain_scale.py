# Generated by Django 4.2.10 on 2024-03-12 17:48

from math import ceil

from django.core.paginator import Paginator
from django.db import migrations, models

import care.utils.models.validators


def double_pain_scale(apps, schema_editor):
    DailyRound = apps.get_model("facility", "DailyRound")

    page = Paginator(
        DailyRound.objects.only("id", "pain_scale_enhanced")
        .exclude(pain_scale_enhanced__exact=[])
        .order_by("id"),
        2000,
    )

    for page_num in page.page_range:
        records_to_update = []
        for daily_round in page.page(page_num):
            for obj in daily_round.pain_scale_enhanced:
                try:
                    obj["scale"] *= 2
                except KeyError:
                    pass
            records_to_update.append(daily_round)
        DailyRound.objects.bulk_update(records_to_update, ["pain_scale_enhanced"])


def halve_pain_scale(apps, schema_editor):
    DailyRound = apps.get_model("facility", "DailyRound")
    page = Paginator(
        DailyRound.objects.only("id", "pain_scale_enhanced")
        .exclude(pain_scale_enhanced__exact=[])
        .order_by("id"),
        2000,
    )

    for page_num in page.page_range:
        records_to_update = []
        for daily_round in page.page(page_num):
            for obj in daily_round.pain_scale_enhanced:
                try:
                    obj["scale"] = ceil(obj["scale"] / 2)
                except KeyError:
                    pass
            records_to_update.append(daily_round)
        DailyRound.objects.bulk_update(records_to_update, ["pain_scale_enhanced"])


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0428_alter_patientmetainfo_occupation"),
    ]

    operations = [
        migrations.AlterField(
            model_name="dailyround",
            name="pain_scale_enhanced",
            field=models.JSONField(
                default=list,
                validators=[
                    care.utils.models.validators.JSONFieldSchemaValidator(
                        {
                            "$schema": "http://json-schema.org/draft-07/schema#",
                            "items": [
                                {
                                    "additionalProperties": False,
                                    "properties": {
                                        "description": {"type": "string"},
                                        "region": {"type": "string"},
                                        "scale": {
                                            "maximum": 10,
                                            "minimum": 1,
                                            "type": "number",
                                        },
                                    },
                                    "required": ["region", "scale"],
                                    "type": "object",
                                }
                            ],
                            "type": "array",
                        }
                    )
                ],
            ),
        ),
        migrations.RunPython(double_pain_scale, reverse_code=halve_pain_scale),
    ]
