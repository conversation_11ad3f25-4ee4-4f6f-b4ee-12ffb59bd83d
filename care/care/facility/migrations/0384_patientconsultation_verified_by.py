# Generated by Django 4.2.2 on 2023-08-31 04:31

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("facility", "0383_patientconsultation_icd11_principal_diagnosis"),
    ]

    operations = [
        migrations.RenameField(
            model_name="patientconsultation",
            old_name="verified_by",
            new_name="deprecated_verified_by",
        ),
        migrations.AddField(
            model_name="patientconsultation",
            name="verified_by",
            field=models.ForeignKey(
                null=True,
                blank=False,
                on_delete=django.db.models.deletion.SET_NULL,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
