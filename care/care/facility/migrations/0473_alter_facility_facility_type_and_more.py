# Generated by Django 5.1.3 on 2024-12-22 07:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('facility', '0472_historicalpatientregistration_mobility_status_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='facility',
            name='facility_type',
            field=models.IntegerField(choices=[(1, 'Educational Inst'), (2, 'Private Hospital'), (3, 'Other'), (4, 'Hostel'), (5, 'Hotel'), (6, 'Lodge'), (7, 'TeleMedicine'), (9, 'Govt Labs'), (10, 'Private Labs'), (800, 'Primary Health Centres'), (802, 'Family Health Centres'), (803, 'Community Health Centres'), (830, 'Taluk Hospitals'), (840, 'Women and Child Health Centres'), (860, 'District Hospitals'), (870, 'Govt Medical College Hospitals'), (900, 'Co-operative hospitals'), (910, 'Autonomous healthcare facility'), (1010, 'COVID-19 Domiciliary Care Center'), (1100, 'First Line Treatment Centre'), (1200, 'Second Line Treatment Center'), (1300, 'Shifting Centre'), (1400, 'Covid Management Center'), (1500, 'Request Approving Center'), (1510, 'Request Fulfilment Center'), (1600, 'District War Room'), (3000, 'Non Governmental Organization'), (4000, 'Community Based Organization')]),
        ),
        migrations.AlterField(
            model_name='shiftingrequest',
            name='assigned_facility_type',
            field=models.IntegerField(blank=True, choices=[(1, 'Educational Inst'), (2, 'Private Hospital'), (3, 'Other'), (4, 'Hostel'), (5, 'Hotel'), (6, 'Lodge'), (7, 'TeleMedicine'), (9, 'Govt Labs'), (10, 'Private Labs'), (800, 'Primary Health Centres'), (802, 'Family Health Centres'), (803, 'Community Health Centres'), (830, 'Taluk Hospitals'), (840, 'Women and Child Health Centres'), (860, 'District Hospitals'), (870, 'Govt Medical College Hospitals'), (900, 'Co-operative hospitals'), (910, 'Autonomous healthcare facility'), (1010, 'COVID-19 Domiciliary Care Center'), (1100, 'First Line Treatment Centre'), (1200, 'Second Line Treatment Center'), (1300, 'Shifting Centre'), (1400, 'Covid Management Center'), (1500, 'Request Approving Center'), (1510, 'Request Fulfilment Center'), (1600, 'District War Room'), (3000, 'Non Governmental Organization'), (4000, 'Community Based Organization')], default=None, null=True),
        ),
    ]
