# Generated by Django 4.2.10 on 2024-08-20 12:38

from django.core.paginator import Paginator
from django.db import migrations


class Migration(migrations.Migration):
    """
    Migration to correct the entries with spelling mistake for "Rules Tube Aspiration"
    to "Ryles Tube Aspiration" in both DailyRound's output JSONField.
    """

    dependencies = [
        ("facility", "0449_merge_20240822_1343"),
    ]

    def forward_rename_dailyround_entries(apps, schema_editor):
        DailyRound = apps.get_model("facility", "DailyRound")

        paginator = Paginator(
            DailyRound.objects.filter(
                output__contains=[{"name": "Rules Tube Aspiration"}]
            ).order_by("id"),
            1000,
        )

        for page_number in paginator.page_range:
            bulk = []
            for instance in paginator.page(page_number).object_list:
                for entry in instance.output:
                    if entry["name"] == "Rules Tube Aspiration":
                        entry["name"] = "Ryles Tube Aspiration"
                bulk.append(instance)
            DailyRound.objects.bulk_update(bulk, ["output"])

    def reverse_rename_dailyround_entries(apps, schema_editor):
        DailyRound = apps.get_model("facility", "DailyRound")

        paginator = Paginator(
            DailyRound.objects.filter(
                output__contains=[{"name": "Ryles Tube Aspiration"}]
            ).order_by("id"),
            1000,
        )

        for page_number in paginator.page_range:
            bulk = []
            for instance in paginator.page(page_number).object_list:
                for entry in instance.output:
                    if entry["name"] == "Ryles Tube Aspiration":
                        entry["name"] = "Rules Tube Aspiration"
                bulk.append(instance)
            DailyRound.objects.bulk_update(bulk, ["output"])

    operations = [
        migrations.RunPython(
            forward_rename_dailyround_entries,
            reverse_code=reverse_rename_dailyround_entries,
        ),
    ]
