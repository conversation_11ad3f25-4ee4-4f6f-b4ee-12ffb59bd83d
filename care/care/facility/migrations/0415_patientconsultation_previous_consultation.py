# Generated by Django 4.2.10 on 2024-02-18 06:37
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("facility", "0414_remove_bed_old_name"),
    ]

    def fill_previous_consultation(apps, schema_editor):
        PatientConsultation = apps.get_model("facility", "PatientConsultation")
        bulk_update_list = []
        qs = PatientConsultation.objects.all().order_by("-created_date")

        def get_previous_consultation(consultation):
            previous_consultation = qs.filter(
                patient=consultation.patient,
                created_date__lt=consultation.created_date,
            ).latest("created_date")

            return previous_consultation

        for consultation in qs:
            try:
                consultation.previous_consultation = get_previous_consultation(
                    consultation
                )
                bulk_update_list.append(consultation)
            except PatientConsultation.DoesNotExist:
                pass

        PatientConsultation.objects.bulk_update(
            bulk_update_list, ["previous_consultation"], batch_size=1000
        )

    operations = [
        migrations.AddField(
            model_name="patientconsultation",
            name="previous_consultation",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="facility.patientconsultation",
            ),
        ),
        migrations.RunPython(
            fill_previous_consultation,
            reverse_code=migrations.RunPython.noop,
        ),
    ]
