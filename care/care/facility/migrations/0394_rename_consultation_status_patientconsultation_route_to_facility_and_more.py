# Generated by Django 4.2.5 on 2023-11-14 06:22

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        (
            "facility",
            "0393_rename_diagnosis_patientconsultation_deprecated_diagnosis_and_more",
        ),
    ]

    operations = [
        migrations.RenameField(
            model_name="patientconsultation",
            old_name="consultation_status",
            new_name="route_to_facility",
        ),
        migrations.RenameField(
            model_name="patientconsultation",
            old_name="verified_by",
            new_name="treating_physician",
        ),
        migrations.AddField(
            model_name="patientconsultation",
            name="icu_admission_date",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="patientconsultation",
            name="referred_by_external",
            field=models.TextField(blank=True, default="", null=True),
        ),
        migrations.AddField(
            model_name="patientconsultation",
            name="referred_from_facility",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="facility.facility",
            ),
        ),
        migrations.AddField(
            model_name="patientconsultation",
            name="referred_from_facility_external",
            field=models.TextField(blank=True, default="", null=True),
        ),
        migrations.AddField(
            model_name="patientconsultation",
            name="transferred_from_location",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="facility.assetlocation",
            ),
        ),
    ]
