from care.security.authorization import Authorization<PERSON><PERSON>roller
from care.security.authorization.base import Authorization<PERSON>and<PERSON>
from care.security.permissions.tag_config import TagConfigPermissions


class TagConfigAccess(AuthorizationHandler):
    def can_list_facility_tag_config(self, user, facility):
        """
        Check if the user has permission to view tag configs in the facility
        """
        return self.check_permission_in_facility_organization(
            [TagConfigPermissions.can_read_tag_config.name],
            user,
            facility=facility,
        )

    def can_write_facility_tag_config(self, user, facility):
        """
        Check if the user has permission to view tag configs in the facility
        """
        return self.check_permission_in_facility_organization(
            [TagConfigPermissions.can_write_tag_config.name],
            user,
            facility=facility,
            root=True,
        )


AuthorizationController.register_internal_controller(TagConfigAccess)
