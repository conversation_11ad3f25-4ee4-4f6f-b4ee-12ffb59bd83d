# Generated by Django 5.1.4 on 2025-01-22 12:35

from django.db import migrations, models


def fix_encounter_diagnosis_category_case(apps, schema_editor):
    Condition = apps.get_model("emr", "Condition")
    Condition.objects.filter(category="encounter-diagnosis").update(category="encounter_diagnosis")

def revert_encounter_diagnosis_category_case(apps, schema_editor):
    Condition = apps.get_model("emr", "Condition")
    Condition.objects.filter(category="encounter_diagnosis").update(category="encounter-diagnosis")

class Migration(migrations.Migration):

    dependencies = [
        ('emr', '0009_medicationrequest_authored_on'),
    ]

    operations = [
        migrations.AddField(
            model_name='condition',
            name='abatement',
            field=models.JSONField(default=dict),
        ),
        migrations.RunPython(
            fix_encounter_diagnosis_category_case,
            revert_encounter_diagnosis_category_case,
        )
    ]
