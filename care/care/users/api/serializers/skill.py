from rest_framework.serializers import <PERSON><PERSON><PERSON><PERSON>, ModelSerializer, <PERSON>UI<PERSON>ield

from care.users.models import Skill, UserSkill


class SkillSerializer(ModelSerializer):
    id = UUIDField(source="external_id", read_only=True)

    class Meta:
        model = Skill
        fields = ("id", "name", "description")


class UserSkillSerializer(ModelSerializer):
    id = UUIDField(source="skill.external_id", read_only=True)
    name = Char<PERSON>ield(source="skill.name", read_only=True)
    description = CharField(source="skill.description", read_only=True)

    class Meta:
        model = UserSkill
        fields = ("id", "name", "description")
