# Generated by Django 5.1.3 on 2024-12-29 21:20

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('emr', '0001_initial'),
        ('users', '0020_plugconfig'),
    ]

    operations = [
        migrations.AlterField(
            model_name='user',
            name='gender',
            field=models.IntegerField(blank=True, choices=[(1, 'Male'), (2, 'Female'), (3, 'Non-binary')], default=None,
                                      null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='user_type',
            field=models.IntegerField(blank=True, choices=[(2, 'Transportation'), (3, 'Pharmacist'), (5, 'Volunteer'),
                                                           (9, 'StaffReadOnly'), (10, 'Staff'), (13, 'NurseReadOnly'),
                                                           (14, 'Nurse'), (15, 'Doctor'), (20, 'Reserved'),
                                                           (21, 'WardAdmin'), (23, 'LocalBodyAdmin'),
                                                           (25, 'DistrictLabAdmin'), (29, 'DistrictReadOnlyAdmin'),
                                                           (30, 'DistrictAdmin'), (35, 'StateLabAdmin'),
                                                           (39, 'StateReadOnlyAdmin'), (40, 'StateAdmin')],
                                      default=None, null=True),
        ),
        migrations.RenameField(
            model_name='user',
            old_name='gender',
            new_name='old_gender',
        ),
        migrations.RenameField(
            model_name='user',
            old_name='user_type',
            new_name='old_user_type',
        ),
        migrations.AddField(
            model_name='user',
            name='geo_organization',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='emr.organization'),
        ),
        migrations.AddField(
            model_name='user',
            name='user_type',
            field=models.CharField(max_length=100,null=True,blank=True),
        ),
        migrations.AddField(
            model_name='user',
            name='gender',
            field=models.CharField(max_length=100 , null=True,blank=True),
        ),

    ]
