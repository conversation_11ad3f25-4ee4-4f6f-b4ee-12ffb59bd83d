# Generated by Django 2.2.11 on 2023-06-13 10:51

import uuid

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models

import care.users.models


class Migration(migrations.Migration):
    initial = True
    replaces = [
        ("users", "0001_initial"),
        ("users", "0002_auto_20200319_0634"),
        ("users", "0003_user_verified"),
        ("users", "0004_auto_20200321_0556"),
        ("users", "0005_auto_20200321_0601"),
        ("users", "0006_user_deleted"),
        ("users", "0007_auto_20200321_2029"),
        ("users", "0008_auto_20200321_2035"),
        ("users", "0009_auto_20200325_1908"),
        ("users", "0010_populate_district"),
        ("users", "0011_map_users_to_district"),
        ("users", "0012_auto_20200326_0342"),
        ("users", "0013_auto_20200327_0437"),
        ("users", "0014_restart_sequence_districts"),
        ("users", "0012_auto_20200326_1752"),
        ("users", "0013_auto_20200326_2021"),
        ("users", "0015_merge_20200327_1215"),
        ("users", "0016_auto_20200327_1954"),
        ("users", "0017_auto_20200328_2256"),
        ("users", "0018_auto_20200328_1853"),
        ("users", "0019_auto_20200328_2226"),
        ("users", "0020_auto_20200401_0930"),
        ("users", "0021_make_kerala_everyones_state"),
        ("users", "0022_auto_verify_users_with_facility"),
        ("users", "0023_auto_20200413_1301"),
        ("users", "0024_auto_20200801_1844"),
        ("users", "0025_auto_20200914_2027"),
        ("users", "0026_auto_20200914_2034"),
        ("users", "0027_auto_20200914_2052"),
        ("users", "0028_auto_20200916_0008"),
        ("users", "0029_ward"),
        ("users", "0030_auto_20200921_1659"),
        ("users", "0031_auto_20200927_1325"),
        ("users", "0032_user_ward"),
        ("users", "0033_auto_20201011_1908"),
        ("users", "0034_auto_20201122_2013"),
        ("users", "0035_auto_20210511_2105"),
        ("users", "0036_auto_20210515_2048"),
        ("users", "0037_auto_20210519_1826"),
        ("users", "0038_user_alt_phone_number"),
        ("users", "0039_auto_20210616_1634"),
        ("users", "0040_auto_20210616_1821"),
        ("users", "0041_user_asset"),
        ("users", "0042_user_created_by"),
        ("users", "0043_auto_20220624_1119"),
        ("users", "0044_user_home_facility"),
        ("users", "0045_auto_20230110_1120"),
        ("users", "0046_auto_20230204_1733"),
        ("users", "0047_user_external_id"),
        ("users", "0048_auto_20230609_1411"),
        ("users", "0049_auto_20230609_1413"),
    ]

    dependencies = [
        ("auth", "0011_update_proxy_permissions"),
    ]

    operations = [
        migrations.CreateModel(
            name="User",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=30, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="last name"
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True, max_length=254, verbose_name="email address"
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user should be treated as active. Unselect this instead of deleting accounts.",
                        verbose_name="active",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "username",
                    models.CharField(
                        error_messages={
                            "unique": "A user with that username already exists."
                        },
                        help_text="150 characters or fewer. Letters, digits and @/./+/-/_ only.",
                        max_length=150,
                        unique=True,
                        validators=[care.users.models.UsernameValidator()],
                        verbose_name="username",
                    ),
                ),
                (
                    "user_type",
                    models.IntegerField(
                        choices=[
                            (2, "Transportation"),
                            (3, "Pharmacist"),
                            (5, "Volunteer"),
                            (9, "StaffReadOnly"),
                            (10, "Staff"),
                            (15, "Doctor"),
                            (20, "Reserved"),
                            (21, "WardAdmin"),
                            (23, "LocalBodyAdmin"),
                            (25, "DistrictLabAdmin"),
                            (29, "DistrictReadOnlyAdmin"),
                            (30, "DistrictAdmin"),
                            (35, "StateLabAdmin"),
                            (39, "StateReadOnlyAdmin"),
                            (40, "StateAdmin"),
                        ]
                    ),
                ),
                (
                    "phone_number",
                    models.CharField(
                        max_length=14,
                        validators=[
                            django.core.validators.RegexValidator(
                                code="invalid_mobile",
                                message="Please Enter 10/11 digit mobile number or landline as 0<std code><phone number>",
                                regex="^((\\+91|91|0)[\\- ]{0,1})?[456789]\\d{9}$",
                            )
                        ],
                    ),
                ),
                (
                    "alt_phone_number",
                    models.CharField(
                        blank=True,
                        default=None,
                        max_length=14,
                        null=True,
                        validators=[
                            django.core.validators.RegexValidator(
                                code="invalid_mobile",
                                message="Please Enter 10/11 digit mobile number or landline as 0<std code><phone number>",
                                regex="^((\\+91|91|0)[\\- ]{0,1})?[456789]\\d{9}$",
                            )
                        ],
                    ),
                ),
                (
                    "gender",
                    models.IntegerField(
                        choices=[(1, "Male"), (2, "Female"), (3, "Non-binary")]
                    ),
                ),
                (
                    "age",
                    models.IntegerField(
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(100),
                        ]
                    ),
                ),
                ("doctor_qualification", models.TextField(null=True)),
                (
                    "doctor_experience_commenced_on",
                    models.DateField(default=None, null=True),
                ),
                (
                    "doctor_medical_council_registration",
                    models.CharField(default=None, max_length=255, null=True),
                ),
                ("verified", models.BooleanField(default=False)),
                ("deleted", models.BooleanField(default=False)),
                ("pf_endpoint", models.TextField(default=None, null=True)),
                ("pf_p256dh", models.TextField(default=None, null=True)),
                ("pf_auth", models.TextField(default=None, null=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="users_created",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "user",
                "verbose_name_plural": "users",
                "abstract": False,
            },
            managers=[
                ("objects", care.users.models.CustomUserManager()),
            ],
        ),
        migrations.CreateModel(
            name="District",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name="LocalBody",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                (
                    "body_type",
                    models.IntegerField(
                        choices=[
                            (1, "Grama Panchayath"),
                            (2, "Block Panchayath"),
                            (3, "District Panchayath"),
                            (4, "Nagar Panchayath"),
                            (10, "Municipality"),
                            (20, "Corporation"),
                            (50, "Others"),
                        ]
                    ),
                ),
                (
                    "localbody_code",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "district",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="users.District"
                    ),
                ),
            ],
            options={
                "verbose_name": "Local Body",
                "verbose_name_plural": "Local Bodies",
            },
        ),
        migrations.CreateModel(
            name="Skill",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                ("name", models.CharField(max_length=255, unique=True)),
                ("description", models.TextField(blank=True, default="", null=True)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="State",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name="Ward",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("number", models.IntegerField()),
                (
                    "local_body",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="users.LocalBody",
                    ),
                ),
            ],
            options={
                "unique_together": {("local_body", "name", "number")},
            },
        ),
        migrations.CreateModel(
            name="UserSkill",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "external_id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
                ),
                (
                    "created_date",
                    models.DateTimeField(auto_now_add=True, db_index=True, null=True),
                ),
                (
                    "modified_date",
                    models.DateTimeField(auto_now=True, db_index=True, null=True),
                ),
                ("deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "skill",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="users.Skill",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="district",
            name="state",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="users.State"
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="district",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="users.District",
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="groups",
            field=models.ManyToManyField(
                blank=True,
                help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                related_name="user_set",
                related_query_name="user",
                to="auth.Group",
                verbose_name="groups",
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="local_body",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="users.LocalBody",
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="skills",
            field=models.ManyToManyField(through="users.UserSkill", to="users.Skill"),
        ),
        migrations.AddField(
            model_name="user",
            name="state",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="users.State",
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="user_permissions",
            field=models.ManyToManyField(
                blank=True,
                help_text="Specific permissions for this user.",
                related_name="user_set",
                related_query_name="user",
                to="auth.Permission",
                verbose_name="user permissions",
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="ward",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="users.Ward",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="localbody",
            unique_together={("district", "body_type", "name")},
        ),
    ]
