# Generated by Django 4.2.2 on 2023-08-07 13:23

from django.db import migrations, models

import care.utils.models.validators


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0006_user_weekly_working_hours"),
    ]

    operations = [
        migrations.<PERSON>er<PERSON>ield(
            model_name="user",
            name="alt_phone_number",
            field=models.CharField(
                blank=True,
                default=None,
                max_length=14,
                null=True,
                validators=[
                    care.utils.models.validators.PhoneNumberValidator(types=("mobile",))
                ],
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="phone_number",
            field=models.Char<PERSON>ield(
                max_length=14,
                validators=[
                    care.utils.models.validators.PhoneNumberValidator(
                        types=("mobile", "landline")
                    )
                ],
            ),
        ),
    ]
