# Generated by Django 2.2.11 on 2023-06-13 11:27

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0002_auto_20230613_1622"),
    ]

    operations = [
        migrations.RunSQL(
            sql="DROP INDEX IF EXISTS users_users_skill_i_6ebc32_partial",
            reverse_sql=migrations.RunSQL.noop,
        ),
        migrations.AddConstraint(
            model_name="userskill",
            constraint=models.UniqueConstraint(
                condition=models.Q(deleted=False),
                fields=("skill", "user"),
                name="unique_user_skill",
            ),
        ),
    ]
