# Generated by Django 4.2.8 on 2024-01-31 14:00

from django.db import migrations, models

import care.utils.models.validators


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0013_staff_to_nurse"),
    ]

    operations = [
        migrations.AlterField(
            model_name="user",
            name="username",
            field=models.CharField(
                error_messages={"unique": "A user with that username already exists."},
                help_text="Username must be 4 to 16 characters long. It may only contain lowercase alphabets, numbers, underscores, hyphens and dots. It shouldn't start or end with underscores, hyphens or dots. It shouldn't contain consecutive underscores, hyphens or dots.",
                max_length=150,
                unique=True,
                validators=[care.utils.models.validators.UsernameValidator()],
                verbose_name="username",
            ),
        ),
    ]
