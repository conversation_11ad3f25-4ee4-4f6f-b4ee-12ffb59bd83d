# Generated by Django 4.2.10 on 2024-04-18 05:42

from django.db import migrations


def add_skills(apps, schema_editor):
    Skill = apps.get_model("users", "Skill")
    if Skill.objects.exists():
        skills = [
            "Dentist",
            "Geriatrician",
            "Immunologist",
            "Infectious Disease Specialist",
            "MBBS doctor",
            "Oral and Maxillofacial Surgeon",
            "Pediatric Surgeon",
            "Sports Medicine Specialist",
            "Transfusion Medicine Specialist",
        ]
        for skill in skills:
            Skill.objects.get_or_create(name=skill)


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0015_age_to_dateofbirth"),
    ]

    operations = [
        migrations.RunPython(add_skills),
    ]
