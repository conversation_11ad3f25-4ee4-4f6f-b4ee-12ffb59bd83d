# Generated by Django 2.2.11 on 2020-03-25 19:08

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0008_auto_20200321_2035"),
    ]

    operations = [
        migrations.CreateModel(
            name="District",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name="State",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name="LocalBody",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.Char<PERSON>ield(max_length=255)),
                (
                    "type",
                    models.IntegerField(
                        choices=[
                            (1, "Panchayath"),
                            (2, "Municipality"),
                            (3, "Corporation"),
                            (25, "Others"),
                        ]
                    ),
                ),
                (
                    "district",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="users.District"
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="district",
            name="state",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="users.State"
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="new_district",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="users.District",
            ),
        ),
    ]
