# Generated by Django 2.2.11 on 2020-03-25 19:20
from django.contrib.auth import get_user_model
from django.db import migrations


def map_users_to_districts(apps, *args):
    User = get_user_model()
    District = apps.get_model("users", "District")

    districts = District.objects.all()
    district_map = {d.id: d for d in districts}

    dummy_user = User()
    if hasattr(dummy_user, "new_district"):
        for user in User.objects.filter(deleted=False):
            d = district_map.get(user.district)
            if d is not None:
                user.new_district_id = d.id
                user.save()


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0010_populate_district"),
    ]

    operations = [
        migrations.RunPython(map_users_to_districts),
    ]
