# Generated by Django 2.2.11 on 2020-03-26 03:42

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0011_map_users_to_district"),
    ]

    operations = [
        migrations.AddField(
            model_name="localbody",
            name="localbody_code",
            field=models.CharField(blank=True, max_length=20),
        ),
        migrations.AlterField(
            model_name="localbody",
            name="type",
            field=models.IntegerField(
                choices=[
                    (1, "Grama Panchayath"),
                    (2, "Block Panchayath"),
                    (3, "District Panchayath"),
                    (10, "Municipality"),
                    (20, "Corporation"),
                    (50, "Others"),
                ]
            ),
        ),
        migrations.AlterUniqueTogether(
            name="localbody",
            unique_together={("district", "type", "name")},
        ),
    ]
