# Generated by Django 2.2.11 on 2021-06-01 19:32

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0037_auto_20210519_1826"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="alt_phone_number",
            field=models.CharField(
                blank=True,
                default=None,
                max_length=14,
                null=True,
                validators=[
                    django.core.validators.RegexValidator(
                        code="invalid_mobile",
                        message="Please Enter 10/11 digit mobile number or landline as 0<std code><phone number>",
                        regex="^((\\+91|91|0)[\\- ]{0,1})?[456789]\\d{9}$",
                    )
                ],
            ),
        ),
    ]
