# Generated by Django 2.2.11 on 2023-02-04 12:03

from django.db import IntegrityError, migrations, models

specializations = [
    "Anesthetists",
    "Cardiac Surgeon",
    "Cardiologist",
    "Dermatologist",
    "Diabetologist",
    "Emergency Medicine Physcian",
    "Endocrinologist",
    "Family Physcian",
    "Gastroenterologist",
    "Genreal Surgeon",
    "Hematologist",
    "Intensivists",
    "Medical Officer",
    "Nephrologist",
    "Neuro Surgeon",
    "Neurologist",
    "obstetrician and Gynecologists",
    "Oncologist",
    "Oncology Surgeon",
    "Opthalmologists",
    "orthopaedic Surgeon",
    "Orthopaedician",
    "Otolaryngologist ( ENT )",
    "Palliative care Physcian",
    "Pathologists",
    "Pediatrician",
    "Physcian",
    "Plastic Surgeon",
    "Psychiatrists",
    "Pulmonologists",
    "Radio technician",
    "Radiologist",
    "Rheumatologist",
    "Thoraco-Vascular Surgeon",
    "Urologist",
]


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0045_auto_20230110_1120"),
    ]

    def remove_all_skills(apps, schema_editor):
        Skill = apps.get_model("users", "Skill")
        Skill.objects.all().delete()

    def add_skills(apps, schema_editor):
        Skill = apps.get_model("users", "Skill")
        for specialization in specializations:
            try:
                skill = Skill(name=specialization)
                skill.save()
            except IntegrityError as e:
                if "unique constraint" in e.args:
                    continue

    operations = [
        migrations.AlterField(
            model_name="skill",
            name="name",
            field=models.CharField(max_length=255, unique=True),
        ),
        migrations.RunPython(remove_all_skills),
        migrations.RunPython(add_skills),
    ]
