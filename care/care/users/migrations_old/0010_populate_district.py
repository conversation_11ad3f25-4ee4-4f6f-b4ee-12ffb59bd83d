# Generated by Django 2.2.11 on 2020-03-25 18:53
from django.db import migrations

from care.users.models import DISTRICT_CHOICES


def create_initial_state_and_districts(apps, *args):
    State = apps.get_model("users", "State")
    District = apps.get_model("users", "District")

    kl = State.objects.create(name="Kerala")
    for choice in DISTRICT_CHOICES:
        d = District.objects.create(id=choice[0], state=kl, name=choice[1])
        d.save()


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0009_auto_20200325_1908"),
    ]

    operations = [
        migrations.RunPython(create_initial_state_and_districts),
    ]
