# Generated by Django 2.2.11 on 2023-06-09 08:41

import uuid

from django.db import migrations


def gen_uuid(apps, schema_editor):
    User = apps.get_model("users", "User")
    for user in User.objects.get_entire_queryset():
        user.external_id = uuid.uuid4()
        user.save(update_fields=["external_id"])


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0047_user_external_id"),
    ]

    operations = [
        migrations.RunPython(gen_uuid, migrations.RunPython.noop),
    ]
