[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[[source]]
url = "https://github.com/ohcnetwork/python-magic-bin/releases/expanded_assets/v0.1"
verify_ssl = true
name = "python-magic-bin"

[packages]
argon2-cffi = "==23.1.0"
authlib = "==1.4.0"
boto3 = "==1.36.12"
celery = "==5.4.0"
django = "==5.1.4"
django-environ = "==0.12.0"
django-cors-headers = "==4.6.0"
django-filter = "==24.3"
django-maintenance-mode = "==0.21.1"
django-queryset-csv = "==1.1.0"
django-ratelimit = "==4.1.0"
django-redis = "==5.4.0"
django-rest-passwordreset = "==1.5.0"
django-simple-history = "==3.7.0"
djangoql = "==0.18.1"
djangorestframework = "==3.15.2"
djangorestframework-simplejwt = "==5.4.0"
dry-rest-permissions = "==0.1.10"
drf-nested-routers = "==0.94.1"
drf-spectacular = "==0.28.0"
gunicorn = "==23.0.0"
healthy-django = "==0.1.0"
json-fingerprint = "==0.14.0"
jsonschema = "==4.23.0"
pillow = "==11.1.0"
psycopg = { extras = ["c"], version = "==3.2.3" }
pydantic = "==2.9.2"
pyjwt = "==2.10.1"
pyotp = "==2.9.0"
python-slugify = "==8.0.4"
pywebpush = "==2.0.1"
redis = { extras = ["hiredis"], version = "==5.2.1" }
redis-om = "==0.3.3"
requests = "==2.32.3"
simplejson = "==3.19.3"
sentry-sdk = "==2.18.0"
whitenoise = "==6.8.2"
django-anymail = {extras = ["amazon-ses"], version = "*"}
pydantic-extra-types = "2.10.2"
phonenumberslite = "==8.13.54"
python-magic = {version = "==0.4.28", index = "python-magic-bin"}
evalidate = "2.0.5"

[dev-packages]
boto3-stubs = { extras = ["s3", "boto3"], version = "*" }
coverage = "==7.6.10"
debugpy = "==1.8.14"
django-coverage-plugin = "==3.1.0"
django-extensions = "==3.2.3"
django-silk = "==5.3.2"
djangorestframework-stubs = "==3.15.2"
factory-boy = "==3.3.3"
freezegun = "==1.5.2"
ipython = "==9.2.0"
mypy = "==1.14.1"
pre-commit = "==4.0.1"
requests-mock = "==1.12.1"
tblib = "==3.0.0"
watchdog = "==6.0.0"
werkzeug = "==3.1.3"
ruff = "==0.11.7"
model-bakery = "==1.20.1"
dirty-equals = "==0.8.0"
polyfactory = "==2.18.1"
Faker = "==33.3.0"

[docs]
furo = "==2024.8.6"
sphinx = "==8.1.3"
myst-parser = "==4.0.0"

[requires]
python_version = "3.13"
