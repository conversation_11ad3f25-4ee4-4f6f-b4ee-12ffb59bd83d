{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "types": ["vite/client", "vite-plugin-pwa/client"], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@core/*": ["src/*"], "@careConfig": ["./care.config.ts"]}, "typeRoots": ["./node_modules/@types", "./src/remote-modules.d.ts"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.d.ts", "care.config.ts", "scripts/**/*.ts", "cypress.config.ts", "plugins/**/*.ts"]}