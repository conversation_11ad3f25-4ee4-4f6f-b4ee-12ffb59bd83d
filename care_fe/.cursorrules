You are an expert in TypeScript, React, Shadcn UI, Tailwind.

Key Principles

- Write concise, technical TypeScript code with accurate examples.
- Use functional and declarative programming patterns; avoid classes.
- Prefer iteration and modularization over code duplication.
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError).

Naming Conventions

- Use lowercase with dashes for directories (e.g., components/auth-wizard).
- <PERSON>av<PERSON> named exports for components.

TypeScript Usage

- Use TypeScript for all code; prefer interfaces over types.
- Avoid enums; use maps instead.
- Use functional components with TypeScript interfaces.

Syntax and Formatting

- Use the "function" keyword for pure functions.
- Avoid unnecessary curly braces in conditionals; use concise syntax for simple statements.
- Use declarative JSX.

UI and Styling

- Use Shadcn UI, Radix, and Tailwind for components and styling.
- Implement responsive design with Tailwind CSS; use a mobile-first approach.

General Guidelines

- Care uses TanStack Query for data fetching from the API along with query and mutate utilities for the queryFn and mutationFn. (Docs @ /Utils/request/README.md)
- APIs are defined in the api.tsx file.
- Use raviger for routing.
- Add and reuse proper translations for the components in the en.json file.

# Testing Guidelines

For Cypress testing guidelines, refer to cypress/docs/*.md
