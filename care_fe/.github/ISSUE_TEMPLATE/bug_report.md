---
name: Bug report
about: Create a report to help us improve
title: ""
labels: ""
assignees: ""
---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:

1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Desktop (please complete the following information):**

- OS: [e.g. iOS]
- Browser [e.g. chrome, safari]
- Version [e.g. 22]

**Smartphone (please complete the following information):**

- Device: [e.g. iPhone6]
- OS: [e.g. iOS8.1]
- <PERSON><PERSON><PERSON> [e.g. stock browser, safari]
- Version [e.g. 22]

**Additional context**
Add any other context about the problem here.

---

### 🚨 DO NOT EDIT BELOW THIS LINE 🚨

### Instructions for Requesting Assignment:

To request assignment, please clearly outline your solution and timeline by commenting on the issue using the format below:

**Describe your solution clearly:**
Provide a detailed explanation of your proposed solution, including your approach, key implementation steps, and relevant examples or references. Mention any dependencies, assumptions, or risks you foresee that might affect your timeline or implementation.

**Expected Timeline:**
- End date: [Expected submission date of a completed Pull Request]

**Additional Context:**
Include any other relevant context, links, screenshots, or resources that support your proposed solution.

> 🚨 Your assignment may be unassigned if there is no activity or progress within the stated timeline unless communicated clearly and agreed upon.
