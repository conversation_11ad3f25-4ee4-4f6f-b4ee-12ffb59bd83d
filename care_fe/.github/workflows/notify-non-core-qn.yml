name: Notify Core Team on Non-Core Questions

on:
  issue_comment:
    types: [created]

permissions:
  issues: write
  pull-requests: write

jobs:
  notify_core_team:
    runs-on: ubuntu-24.04-arm
    env:
      ALLOWED_USERNAMES: ${{ vars.ALLOWED_USERNAMES || '' }}
      QUESTION_KEYWORDS: ${{ vars.QUESTION_KEYWORDS || '' }}
      QUESTION_LABELS: ${{ vars.QUESTION_LABELS || '' }}
      SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK || '' }}

    steps:
      - name: Check and Notify
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const isOrgMember = (commenter, allowedUsers) =>
              allowedUsers.split(',').map(u => u.trim()).includes(commenter);

            const containsQuestionKeywords = (text, keywords) =>
              keywords.split(',').map(k => k.trim()).some(keyword =>
                text.toLowerCase().includes(keyword.toLowerCase())
              );

            const addLabelsToIssue = async (github, context, labelsString) => {
              const labels = labelsString.split(',').map(label => label.trim()).filter(Boolean);
              if (labels.length > 0) {
                await github.rest.issues.addLabels({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: context.payload.issue.number,
                  labels: labels
                });
              }
            };

            const sendSlackNotification = async (webhook, commentUrl) => {
              const payload = { commentUrl };
              const response = await fetch(webhook, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
              });
              if (!response.ok) {
                throw new Error(`Slack notification failed with status: ${response.status}`);
              }
            };

            const isBot = async (github, commenter) => {
              try {
                const { data: user } = await github.rest.users.getByUsername({ username: commenter });
                return user.type === 'Bot';
              } catch {
                return false;
              }
            };

            const commenter = context.payload.comment.user.login;
            const allowedUsers = process.env.ALLOWED_USERNAMES;
            const keywords = process.env.QUESTION_KEYWORDS;
            const labels = process.env.QUESTION_LABELS;
            const webhook = process.env.SLACK_WEBHOOK;

            if (await isBot(github, commenter)) return;

            if (allowedUsers && !isOrgMember(commenter, allowedUsers)) {
              const commentBody = context.payload.comment.body.trim();
              const filteredCommentBody = commentBody.split('\n').filter(line => !line.startsWith('>')).join('\n');

              if (keywords && containsQuestionKeywords(filteredCommentBody, keywords)) {
                if (labels) {
                  await addLabelsToIssue(github, context, labels);
                }
                if (webhook) {
                  const commentUrl = context.payload.comment.html_url;
                  await sendSlackNotification(webhook, commentUrl);
                }
              }
            }
