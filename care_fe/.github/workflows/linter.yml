name: Lint Code Base

on:
  pull_request:
    branches:
      - develop
  merge_group:

jobs:
  lint:
    runs-on: ubuntu-24.04-arm
    permissions:
      contents: read
      packages: read
      statuses: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "22"

      - name: Install dependencies
        run: npm ci

      - name: Run lint
        run: npm run lint -- --quiet

      - name: Run unimported
        run: npm run unimported
