name: Say thanks for the contributors

on:
  pull_request_target:
    types:
      - closed

jobs:
  if_merged:
    if: github.event.pull_request.merged == true
    runs-on: ubuntu-24.04-arm
    permissions:
      issues: write
      pull-requests: write
      contents: write
    steps:
      - name: Add thankyou note
        uses: actions/github-script@v7
        with:
          script: |
            const thankyouNote = 'Your efforts have helped advance digital healthcare and TeleICU systems. :rocket: Thank you for taking the time out to make CARE better. We hope you continue to innovate and contribute; your impact is immense! :raised_hands:';
            const options = {
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
            };

            const { data : { assignees, user } } = await github.rest.issues.get(options);

            const taggedUsers = [...new Set(
              assignees.map(u => "@"+u.login).concat("@"+user.login)  
            )].join(" ")

            await github.rest.issues.createComment({ 
              ...options, 
              body: `${taggedUsers} ${thankyouNote}` 
            });
