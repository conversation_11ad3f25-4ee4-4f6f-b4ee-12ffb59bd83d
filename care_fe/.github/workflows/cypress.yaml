name: Cypress Tests

on:
  pull_request:
    branches:
      - develop
      - production
    paths:
      - "src/**"
      - "cypress/**"
      - "package.json"
      - "package-lock.json"
      - "cypress.config.ts"
      - "plugins/**"
      - "public/**"
      - ".github/workflows/cypress.yaml"
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  install:
    if: github.repository == 'ohcnetwork/care_fe'
    permissions: write-all
    runs-on: ubuntu-latest
    env:
      REACT_CARE_API_URL: http://localhost:9000
    steps:
      - name: Checkout 📥
        uses: actions/checkout@v4

      - name: Cypress install
        uses: cypress-io/github-action@v6
        with:
          runTests: false
          build: npm run build

      - name: Save build folder
        uses: actions/upload-artifact@v4
        with:
          name: build
          if-no-files-found: error
          path: build
          retention-days: 1

  parallel-job:
    if: github.repository == 'ohcnetwork/care_fe'
    permissions: write-all
    timeout-minutes: 15
    runs-on: ubuntu-latest
    needs: install
    strategy:
      fail-fast: false
      matrix:
        containers: [1, 2, 3, 4]
    steps:
      - name: Checkout 📥
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set backend branch
        id: backend-branch
        run: echo "branch=develop" >> $GITHUB_OUTPUT

      - name: Checkout care 📥
        uses: actions/checkout@v4
        with:
          repository: ohcnetwork/care
          path: care
          ref: ${{ steps.backend-branch.outputs.branch }}
          fetch-depth: 0

      - name: Start care docker containers 🐳
        run: |
          cd care
          echo DISABLE_RATELIMIT=True >> docker/.local.env
          echo JWKS_BASE64=\"$(cat ../.github/runner-files/jwks.b64.txt)\" >> docker/.local.env
          echo MAX_QUESTIONNAIRE_TEXT_RESPONSE_SIZE=500 >> docker/.local.env
          make docker_config_file=docker-compose.local.yaml up load-fixtures
          cd ..
        env:
          JWKS_BASE64: ${{ secrets.JWKS_BASE64 }}

      - name: Determine PR Origin
        id: pr_origin
        run: |
          echo "is_forked=$(echo ${{ github.event.pull_request.head.repo.fork }})" >> $GITHUB_OUTPUT

      - name: Download the build folders
        uses: actions/download-artifact@v4
        with:
          name: build
          path: build
          merge-multiple: true

      # For non-forked PRs
      - name: "UI Tests - Chrome (Non-forked)"
        if: steps.pr_origin.outputs.is_forked == 'false'
        uses: cypress-io/github-action@v6
        with:
          start: "npx vite preview --host"
          wait-on: "http://localhost:4000"
          wait-on-timeout: 300
          browser: chrome
          record: true
          parallel: true
          group: "UI - Chrome"
          config-file: cypress.config.ts
          install-command: npm ci
        env:
          CYPRESS_PROJECT_ID: ${{ secrets.CYPRESS_PROJECT_ID }}
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_OPTIONS: --max_old_space_size=1536
          COMMIT_INFO_MESSAGE: ${{github.event.pull_request.title}}
          COMMIT_INFO_SHA: ${{github.event.pull_request.head.sha}}
          CYPRESS_CHROME_ARGS: "--no-sandbox --disable-dev-shm-usage"
          CYPRESS_BROWSER_VERSION: "136"

      # For forked PRs
      - name: "UI Tests - Chrome (Forked)"
        if: steps.pr_origin.outputs.is_forked == 'true'
        uses: cypress-io/github-action@v6
        with:
          env: SKIP_PREFLIGHT_CHECK=true
          install: true
          start: "npx vite preview --host"
          wait-on: "http://localhost:4000"
          wait-on-timeout: 300
          browser: chrome
          record: false
          parallel: false
          install-command: npm ci
        env:
          CYPRESS_SPLIT_TESTS: "true"
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_OPTIONS: --max_old_space_size=1536
          COMMIT_INFO_MESSAGE: ${{github.event.pull_request.title}}
          COMMIT_INFO_SHA: ${{github.event.pull_request.head.sha}}
          SPLIT: ${{ strategy.job-total }}
          SPLIT_INDEX: ${{ strategy.job-index }}
          DEBUG: "cypress-split,find-cypress-specs"
          CYPRESS_CHROME_ARGS: "--no-sandbox --disable-dev-shm-usage"
          CYPRESS_BROWSER_VERSION: "136"

      - name: Upload cypress screenshots on failure 📸
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-screenshots-${{ github.run_id }}-${{ github.run_attempt }}-${{ matrix.containers }}
          path: cypress/screenshots
          compression-level: 9

      - name: Upload cypress videos 📹
        uses: actions/upload-artifact@v4
        if: ${{ failure() && steps.pr_origin.outputs.is_forked == 'true' }}
        with:
          name: cypress-videos-${{ github.run_id }}-${{ github.run_attempt }}-${{ matrix.containers }}
          path: cypress/videos
          compression-level: 9

  cleanup:
    if: always() && github.repository == 'ohcnetwork/care_fe'
    needs: parallel-job
    runs-on: ubuntu-latest
    steps:
      - name: Delete build artifact
        uses: geekyeggo/delete-artifact@v5
        with:
          name: build
