import { useQuery } from "@tanstack/react-query";
import { navigate } from "raviger";
import { createContext, useEffect, useState } from "react";

import { useAuthContext } from "@/hooks/useAuthUser";

import routes from "@/Utils/request/api";
import query from "@/Utils/request/query";
import { TokenData } from "@/types/auth/otp";
import { PatientRead } from "@/types/emr/patient/patient";

export type PatientUserContextType = {
  patients?: PatientRead[];
  selectedPatient: PatientRead | null;
  setSelectedPatient: (patient: PatientRead) => void;
  tokenData: TokenData;
};

export const PatientUserContext = createContext<PatientUserContextType | null>(
  null,
);

interface Props {
  children: React.ReactNode;
}

export default function PatientUserProvider({ children }: Props) {
  const [patients, setPatients] = useState<PatientRead[]>([]);
  const [selectedPatient, setSelectedPatient] = useState<PatientRead | null>(
    null,
  );

  const { patientToken: tokenData } = useAuthContext();

  const { data: userData } = useQuery({
    queryKey: ["patients", tokenData],
    queryFn: query(routes.otp.getPatient, {
      headers: {
        Authorization: `Bearer ${tokenData?.token}`,
      },
    }),
    enabled: !!tokenData?.token,
  });

  useEffect(() => {
    if (userData?.results && userData.results.length > 0) {
      setPatients(userData.results);
      setSelectedPatient(userData.results[0]);
    }
  }, [userData]);

  if (!tokenData) {
    navigate("/");
    return null;
  }

  return (
    <PatientUserContext.Provider
      value={{
        patients,
        selectedPatient,
        setSelectedPatient,
        tokenData: tokenData,
      }}
    >
      {children}
    </PatientUserContext.Provider>
  );
}
