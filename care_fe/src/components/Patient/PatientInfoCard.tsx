import { useQueryClient } from "@tanstack/react-query";
import {
  BedSingle,
  Building,
  ChevronDown,
  CircleCheck,
  CircleDashed,
  Clock,
  Droplet,
  UserRound,
} from "lucide-react";
import { Link, usePathParams } from "raviger";
import { useTranslation } from "react-i18next";

import { cn } from "@/lib/utils";

import CareIcon from "@/CAREUI/icons/CareIcon";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

import { CareTeamSheet } from "@/components/CareTeam/CareTeamSheet";
import { Avatar } from "@/components/Common/Avatar";
import EncounterActions from "@/components/Encounter/EncounterActions";
import { LocationSheet } from "@/components/Location/LocationSheet";
import { LocationTree } from "@/components/Location/LocationTree";
import { AccountSheetButton } from "@/components/Patient/AccountSheet";
import LinkDepartmentsSheet from "@/components/Patient/LinkDepartmentsSheet";
import TagAssignmentSheet from "@/components/Tags/TagAssignmentSheet";

import { PLUGIN_Component } from "@/PluginEngine";
import dayjs from "@/Utils/dayjs";
import { formatDateTime, formatPatientAge } from "@/Utils/utils";
import {
  EncounterRead,
  completedEncounterStatus,
  inactiveEncounterStatus,
} from "@/types/emr/encounter/encounter";
import { PatientRead } from "@/types/emr/patient/patient";
import { FacilityOrganization } from "@/types/facilityOrganization/facilityOrganization";

export interface PatientInfoCardProps {
  patient: PatientRead;
  encounter: EncounterRead;
  fetchPatientData?: (state: { aborted: boolean }) => void;
  canWrite: boolean;
  disableButtons?: boolean;
}

export default function PatientInfoCard(props: PatientInfoCardProps) {
  const { patient, encounter, canWrite, disableButtons = false } = props;
  const subpathMatch = usePathParams("/facility/:facilityId/*");
  const facilityIdExists = !!subpathMatch?.facilityId;
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const handleTagsUpdate = () => {
    // Refresh the patient data to get updated tags
    queryClient.invalidateQueries({ queryKey: ["encounter", encounter.id] });
  };

  return (
    <>
      <section className="flex flex-col lg:flex-row">
        <div
          className="flex w-full flex-col bg-white px-4 pt-4 lg:flex-row"
          id="patient-infobadges"
        >
          <div className="flex justify-items-start gap-2 lg:justify-normal">
            <div className="flex flex-col items-start lg:items-center">
              <div className="w-16 min-w-16 bg-secondary-200 h-16 md:size-24 rounded">
                <Avatar name={patient.name} className="w-full h-full" />
              </div>
            </div>
            <div className="flex justify-center">
              <div
                className="mb-2 flex flex-col text-base md:text-xl font-semibold capitalize lg:hidden"
                id="patient-name-consultation"
              >
                <Link
                  href={`/facility/${encounter.facility.id}/patient/${encounter.patient.id}`}
                  className="text-gray-950 font-semibold flex items-start leading-tight"
                  id="patient-details"
                  data-cy="patient-details-button"
                >
                  {patient.name}
                  <CareIcon
                    icon="l-external-link-alt"
                    className="size-3 opacity-50 mt-1"
                  />
                </Link>
                <div className="my-[2px] text-sm font-semibold text-secondary-600">
                  {formatPatientAge(patient, true)} •{" "}
                  {t(`GENDER__${patient.gender}`)}
                </div>
                {patient.deceased_datetime && (
                  <Badge
                    variant="destructive"
                    className="border-2 border-red-700 bg-red-100 text-red-800 hover:bg-red-200 hover:text-red-900"
                  >
                    <h3 className="text-xs font-normal sm:text-sm sm:font-medium">
                      {t("time_of_death")}
                      {": "}
                      {dayjs(patient.deceased_datetime).format(
                        "DD MMM YYYY, hh:mm A",
                      )}
                    </h3>
                  </Badge>
                )}
              </div>
            </div>
          </div>
          <div className="flex w-full flex-col items-center gap-4 space-y-2 lg:items-start lg:gap-0 lg:pl-2">
            <div className="w-full flex flex-col flex-wrap justify-center lg:items-start lg:justify-normal">
              <div
                className="hidden flex-row text-xl font-semibold capitalize lg:flex"
                id="patient-name-consultation"
              >
                <Link
                  href={`/facility/${encounter.facility.id}/patient/${encounter.patient.id}`}
                  className="font-semibold flex items-center gap-1"
                  id="patient-details"
                  data-cy="patient-details-button"
                >
                  {patient.name}
                  <CareIcon
                    icon="l-external-link-alt"
                    className="size-4 opacity-70"
                  />
                </Link>
                <div className="ml-3 mr-2 mt-[6px] text-sm font-semibold text-secondary-600">
                  {formatPatientAge(patient, true)} •{" "}
                  {t(`GENDER__${patient.gender}`)}
                </div>
                {patient.deceased_datetime && (
                  <Badge
                    variant="destructive"
                    className="border-2 border-red-700 bg-red-100 text-red-800 hover:bg-red-200 hover:text-red-900"
                  >
                    <h3 className="text-sm font-medium">
                      {t("time_of_death")}
                      {": "}
                      {dayjs(patient.deceased_datetime).format(
                        "DD MMM YYYY, hh:mm A",
                      )}
                    </h3>
                  </Badge>
                )}
              </div>
              <div className="grid gap-4 grid-cols-3 mt-2 md:mt-0">
                <div className="flex flex-col space-y-1">
                  <span className="text-xs text-gray-500 font-medium">
                    {t("start_date")}
                  </span>
                  <span className="text-xs">
                    {props.encounter.period.start
                      ? formatDateTime(props.encounter.period.start)
                      : t("not_started")}
                  </span>
                </div>
                <div className="flex flex-col space-y-1">
                  <span className="text-xs text-gray-500 font-medium">
                    {t("end_date")}
                  </span>
                  <span className="text-xs">
                    {props.encounter.period.end
                      ? formatDateTime(props.encounter.period.end)
                      : t("ongoing")}
                  </span>
                </div>
                {props.encounter.external_identifier && (
                  <div className="flex flex-col space-y-1 col-span-1">
                    <span className="text-xs text-gray-500 font-medium">
                      {t("hospital_identifier")}
                    </span>
                    <span className="text-xs">
                      {props.encounter.external_identifier}
                    </span>
                  </div>
                )}
              </div>

              <div className="mt-3 w-full flex flex-col gap-3 sm:flex-row">
                <div
                  className="flex w-full flex-wrap items-center justify-start gap-2 text-sm text-secondary-900 sm:flex-row sm:text-sm md:pr-10 lg:justify-normal"
                  id="patient-consultationbadges"
                >
                  <Popover>
                    <PopoverTrigger asChild>
                      <Badge
                        className="capitalize gap-1 py-1 px-3 cursor-pointer hover:bg-secondary-100"
                        variant="outline"
                        title={`Encounter Status: ${t(`encounter_status__${props.encounter.status}`)}`}
                      >
                        {completedEncounterStatus.includes(
                          props.encounter.status,
                        ) || props.encounter.status === "discharged" ? (
                          <CircleCheck
                            className="size-4 text-green-300"
                            fill="green"
                          />
                        ) : (
                          <CircleDashed className="size-4 text-yellow-500" />
                        )}
                        {t(`encounter_status__${props.encounter.status}`)}
                        <ChevronDown className="size-3 opacity-50" />
                      </Badge>
                    </PopoverTrigger>
                    <PopoverContent align={"start"} className="w-auto p-2">
                      <div className="space-y-2">
                        <h4 className="font-medium text-sm">
                          {t("status_history")}
                        </h4>
                        {encounter.status_history.history.map(
                          (history, index) => (
                            <div
                              key={index}
                              className="flex items-center gap-2 text-sm"
                            >
                              <span className="text-gray-500">
                                {formatDateTime(history.moved_at)}
                              </span>
                              <span className="font-medium">
                                {t(`encounter_status__${history.status}`)}
                              </span>
                            </div>
                          ),
                        )}
                      </div>
                    </PopoverContent>
                  </Popover>

                  <Popover>
                    <PopoverTrigger asChild>
                      <Badge
                        className="capitalize gap-1 py-1 cursor-pointer hover:bg-secondary-100"
                        variant="outline"
                        title={`Encounter Class: ${props.encounter.encounter_class}`}
                      >
                        <BedSingle
                          className="size-4 text-blue-400"
                          fill="#93C5FD"
                        />
                        {t(
                          `encounter_class__${props.encounter.encounter_class}`,
                        )}
                        <ChevronDown className="size-3 opacity-50" />
                      </Badge>
                    </PopoverTrigger>
                    <PopoverContent align={"end"} className="w-auto p-2">
                      <div className="space-y-2">
                        <h4 className="font-medium text-sm">
                          {t(`class_history`)}
                        </h4>
                        {encounter.encounter_class_history.history.map(
                          (history, index) => (
                            <div
                              key={index}
                              className="flex items-center gap-2 text-sm"
                            >
                              <span className="text-gray-500">
                                {formatDateTime(history.moved_at)}
                              </span>
                              <span className="font-medium">
                                {t(`encounter_class__${history.status}`)}
                              </span>
                            </div>
                          ),
                        )}
                      </div>
                    </PopoverContent>
                  </Popover>
                  <Badge
                    className="capitalize gap-1 py-1 px-3"
                    variant="outline"
                    title={`Priority: ${t(
                      `encounter_priority__${props.encounter.priority.toLowerCase()}`,
                    )}`}
                  >
                    <Clock className="size-4 text-yellow-500" />
                    {t(
                      `encounter_priority__${props.encounter.priority.toLowerCase()}`,
                    )}
                  </Badge>

                  {patient.blood_group && (
                    <Badge
                      className="capitalize gap-1 py-1 px-3"
                      variant="outline"
                      title={`Blood Group: ${patient.blood_group?.replace("_", " ")}`}
                    >
                      <Droplet className="size-4 text-red-300" fill="red" />
                      {patient.blood_group?.replace("_", " ")}
                    </Badge>
                  )}

                  {
                    // (encounter.status === "discharged" ||
                    //   encounter.status === "completed") &&
                    encounter.hospitalization?.discharge_disposition && (
                      <Badge
                        title={t("discharge_disposition")}
                        variant="outline"
                        className="gap-1"
                      >
                        <CareIcon
                          icon="l-signout"
                          className="size-4 text-blue-400"
                        />
                        {t(
                          `encounter_discharge_disposition__${encounter.hospitalization.discharge_disposition}`,
                        )}
                      </Badge>
                    )
                  }

                  {canWrite ? (
                    <LinkDepartmentsSheet
                      entityType="encounter"
                      entityId={encounter.id}
                      currentOrganizations={encounter.organizations}
                      facilityId={encounter.facility.id}
                      trigger={
                        <div
                          className="flex flex-wrap gap-2"
                          data-cy="add-organization-badge"
                        >
                          {encounter.organizations.map((org) =>
                            organizationBadge(org),
                          )}
                          {encounter.organizations.length === 0 && (
                            <Badge
                              className="capitalize gap-1 py-1 px-3 cursor-pointer hover:bg-secondary-100"
                              variant="outline"
                            >
                              <Building className="size-4 text-blue-400" />
                              {t("add_organization_other")}
                            </Badge>
                          )}
                        </div>
                      }
                    />
                  ) : (
                    encounter.organizations.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {encounter.organizations.map((org) =>
                          organizationBadge(org),
                        )}
                      </div>
                    )
                  )}
                  {props.encounter.current_location ? (
                    <Popover>
                      <PopoverTrigger asChild>
                        <Badge
                          className="capitalize gap-1 py-1 px-3 cursor-pointer hover:bg-secondary-100"
                          variant="outline"
                          title={`Current Location: ${props.encounter.current_location.name}`}
                          data-cy="current-location-badge"
                        >
                          <CareIcon
                            icon="l-location-point"
                            className="size-4 text-green-600"
                          />
                          {props.encounter.current_location.name}
                          <ChevronDown className="size-3 opacity-50" />
                        </Badge>
                      </PopoverTrigger>
                      <PopoverContent align={"start"} className="w-auto p-2">
                        <div className="space-y-2 p-2 items-center">
                          <div className="flex items-center gap-8 justify-between">
                            <h4 className="font-medium text-sm">
                              {t("location")}
                            </h4>

                            <LocationSheet
                              facilityId={props.encounter.facility.id}
                              history={encounter.location_history}
                              encounter={encounter}
                              trigger={
                                <div>
                                  <CareIcon
                                    icon="l-history"
                                    className="size-4 text-gray-700"
                                  />
                                  <Button
                                    variant="link"
                                    className="text-gray-950 underline pl-1 pr-0  font-semibold"
                                  >
                                    {t("history")}
                                  </Button>
                                </div>
                              }
                            />
                          </div>
                          <div className="border-b border-gray-200 my-2" />
                          <LocationTree
                            location={props.encounter.current_location}
                          />
                          <div className="border-b border-dashed border-gray-200 my-2" />
                          {canWrite && (
                            <LocationSheet
                              facilityId={props.encounter.facility.id}
                              encounter={encounter}
                              trigger={
                                <Button
                                  variant="outline"
                                  className="border-gray-400 w-full"
                                  data-cy="update-encounter-location-button"
                                >
                                  {t("update_location")}
                                </Button>
                              }
                              history={encounter.location_history}
                            />
                          )}
                        </div>
                      </PopoverContent>
                    </Popover>
                  ) : canWrite ? (
                    <Badge variant="outline" className="py-0.5 px-3">
                      <LocationSheet
                        facilityId={props.encounter.facility.id}
                        encounter={encounter}
                        trigger={
                          <div
                            className="flex items-center gap-1 text-gray-950 py-0.5 cursor-pointer hover:bg-secondary-100"
                            data-cy="add-encounter-location"
                          >
                            <CareIcon
                              icon="l-location-point"
                              className="size-4 text-green-600"
                            />
                            {t("add_location")}
                          </div>
                        }
                        history={encounter.location_history}
                      />
                    </Badge>
                  ) : (
                    <></>
                  )}
                  <Badge variant="outline" className="py-0.5 px-3">
                    <CareTeamSheet
                      encounter={encounter}
                      trigger={
                        <div className="flex items-center gap-1 text-gray-950 py-0.5 cursor-pointer hover:bg-secondary-100">
                          <UserRound className="size-4 text-green-600" />
                          {canWrite
                            ? t("manage_care_team")
                            : t("view_care_team")}
                        </div>
                      }
                      canWrite={canWrite}
                    />
                  </Badge>
                  <Badge variant="outline">
                    <TagAssignmentSheet
                      entityType="encounter"
                      entityId={encounter.id}
                      currentTags={encounter.tags || []}
                      onUpdate={handleTagsUpdate}
                      trigger={
                        <div className="flex items-center gap-1 text-gray-950 py-0.5 cursor-pointer hover:bg-secondary-100 capitalize">
                          <CareIcon
                            icon="l-tag-alt"
                            className="size-4 text-green-600"
                          />
                          {t("tags")}
                        </div>
                      }
                      canWrite={canWrite}
                    />
                  </Badge>
                  <Badge variant="outline">
                    <AccountSheetButton
                      encounter={encounter}
                      trigger={
                        <div className="flex items-center gap-1 text-gray-950 py-0.5 cursor-pointer hover:bg-secondary-100 capitalize">
                          <CareIcon
                            icon="l-wallet"
                            className="size-4 text-green-600"
                          />
                          {t("account")}
                        </div>
                      }
                      canWrite={canWrite}
                    />
                  </Badge>
                </div>
                <div
                  className="flex flex-col items-center justify-end gap-4 px-4 py-1 2xl:flex-row"
                  id="consultation-buttons"
                >
                  <PLUGIN_Component
                    __name="PatientInfoCardQuickActions"
                    encounter={encounter}
                    className="w-full lg:w-auto bg-primary-700 text-white hover:bg-primary-600"
                  />
                  {!disableButtons && (
                    <div
                      className="flex w-full flex-col gap-3 sm:w-auto"
                      data-cy="update-encounter-button"
                    >
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="primary">
                            {inactiveEncounterStatus.includes(
                              encounter.status,
                            ) || !facilityIdExists
                              ? t("actions")
                              : t("update")}
                            <ChevronDown className="ml-2 size-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                          align="end"
                          className="w-(--radix-dropdown-menu-trigger-width) sm:w-auto"
                        >
                          <EncounterActions
                            encounter={encounter}
                            layout="dropdown"
                          />
                          <PLUGIN_Component
                            __name="PatientInfoCardActions"
                            encounter={encounter}
                          />
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );

  function organizationBadge(org: FacilityOrganization) {
    return (
      <Badge
        key={org.id}
        className={cn(
          "capitalize gap-1 py-1 px-3 hover:bg-secondary-100 cursor-pointer",
        )}
        variant="outline"
        title={`Organization: ${org.name}${org.description ? ` - ${org.description}` : ""}`}
      >
        <Building className="size-4 text-blue-400" />
        {org.name}
      </Badge>
    );
  }
}
