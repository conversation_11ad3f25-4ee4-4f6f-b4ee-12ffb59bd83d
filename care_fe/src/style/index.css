@import "@fontsource/figtree/300.css" layer(base);
@import "@fontsource/figtree/400.css" layer(base);
@import "@fontsource/figtree/500.css" layer(base);
@import "@fontsource/figtree/600.css" layer(base);
@import "@fontsource/figtree/700.css" layer(base);
@import "@fontsource/figtree/800.css" layer(base);
@import "@fontsource/figtree/900.css" layer(base);

@import "tailwindcss";

@import "tw-animate-css";

@config '../../tailwind.config.js';

/* Custom animations for the SessionExpired component */
@keyframes ping-slow {
  0% {
    transform: scale(0.8);
    opacity: 0.3;
  }
  50% {
    transform: scale(1);
    opacity: 0.8;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.3;
  }
}

@keyframes ping-medium {
  0% {
    transform: scale(0.75);
    opacity: 0.3;
  }
  50% {
    transform: scale(1);
    opacity: 0.7;
  }
  100% {
    transform: scale(0.75);
    opacity: 0.3;
  }
}

@keyframes ping-fast {
  0% {
    transform: scale(0.7);
    opacity: 0.3;
  }
  50% {
    transform: scale(1);
    opacity: 0.6;
  }
  100% {
    transform: scale(0.7);
    opacity: 0.3;
  }
}
@keyframes fade {
  0%,
  100% {
    opacity: 0.2;
  }
  50% {
    opacity: 1;
  }
}

.animate-ping-slow {
  animation: ping-slow 8s ease-in-out infinite;
}

.animate-ping-medium {
  animation: ping-medium 8s ease-in-out infinite;
}

.animate-ping-fast {
  animation: ping-fast 8s ease-in-out infinite;
}
.animate-fade {
  animation: fade 4s ease-in-out infinite;
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }

  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }
}

@layer utilities {
  html {
    @apply size-full scroll-smooth;
  }

  body {
    font-family: "Figtree", sans-serif;
    color: #453c52;
    @apply text-secondary-900 size-full font-sans antialiased;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-bold;
  }

  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  h3 {
    font-size: 1.5rem;
  }

  h4 {
    font-size: 1.25rem;
  }

  h5 {
    font-size: 1rem;
  }

  h6 {
    font-size: 0.875rem;
  }

  @keyframes wave {
    50% {
      transform: scale(0.9);
    }
  }

  @media print {
    @page {
      margin-top: 0;
    }
    body * {
      visibility: hidden;
    }
    #section-to-print,
    #section-to-print * {
      visibility: visible;
      -webkit-print-color-adjust: exact;
    }
    #section-to-print {
      position: absolute;
      left: 0;
      top: 0;
    }
  }

  .App {
    text-align: center;
  }

  .App-logo {
    height: 10vmin;
    pointer-events: none;
    filter: brightness(80%);
  }

  @media (prefers-reduced-motion: no-preference) {
    .App-logo {
      animation: App-logo-blink 1s linear infinite;
    }
  }

  .App-header {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: calc(10px + 2vmin);
    color: white;
  }

  @keyframes App-logo-blink {
    0% {
      opacity: 0;
    }
    25% {
      opacity: 0.5;
    }
    50% {
      opacity: 1;
    }
    75% {
      opacity: 0.5;
    }
    100% {
      opacity: 0;
    }
  }

  .dropdown:hover .dropdown-menu {
    display: block;
  }

  /* for gmaps search dropdown */
  .pac-container {
    z-index: 100000 !important;
  }

  .login-hero {
    background:
      linear-gradient(
        to bottom right,
        rgba(4, 78, 55, 0.466),
        rgba(2, 54, 40, 0.521)
      ),
      url("/images/wave_scene_square.png");
    background-size: cover;
    background-position: center;
    animation: alternate-reverse 30s linear infinite herobg;
  }

  .login-hero::before {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0px;
    width: 200px;
    z-index: 2;
    background: url("/images/wave_long_2.png");
    background-position: top right;
    transition: 0.2s;
    animation: 120s linear infinite wave alternate-reverse;
    -webkit-filter: drop-shadow(-30px 10px 10px rgba(34, 34, 34, 0.101));
  }

  .login-hero::after {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    right: 10px;
    width: 200px;
    z-index: 1;
    background: url("/images/wave_long.png");
    opacity: 0.5;
    background-position: bottom right;
    animation: 120s linear infinite wave_2 alternate-reverse;
    -webkit-filter: drop-shadow(-30px 10px 10px rgba(34, 34, 34, 0.116));
  }

  @media screen and (max-width: 768px) {
    .login-hero::before {
      display: none;
    }
    .login-hero::after {
      display: none;
    }
  }

  @keyframes wave {
    0% {
      background-position: top right;
    }
    100% {
      background-position: bottom right;
    }
  }

  @keyframes wave_2 {
    0% {
      background-position: bottom right;
    }
    100% {
      background-position: top right;
    }
  }

  @keyframes herobg {
    0% {
      background-size: 110%;
    }
    100% {
      background-size: 200%;
    }
  }

  @media screen and (max-width: 1078px) {
    @keyframes herobg {
      0% {
        background-size: 140%;
      }
      100% {
        background-size: 200%;
      }
    }
  }

  /* clears the ‘X’ from Internet Explorer for input:search */
  input[type="search"]::-ms-clear {
    display: none;
    width: 0;
    height: 0;
  }
  input[type="search"]::-ms-reveal {
    display: none;
    width: 0;
    height: 0;
  }

  /* clears the ‘X’ from Chrome for input:search */
  input[type="search"]::-webkit-search-decoration,
  input[type="search"]::-webkit-search-cancel-button,
  input[type="search"]::-webkit-search-results-button,
  input[type="search"]::-webkit-search-results-decoration {
    display: none;
  }

  /* Style for required form labels */
  [data-slot="form-label"][for][aria-required="true"]::after {
    @apply -ml-1 text-red-500 content-['*'];
  }

  /* Style for tabs */
  [data-slot="tabs-trigger"] {
    @apply not-data-[state=active]:text-gray-500;
  }

  /* Styling for input otp */
  [data-slot="input-otp-slot"] {
    @apply data-[active=true]:ring-primary-500 data-[active=true]:border-primary-500 data-[active=true]:ring-1 not-first:data-[active=true]:border-x;
  }

  /* Styling for radio group */
  button[role="radio"] {
    @apply data-[state=checked]:border-primary-500 data-[state=checked]:text-primary-500 border-gray-300 shadow-sm;
  }

  /* Styling for autocomplete */
  [data-slot="popover-trigger"] {
    @apply px-3;
  }

  /* Styling fix for Select for backwards compatibility */
  button[role="combobox"] {
    @apply w-full;
  }
}

@layer base {
  :root {
    --radius: 0.5rem;
    --sidebar-background: 220 14.3% 95.9%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: transparent;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}
