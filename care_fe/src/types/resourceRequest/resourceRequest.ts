import { FacilityModel } from "@/components/Facility/models";
import { UserBareMinimum } from "@/components/Users/<USER>";

import { PatientRead } from "@/types/emr/patient/patient";
import { UserBase } from "@/types/user/user";

export interface ResourceRequest {
  approving_facility: FacilityModel | null;
  assigned_facility: FacilityModel | undefined;
  category: string;
  emergency: boolean;
  id: string;
  origin_facility: FacilityModel;
  priority: number;
  reason: string;
  referring_facility_contact_name: string;
  referring_facility_contact_number: string;
  requested_quantity: number;
  status: string;
  title: string;
  assigned_to: UserBase | null;
  created_by: UserBase;
  updated_by: UserBase;
  created_date: string;
  modified_date: string;
  related_patient: PatientRead | null;
}

export const RESOURCE_REQUEST_STATUS_COLORS = {
  pending: "yellow",
  approved: "green",
  rejected: "destructive",
  cancelled: "secondary",
  transportation_to_be_arranged: "secondary",
  transfer_in_progress: "secondary",
  completed: "secondary",
} as const;

export interface CreateResourceRequest {
  title: string;
  status: string;
  reason: string;
  referring_facility_contact_name: string;
  referring_facility_contact_number: string;
  approving_facility: string | null;
  assigned_to: string | null;
  assigned_facility: string | null;
  origin_facility: string;
  related_patient: string;
  emergency: boolean;
  priority: number;
  category: string;
}

export interface UpdateResourceRequest {
  id: string;
  title: string;
  reason: string;
  assigned_to: string | null;
  status: string;
  referring_facility_contact_name: string;
  referring_facility_contact_number: string;
  approving_facility: string | null;
  assigned_facility: string | null;
  origin_facility: string;
  related_patient: string;
  emergency: boolean;
  priority: number;
  category: string;
}

export interface CommentModel {
  id: string;
  created_by: UserBareMinimum;
  created_date: string;
  comment: string;
}
