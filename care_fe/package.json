{"name": "care_fe", "version": "3.0.5", "description": "Care is a Digital Public Good enabling TeleICU & Decentralised Administration of Healthcare Capacity across States.", "private": true, "repository": {"type": "git", "url": "**************:ohcnetwork/care_fe.git"}, "bugs": {"url": "https://github.com/ohcnetwork/care_fe/issues", "email": "<EMAIL>"}, "contributors": ["Open Healthcare Network Contributors"], "homepage": "https://care.ohc.network", "keywords": ["ohcnetwork", "care", "react", "tailwind", "pwa", "typescript", "vite"], "author": "Open Healthcare Network Contributors", "license": "MIT", "scripts": {"dev": "test -f src/pluginMap.ts || npm run setup && npm run supported-browsers && vite", "local": "npm run supported-browsers && vite --mode docker", "preview": "cross-env NODE_ENV=production vite preview", "preview:scan": "npm run preview & npx react-scan localhost:4000", "build:meta": "node ./scripts/generate-build-version.js", "build:react": "cross-env NODE_ENV=production vite build", "supported-browsers": "node ./scripts/generate-supported-browsers.mjs", "build": "npm run setup && npm run build:meta && npm run supported-browsers && npm run build:react", "setup": "tsx scripts/setup-care-apps.ts", "postinstall": "tsx scripts/install-platform-deps.ts && tsx scripts/generate-headers.ts", "test": "snyk test", "cypress:open": "cross-env NODE_ENV=development cypress open", "cypress:run": "cross-env NODE_ENV=development cypress run", "cypress:run:gui": "cross-env NODE_ENV=development cypress run --headed", "cypress:install": "cross-env NODE_ENV=development cypress install", "prepare": "husky", "lint": "eslint ./src", "unimported": "unimported", "lint-fix": "eslint ./src --fix", "format": "prettier ./src ./cypress --write", "sort-locales": "node ./scripts/sort-locales.js"}, "dependencies": {"@excalidraw/excalidraw": "^0.18.0-a18b139", "@fontsource/figtree": "^5.1.1", "@fontsource/libre-barcode-128-text": "^5.2.5", "@hookform/resolvers": "^5.0.1", "@originjs/vite-plugin-federation": "^1.3.7", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-tooltip": "^1.2.0", "@sentry/browser": "^9.0.0", "@tailwindcss/vite": "^4.0.17", "@tanstack/query-async-storage-persister": "^5.83.0", "@tanstack/react-query": "^5.64.1", "@tanstack/react-query-devtools": "^5.64.2", "@tanstack/react-query-persist-client": "^5.83.0", "@vitejs/plugin-react": "^4.3.4", "bowser": "^2.11.0", "browser-image-compression": "^2.0.2", "browserslist": "^4.24.4", "browserslist-useragent-regexp": "^4.1.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cross-env": "^7.0.3", "date-fns": "^4.0.0", "dayjs": "^1.11.13", "framer-motion": "^12.6.3", "html-to-image": "^1.11.11", "i18next": "^25.0.0", "i18next-browser-languagedetector": "^8.0.2", "i18next-http-backend": "^3.0.1", "i18next-resources-to-backend": "^1.2.1", "input-otp": "^1.4.2", "jotai": "^2.12.3", "jspdf": "^3.0.1", "libphonenumber-js": "^1.11.18", "lucide-react": "^0.503.0", "markdown-it": "^14.1.0", "next-themes": "^0.4.3", "normalize-wheel": "^1.0.1", "pigeon-maps": "^0.22.1", "qrcode.react": "^4.1.0", "raviger": "^5.0.0-2", "react": "19.1.0", "react-day-picker": "^9.6.3", "react-dom": "19.1.0", "react-easy-crop": "^5.4.2", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.55.0", "react-i18next": "^15.2.0", "react-intersection-observer": "^9.15.1", "react-pdf": "^9.2.1", "react-phone-number-input": "^3.4.12", "react-qr-barcode-scanner": "^2.1.2", "react-resizable-panels": "^3.0.2", "react-webcam": "^7.2.0", "react-zoom-pan-pinch": "^3.7.0", "reactflow": "^11.11.4", "recharts": "^2.15.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "use-keyboard-shortcut": "^1.1.6", "zod": "^3.23.8"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@faker-js/faker": "^9.8.0", "@julr/vite-plugin-validate-env": "^1.1.1", "@react-scan/vite-plugin-react-scan": "^0.1.3", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@trivago/prettier-plugin-sort-imports": "^5.0.0", "@types/events": "^3.0.3", "@types/google.maps": "^3.58.1", "@types/jsdom": "^21.1.7", "@types/markdown-it": "^14.1.2", "@types/node": "^22.10.10", "@types/react": "^19.1.0", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-csv": "^1.1.10", "@types/react-dom": "^19.1.0", "@types/react-google-recaptcha": "^2.1.9", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.21.0", "@typescript-eslint/parser": "^8.21.0", "cypress": "^14.2.1", "cypress-localstorage-commands": "^2.2.6", "cypress-split": "^1.24.5", "dompurify": "^3.2.5", "dotenv": "^16.4.5", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-i18next": "^6.1.0", "eslint-plugin-i18next-no-undefined-translation-keys": "^3.4.0", "eslint-plugin-no-relative-import-paths": "^1.6.1", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "glob": "^11.0.0", "globals": "^16.0.0", "husky": "^9.1.7", "jsdom": "^26.0.0", "lint-staged": "^15.2.10", "local-cypress": "^1.2.6", "marked": "^15.0.0", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "snyk": "^1.1294.0", "tailwindcss": "^4.1.3", "ts-node": "^10.9.2", "tsx": "^4.19.2", "tw-animate-css": "^1.2.4", "typescript": "^5.6.3", "unimported": "^1.31.0", "uuid": "^11.0.2", "vite": "^6.0.0", "vite-plugin-checker": "^0.9.0", "vite-plugin-pwa": "^1.0.0", "vite-plugin-static-copy": "^2.0.0", "zod": "^3.24.2"}, "optionalDependencies": {"@esbuild/linux-arm64": "latest", "@esbuild/linux-x64": "latest", "@rollup/rollup-linux-arm64-gnu": "4.45.1", "@rollup/rollup-linux-x64-gnu": "4.45.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["prettier --write --ignore-unknown --plugin prettier-plugin-tailwindcss --plugin @trivago/prettier-plugin-sort-imports", "cross-env PRE_COMMIT=true eslint --fix --config eslint.config.mjs", "git update-index --again"], "public/locale/*.json": ["npm run sort-locales"]}, "engines": {"node": ">=22.9.0"}, "packageManager": "npm@11.3.0"}