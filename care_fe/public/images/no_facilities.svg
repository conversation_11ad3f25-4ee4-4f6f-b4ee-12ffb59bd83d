<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1000" height="1000" viewBox="0 0 1000 1000" xml:space="preserve">
<desc>Created with Fabric.js 3.5.0</desc>
<defs>
</defs>
<rect x="0" y="0" width="100%" height="100%" fill="#ffffff"/>
<g transform="matrix(1.0763 0 0 1.0763 500.0166 580.0117)" id="940076">
<g style="" vector-effect="non-scaling-stroke">
		<g transform="matrix(1 0 0 1 9.0551 250.3692)">
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(0,160,113); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="46"/>
</g>
		<g transform="matrix(1 0 0 1 43.4995 23.0069)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(242,242,242); fill-rule: nonzero; opacity: 1;" transform=" translate(-643.4995, -473.0069)" d="M 883.86176 743.78487 c 25.7345 -7.72868 53.09381 -15.78786 73.50313 -34.161 c 18.23763 -16.41813 30.55024 -41.48912 22.99475 -66.1115 c -7.54 -24.57187 -30.12421 -40.95629 -53.44165 -49.10532 c -13.225 -4.62188 -27.06087 -7.18608 -40.89147 -9.20037 c -15.03485 -2.18967 -30.13615 -3.98373 -45.23026 -5.71012 q -91.67724 -10.48563 -184.04386 -12.811 c -30.38456 -0.76525 -60.76358 -0.74682 -91.15243 -0.3057 c -27.13937 0.394 -55.72215 0.38417 -80.899 -11.15051 c -19.57846 -8.96979 -37.348 -25.28881 -42.8033 -46.73352 c -6.29728 -24.75467 5.318 -49.96382 21.97993 -67.892 c 8.78265 -9.45011 19.04731 -17.40385 29.63621 -24.71743 c 11.4874 -7.93416 23.37539 -15.30643 35.52084 -22.18813 a 494.63414 494.63414 0 0 1 74.7667 -34.4685 c 12.74555 -4.63445 25.68047 -8.63281 38.72759 -12.32143 c 11.017 -3.11469 22.06832 -6.23382 32.71588 -10.47748 c 20.58349 -8.20371 40.161 -22.09985 45.39464 -44.88142 c 4.96024 -21.59145 -3.40305 -45.03067 -18.065 -61.07057 c -16.96282 -18.557 -42.53949 -26.69181 -67.06007 -28.008 c -27.52842 -1.47765 -54.42246 5.412 -80.29678 14.15585 c -27.59673 9.326 -54.59854 20.04924 -82.77827 27.60322 a 556.95783 556.95783 0 0 1 -85.19574 15.83655 c -14.08227 1.49951 -28.59019 3.19273 -42.75626 2.04475 c -11.87246 -0.96211 -23.68426 -4.45375 -32.43408 -12.87964 c -7.50252 -7.22477 -11.97184 -17.154 -10.4353 -27.63238 c 0.27909 -1.90318 3.17022 -1.09407 2.89284 0.79752 c -1.8704 12.75513 6.79991 24.50863 17.48415 30.5293 c 12.34817 6.95832 27.37408 6.9678 41.1218 6.172 a 537.82528 537.82528 0 0 0 88.51452 -12.79561 c 28.59252 -6.53059 56.16382 -15.86633 83.70391 -25.83908 c 26.15594 -9.47153 52.89665 -18.71579 80.84009 -20.76729 c 24.24611 -1.78007 49.75165 1.75222 70.87426 14.42313 c 18.56387 11.136 32.21482 29.70722 36.56451 51.01813 c 4.25044 20.82462 -1.63632 41.785 -17.4 56.31714 c -16.32147 15.04633 -38.7007 21.47909 -59.55655 27.40368 c -26.45223 7.51437 -52.33726 16.29809 -77.39248 27.7031 a 485.82354 485.82354 0 0 0 -72.8001 40.92805 c -22.24625 15.20228 -44.2007 34.33058 -51.23658 61.45126 c -3.27739 12.63313 -2.67227 26.03212 2.8116 37.96461 c 4.87605 10.60993 12.90656 19.53469 22.26169 26.41853 c 22.32074 16.42443 50.45266 19.79665 77.41421 20.13212 c 30.28143 0.37678 60.56382 -0.64518 90.85508 -0.148 q 92.5988 1.51977 184.81863 11.27265 q 23.108 2.44594 46.15759 5.40711 c 13.82158 1.776 27.68967 3.54058 41.27849 6.69464 c 24.16222 5.60822 47.67389 16.39167 62.69178 36.878 a 61.31938 61.31938 0 0 1 11.94709 30.44593 c 1.05134 11.52384 -1.76985 23.0693 -6.98016 33.32083 c -11.53233 22.69042 -33.13363 37.12286 -56.07337 46.60471 c -12.28683 5.0786 -25.03167 8.926 -37.7516 12.74609 c -1.853 0.55651 -2.64487 -2.338 -0.79752 -2.89283 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -296.4449 -228.1308)">
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(242,242,242); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="24"/>
</g>
		<g transform="matrix(1 0 0 1 -129.9449 -182.6308)">
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(242,242,242); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="24"/>
</g>
		<g transform="matrix(1 0 0 1 11.0551 -273.6308)">
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(242,242,242); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="24"/>
</g>
		<g transform="matrix(1 0 0 1 99.0551 -171.6308)">
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(242,242,242); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="24"/>
</g>
		<g transform="matrix(1 0 0 1 -19.9449 -53.6308)">
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(242,242,242); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="24"/>
</g>
		<g transform="matrix(1 0 0 1 -170.9449 3.3692)">
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(242,242,242); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="24"/>
</g>
		<g transform="matrix(1 0 0 1 -10.9449 92.3692)">
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(242,242,242); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="24"/>
</g>
		<g transform="matrix(1 0 0 1 161.0551 142.3692)">
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(242,242,242); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="24"/>
</g>
		<g transform="matrix(1 0 0 1 362.0551 131.3692)">
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(242,242,242); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="24"/>
</g>
		<g transform="matrix(1 0 0 1 5.0868 -36.6308)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(0,160,113); fill-rule: nonzero; opacity: 1;" transform=" translate(-605.0868, -413.3692)" d="M 604.12763 220.37264 c -71.89185 0.50782 -130.75611 58.92987 -131.77735 130.81635 c -0.00946 0.66369 -0.01381 5.33048 -0.01324 11.43422 a 33.74778 33.74778 0 0 0 33.74387 33.746 h 0.00007 a 33.76855 33.76855 0 0 0 33.76114 -33.79775 c -0.00343 -4.15211 -0.00551 -7.02584 -0.00551 -7.20227 a 64.00037 64.00037 0 1 1 98.52027 53.8794 l 0.01171 0.01422 s -48.02832 30.91956 -62.67089 73.33545 l 0.01245 0.003 a 94.00389 94.00389 0 0 0 -3.87354 26.76794 c 0 3.72538 0.21916 36.32138 0.64261 62.77767 a 34.78649 34.78649 0 0 0 34.79009 34.22233 h 0.00007 a 34.79588 34.79588 0 0 0 34.79384 -35.01061 c -0.14706 -24.22912 -0.22661 -52.44168 -0.22661 -54.48939 c 0 -26.04473 25.12525 -51.99475 45.76367 -68.91741 c 23.76587 -19.48694 40.86792 -46.04291 47.73706 -75.99909 a 86.7618 86.7618 0 0 0 2.49927 -18.8335 A 132.75 132.75 0 0 0 604.12763 220.37264 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 0.0018 296.4401)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(63,61,86); fill-rule: nonzero; opacity: 1;" transform=" translate(-600.0017, -746.4401)" d="M 1021.147 747.63078 H 178.853 a 1.19069 1.19069 0 0 1 0 -2.38137 h 842.294 a 1.19068 1.19068 0 0 1 0 2.38137 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 206.1112 -54.6349)">
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(47,46,65); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30"/>
</g>
		<g transform="matrix(1 0 0 1 141.6298 260.8542)">
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(160,97,106); fill-rule: nonzero; opacity: 1;" points="9.0445,23.644 -3.2145,23.644 -9.0475,-23.644 9.0475,-23.644 9.0445,23.644 "/>
</g>
		<g transform="matrix(1 0 0 1 134.0364 288.4382)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(47,46,65); fill-rule: nonzero; opacity: 1;" transform=" translate(-556.374, -586.0691)" d="M 551.99554 578.62562 h 23.64387 a 0 0 0 0 1 0 0 v 14.88687 a 0 0 0 0 1 0 0 H 537.10868 a 0 0 0 0 1 0 0 v 0 A 14.88686 14.88686 0 0 1 551.99554 578.62562 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 236.6298 260.8542)">
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(160,97,106); fill-rule: nonzero; opacity: 1;" points="9.0445,23.644 -3.2145,23.644 -9.0475,-23.644 9.0475,-23.644 9.0445,23.644 "/>
</g>
		<g transform="matrix(1 0 0 1 229.0364 288.4382)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(47,46,65); fill-rule: nonzero; opacity: 1;" transform=" translate(-651.374, -586.0691)" d="M 646.99554 578.62562 h 23.64387 a 0 0 0 0 1 0 0 v 14.88687 a 0 0 0 0 1 0 0 H 632.10868 a 0 0 0 0 1 0 0 v 0 A 14.88686 14.88686 0 0 1 646.99554 578.62562 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 201.5521 -49.0207)">
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(160,97,106); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="24.56103"/>
</g>
		<g transform="matrix(1 0 0 1 190.5935 128.2151)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(47,46,65); fill-rule: nonzero; opacity: 1;" transform=" translate(-790.5934, -578.2151)" d="M 816.19123 504.7751 l 10.98975 -25.25 a 31.38253 31.38253 0 0 0 -6.94971 -35.6 a 31.87322 31.87322 0 0 0 -3.07031 -2.67 a 30.93522 30.93522 0 0 0 -18.98975 -6.57 a 32.179 32.179 0 0 0 -13.3999 2.98 c -0.36035 0.16 -0.71 0.33 -1.07031 0.5 c -0.68994 0.33 -1.36963 0.69 -2.02979 1.06 a 31.67823 31.67823 0 0 0 -15.70019 23.88 l -4.8501 40.64 c -1.21973 3.19 -44.73975 118.39 -29.51953 206.34 a 4.46692 4.46692 0 0 0 3.81982 3.67 l 15.43018 2.1 a 4.49661 4.49661 0 0 0 5.00976 -3.53 l 25.89014 -123.41 a 3.50323 3.50323 0 0 1 6.79981 -0.23 l 36.58007 129.78 a 4.47129 4.47129 0 0 0 4.31006 3.28 a 5.12184 5.12184 0 0 0 0.87012 -0.08 l 18.84961 -3.63 a 4.471 4.471 0 0 0 3.63037 -4.81 C 850.02131 682.3351 835.3011 527.60512 816.19123 504.7751 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 123.4219 -10.9846)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(160,97,106); fill-rule: nonzero; opacity: 1;" transform=" translate(-723.4219, -439.0154)" d="M 706.10166 421.41909 A 10.05576 10.05576 0 0 0 716.696 432.6225 l 13.72894 32.99236 l 10.385 -15.3943 l -14.62937 -28.97 a 10.11027 10.11027 0 0 0 -20.07892 0.16852 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 213.1579 67.8037)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(160,97,106); fill-rule: nonzero; opacity: 1;" transform=" translate(-813.1578, -517.8037)" d="M 800.19025 537.99553 a 10.05577 10.05577 0 0 0 8.42651 -12.91316 l 28.88533 -21.03846 l -17.39036 -6.51224 l -24.76387 20.97687 a 10.11028 10.11028 0 0 0 4.84239 19.487 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 155.4936 13.0003)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(47,46,65); fill-rule: nonzero; opacity: 1;" transform=" translate(-755.4936, -463.0003)" d="M 753.10188 487.61024 a 17.05692 17.05692 0 0 1 -3.29834 -0.32519 a 16.30539 16.30539 0 0 1 -11.94751 -9.61621 l -19.23438 -23.45313 a 4.50075 4.50075 0 0 1 1.11109 -6.68066 l 13.68432 -8.4707 a 4.50007 4.50007 0 0 1 6.21533 1.49023 l 13.5564 22.334 L 779.15022 443.702 A 9.72146 9.72146 0 0 1 790.46 459.26356 l -25.91186 23.63672 A 16.25271 16.25271 0 0 1 753.10188 487.61024 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 231.3344 34.3032)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(47,46,65); fill-rule: nonzero; opacity: 1;" transform=" translate(-831.3344, -484.3032)" d="M 823.252 522.8827 c -0.03515 0 -0.07055 0 -0.10571 -0.001 a 4.50783 4.50783 0 0 1 -3.31079 -1.57031 l -12.16626 -14.19336 a 4.49979 4.49979 0 0 1 0.92041 -6.67286 l 22.78149 -15.1875 l -20.63842 -24.8125 a 9.721 9.721 0 0 1 14.8872 -12.18261 l 25.0835 24.51269 a 16.52481 16.52481 0 0 1 -3.67529 26.94043 l -20.50122 21.75391 A 4.50742 4.50742 0 0 1 823.252 522.8827 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 199.2247 1.3843)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(230,230,230); fill-rule: nonzero; opacity: 1;" transform=" translate(-799.2247, -451.3843)" d="M 795.30707 470.58358 a 4.63212 4.63212 0 0 1 -0.584 -0.03711 a 4.46111 4.46111 0 0 1 -3.71045 -3.06885 l -9.14234 -28.02929 a 3.08255 3.08255 0 0 1 1.594 -3.72461 l 0.29663 -0.14014 c 0.269 -0.12793 0.5354 -0.25439 0.80737 -0.37549 a 32.57412 32.57412 0 0 1 13.603 -3.023 a 31.327 31.327 0 0 1 17.16138 5.15674 a 3.13007 3.13007 0 0 1 0.90136 4.29443 L 799.08393 468.504 A 4.45513 4.45513 0 0 1 795.30707 470.58358 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 229.7634 -77.8446)">
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(47,46,65); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="9.81668"/>
</g>
		<g transform="matrix(1 0 0 1 201.6112 -70.1349)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(47,46,65); fill-rule: nonzero; opacity: 1;" transform=" translate(-801.6111, -379.8651)" d="M 796.11115 361.36513 h 0 a 26 26 0 0 0 -26 25.99994 v 11.00006 h 13.5293 l 6.4707 -11 l 1.94141 11 h 41.05859 l -11 -11.00006 A 26 26 0 0 0 796.11115 361.36513 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 266.96 -63.0385)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(47,46,65); fill-rule: nonzero; opacity: 1;" transform=" translate(-866.96, -386.9615)" d="M 834.80883 365.43121 a 15.15 15.15 0 0 1 16.48081 -10.39558 c 6.256 1.04586 11.20228 6.07455 14.14944 11.69107 s 4.30806 11.90252 6.28935 17.92793 s 4.79124 12.08362 9.79306 15.984 s 12.67721 4.9584 17.58966 0.94607 a 20.11809 20.11809 0 0 1 -37.47706 7.18124 c -2.59206 -4.61172 -3.26121 -10.01684 -4.02988 -15.251 s -1.7674 -10.6498 -4.86211 -14.94043 s -8.88772 -7.09293 -13.80374 -5.13859 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -95.0193 -114.1369)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(63,61,86); fill-rule: nonzero; opacity: 1;" transform=" translate(-504.9806, -335.8631)" d="M 515.60883 380.40755 h 0 a 33.748 33.748 0 0 1 -33.74414 -33.746 c -0.00049 -6.10376 0.0039 -10.77051 0.01318 -11.4342 a 131.50724 131.50724 0 0 1 15.35889 -59.90875 a 131.80321 131.80321 0 0 0 -25.35889 75.90875 c -0.00928 0.66369 -0.01367 5.33044 -0.01318 11.4342 a 33.748 33.748 0 0 0 33.74414 33.746 h 0 A 33.77281 33.77281 0 0 0 538.09662 371.817 A 33.62247 33.62247 0 0 1 515.60883 380.40755 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 15.9971 -121.8518)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(63,61,86); fill-rule: nonzero; opacity: 1;" transform=" translate(-615.9971, -328.1482)" d="M 606.415 291.47848 a 64.00385 64.00385 0 0 1 55.65918 89.413 a 63.9972 63.9972 0 1 0 -107.42578 -66.98523 A 63.87073 63.87073 0 0 1 606.415 291.47848 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 5.7814 80.1716)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(63,61,86); fill-rule: nonzero; opacity: 1;" transform=" translate(-605.7814, -530.1716)" d="M 616.79682 590.40755 h 0 a 34.78682 34.78682 0 0 1 -34.79 -34.22235 c -0.42334 -26.45629 -0.64258 -59.0523 -0.64258 -62.77765 a 94.00389 94.00389 0 0 1 3.87354 -26.76794 l -0.01221 -0.003 a 95.069 95.069 0 0 1 5.49414 -12.70087 a 110.04745 110.04745 0 0 0 -15.49414 28.70087 l 0.01221 0.003 a 94.00389 94.00389 0 0 0 -3.87354 26.76794 c 0 3.72535 0.21924 36.32136 0.64258 62.77765 a 34.78682 34.78682 0 0 0 34.79 34.22235 h 0 a 34.80287 34.80287 0 0 0 33.40185 -25.04846 A 34.66005 34.66005 0 0 1 616.79682 590.40755 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -286.7522 261.3097)">
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,184,184); fill-rule: nonzero; opacity: 1;" points="-9.0445,23.6445 3.2145,23.6435 9.0475,-23.6445 -9.0475,-23.6435 -9.0445,23.6445 "/>
</g>
		<g transform="matrix(-1 0 0 -1 -279.1587 288.8941)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(47,46,65); fill-rule: nonzero; opacity: 1;" transform=" translate(-320.8414, -738.8941)" d="M 301.576 731.45065 H 340.1067 a 0 0 0 0 1 0 0 v 14.88687 a 0 0 0 0 1 0 0 H 316.46283 A 14.88686 14.88686 0 0 1 301.576 731.45066 v 0 A 0 0 0 0 1 301.576 731.45065 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -330.7522 261.3097)">
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,184,184); fill-rule: nonzero; opacity: 1;" points="-9.0445,23.6445 3.2145,23.6435 9.0475,-23.6445 -9.0475,-23.6435 -9.0445,23.6445 "/>
</g>
		<g transform="matrix(-1 0 0 -1 -323.1587 288.8941)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(47,46,65); fill-rule: nonzero; opacity: 1;" transform=" translate(-276.8414, -738.8941)" d="M 257.576 731.45065 H 296.1067 a 0 0 0 0 1 0 0 v 14.88687 a 0 0 0 0 1 0 0 H 272.46283 A 14.88686 14.88686 0 0 1 257.576 731.45066 v 0 A 0 0 0 0 1 257.576 731.45065 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -302.1948 188.3829)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(47,46,65); fill-rule: nonzero; opacity: 1;" transform=" translate(-297.8052, -638.3828)" d="M 270.91659 720.41068 l -11.975 -0.62988 a 4.6735 4.6735 0 0 1 -4.41851 -4.967 l 14.31268 -158.46594 l 65.911 17.78562 l 6.35023 -1.73241 L 321.23868 712.68583 a 4.69622 4.69622 0 0 1 -4.35816 3.94458 l -12.9089 0.60147 a 4.67413 4.67413 0 0 1 -4.93149 -4.79557 l 2.339 -84.19641 a 1.55813 1.55813 0 0 0 -3.0832 -0.36007 L 275.739 716.69228 a 4.64568 4.64568 0 0 1 -4.56913 3.7255 C 271.086 720.41778 271.00154 720.41575 270.91659 720.41068 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -293.5957 -47.872)">
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,184,184); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="24.56103"/>
</g>
		<g transform="matrix(1 0 0 1 -296.3333 58.2629)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(204,204,204); fill-rule: nonzero; opacity: 1;" transform=" translate(-303.6667, -508.2628)" d="M 265.51193 474.28693 l 2.70056 58.26748 l 0.97625 21.19852 a 4.64221 4.64221 0 0 0 3.07432 4.17534 l 63.336 22.94342 a 4.47742 4.47742 0 0 0 1.59954 0.28045 a 4.64358 4.64358 0 0 0 4.66371 -4.7881 L 339.2657 471.5969 A 36.93044 36.93044 0 0 0 308.522 435.91974 c -0.61263 -0.09345 -1.23592 -0.18695 -1.8592 -0.27006 a 36.24947 36.24947 0 0 0 -29.165 9.44122 a 37.23612 37.23612 0 0 0 -11.9859 29.196 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -240.9843 97.0157)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,184,184); fill-rule: nonzero; opacity: 1;" transform=" translate(-359.0157, -547.0156)" d="M 365.85452 569.24512 a 10.06355 10.06355 0 0 1 -5.36877 -15.22659 l -21.478 -28.56 l 18.53424 -1.14707 l 17.55439 27.29693 a 10.111 10.111 0 0 1 -9.24184 17.63673 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -256.7804 50.5708)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(204,204,204); fill-rule: nonzero; opacity: 1;" transform=" translate(-343.2197, -500.5708)" d="M 350.75332 548.85022 a 4.64437 4.64437 0 0 1 -2.54106 -2.51848 L 315.854 469.2374 a 12.4634 12.4634 0 1 1 22.98438 -9.64693 l 32.3582 77.09534 a 4.679 4.679 0 0 1 -2.50048 6.11822 l -14.36542 6.029 a 4.64165 4.64165 0 0 1 -3.57741 0.01724 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -259.0777 80.4123)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(63,61,86); fill-rule: nonzero; opacity: 1;" transform=" translate(-340.9223, -530.4123)" d="M 298.50776 546.13086 L 329.587 486.62205 a 4.87826 4.87826 0 0 1 6.57494 -2.06344 l 45.11152 23.5601 a 4.87826 4.87826 0 0 1 2.06343 6.57494 l -31.07927 59.50881 a 4.87827 4.87827 0 0 1 -6.57494 2.06344 L 300.5712 552.7058 A 4.87826 4.87826 0 0 1 298.50776 546.13086 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -302.9864 63.702)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,184,184); fill-rule: nonzero; opacity: 1;" transform=" translate(-297.0136, -513.7019)" d="M 319.35062 518.94278 a 10.06358 10.06358 0 0 0 -15.517 -4.46026 l -29.77845 -19.75406 l -0.05061 18.56963 L 302.2904 529.21 a 10.111 10.111 0 0 0 17.06022 -10.26718 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -327.3441 34.0855)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(204,204,204); fill-rule: nonzero; opacity: 1;" transform=" translate(-272.656, -484.0855)" d="M 281.7006 523.11883 l -24.33677 -19.27776 a 17.16326 17.16326 0 0 1 -7.82343 -27.13518 l 22.09715 -28.95951 a 10.096 10.096 0 0 1 17.1296 10.28435 l -17.48384 28.6 l 25.694 12.18686 a 4.67363 4.67363 0 0 1 1.94814 6.71958 l -10.37175 16.41406 a 4.682 4.682 0 0 1 -3.16671 2.1111 c -0.02565 0.00448 -0.05149 0.00846 -0.0773 0.0123 A 4.69555 4.69555 0 0 1 281.7006 523.11883 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -296.8293 -55.9969)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(47,46,65); fill-rule: nonzero; opacity: 1;" transform=" translate(-303.1706, -394.0031)" d="M 287.84537 418.57447 a 2.13479 2.13479 0 0 1 1.85636 -2.81905 a 4.93046 4.93046 0 0 1 3.4761 1.71495 a 13.8334 13.8334 0 0 0 3.07115 2.63711 c 1.18812 0.59889 2.79953 0.51354 3.47685 -0.62824 c 0.63605 -1.07221 0.20023 -2.508 -0.18482 -3.75347 a 36.90711 36.90711 0 0 1 -1.62991 -9.77 c -0.11092 -3.70032 0.41115 -7.562 2.45972 -10.44807 c 2.64387 -3.72475 7.37142 -5.13883 11.84544 -5.0363 s 8.87547 1.48362 13.30713 2.35665 c 1.52992 0.30139 3.32826 0.4555 4.35153 -0.73025 c 1.08805 -1.26082 0.68844 -3.3014 0.22563 -5.00376 c -1.20094 -4.41743 -2.475 -8.98461 -5.26525 -12.55224 a 18.89839 18.89839 0 0 0 -12.06081 -6.79014 a 28.93848 28.93848 0 0 0 -13.46236 1.52838 a 36.09628 36.09628 0 0 0 -17.68285 12.3186 a 29.23592 29.23592 0 0 0 -5.57809 21.60019 a 26.66712 26.66712 0 0 0 9.88579 16.85462 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 3.697 255.2343)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(63,61,86); fill-rule: nonzero; opacity: 1;" transform=" translate(-603.697, -705.2343)" d="M 598.92043 735.14922 a 45.99375 45.99375 0 0 1 -17.07033 -71.4888 a 45.99715 45.99715 0 1 0 62.56892 66.464 A 45.96919 45.96919 0 0 1 598.92043 735.14922 Z" stroke-linecap="round"/>
</g>
</g>
</g>
<g transform="matrix(1 0 0 1 497.8985 157)" style="" id="846269">
		
<path xml:space="preserve" font-family="Lato" font-size="100" font-style="normal" font-weight="bold" style="stroke: rgb(0,160,113); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(0,160,113); fill-rule: nonzero; opacity: 1; white-space: pre;" d="M-410.42-40.89L-410.42-40.89Q-409.52-40.89-408.92-40.81Q-408.32-40.74-407.85-40.51Q-407.37-40.29-406.92-39.86Q-406.47-39.44-405.92-38.74L-405.92-38.74L-367.97 9.61Q-368.17 7.86-368.25 6.19Q-368.32 4.51-368.32 3.06L-368.32 3.06L-368.32-40.89L-356.47-40.89L-356.47 31.41L-363.42 31.41Q-365.02 31.41-366.07 30.91Q-367.12 30.41-368.12 29.11L-368.12 29.11L-405.92-19.04Q-405.77-17.44-405.70-15.86Q-405.62-14.29-405.62-12.99L-405.62-12.99L-405.62 31.41L-417.47 31.41L-417.47-40.89L-410.42-40.89ZM-320.67-20.69L-320.67-20.69Q-314.92-20.69-310.25-18.84Q-305.57-16.99-302.27-13.59Q-298.97-10.19-297.17-5.29Q-295.37-0.39-295.37 5.66L-295.37 5.66Q-295.37 11.76-297.17 16.66Q-298.97 21.56-302.27 25.01Q-305.57 28.46-310.25 30.31Q-314.92 32.16-320.67 32.16L-320.67 32.16Q-326.42 32.16-331.12 30.31Q-335.82 28.46-339.15 25.01Q-342.47 21.56-344.30 16.66Q-346.12 11.76-346.12 5.66L-346.12 5.66Q-346.12-0.39-344.30-5.29Q-342.47-10.19-339.15-13.59Q-335.82-16.99-331.12-18.84Q-326.42-20.69-320.67-20.69ZM-320.67 22.66L-320.67 22.66Q-314.27 22.66-311.20 18.36Q-308.12 14.06-308.12 5.76L-308.12 5.76Q-308.12-2.54-311.20-6.89Q-314.27-11.24-320.67-11.24L-320.67-11.24Q-327.17-11.24-330.27-6.86Q-333.37-2.49-333.37 5.76L-333.37 5.76Q-333.37 14.01-330.27 18.34Q-327.17 22.66-320.67 22.66ZM-265.62-42.89L-253.27-42.89L-253.27 31.41L-265.62 31.41L-265.62-42.89ZM-238.52-19.89L-226.17-19.89L-226.17 31.41L-238.52 31.41L-238.52-19.89ZM-224.32-34.84L-224.32-34.84Q-224.32-33.24-224.97-31.84Q-225.62-30.44-226.70-29.39Q-227.77-28.34-229.22-27.71Q-230.67-27.09-232.32-27.09L-232.32-27.09Q-233.92-27.09-235.35-27.71Q-236.77-28.34-237.82-29.39Q-238.87-30.44-239.50-31.84Q-240.12-33.24-240.12-34.84L-240.12-34.84Q-240.12-36.49-239.50-37.94Q-238.87-39.39-237.82-40.44Q-236.77-41.49-235.35-42.11Q-233.92-42.74-232.32-42.74L-232.32-42.74Q-230.67-42.74-229.22-42.11Q-227.77-41.49-226.70-40.44Q-225.62-39.39-224.97-37.94Q-224.32-36.49-224.32-34.84ZM-199.87 31.41L-212.22 31.41L-212.22-19.89L-204.67-19.89Q-202.27-19.89-201.52-17.64L-201.52-17.64L-200.67-13.59Q-199.12-15.19-197.40-16.49Q-195.67-17.79-193.75-18.74Q-191.82-19.69-189.62-20.19Q-187.42-20.69-184.82-20.69L-184.82-20.69Q-180.62-20.69-177.37-19.26Q-174.12-17.84-171.95-15.26Q-169.77-12.69-168.65-9.11Q-167.52-5.54-167.52-1.24L-167.52-1.24L-167.52 31.41L-179.87 31.41L-179.87-1.24Q-179.87-5.94-182.05-8.51Q-184.22-11.09-188.57-11.09L-188.57-11.09Q-191.77-11.09-194.57-9.64Q-197.37-8.19-199.87-5.69L-199.87-5.69L-199.87 31.41ZM-155.22-42.89L-142.87-42.89L-142.87-0.34L-140.57-0.34Q-139.32-0.34-138.62-0.69Q-137.92-1.04-137.12-1.99L-137.12-1.99L-124.37-17.74Q-123.52-18.74-122.57-19.31Q-121.62-19.89-120.07-19.89L-120.07-19.89L-108.77-19.89L-124.72-0.84Q-125.57 0.21-126.47 1.09Q-127.37 1.96-128.42 2.61L-128.42 2.61Q-127.37 3.36-126.57 4.36Q-125.77 5.36-124.97 6.51L-124.97 6.51L-107.87 31.41L-119.02 31.41Q-120.47 31.41-121.47 30.91Q-122.47 30.41-123.27 29.21L-123.27 29.21L-136.32 9.76Q-137.07 8.61-137.82 8.26Q-138.57 7.91-140.07 7.91L-140.07 7.91L-142.87 7.91L-142.87 31.41L-155.22 31.41L-155.22-42.89ZM-82.02-20.69L-82.02-20.69Q-77.17-20.69-73.10-19.14Q-69.02-17.59-66.07-14.61Q-63.12-11.64-61.47-7.31Q-59.82-2.99-59.82 2.56L-59.82 2.56Q-59.82 3.96-59.95 4.89Q-60.07 5.81-60.40 6.34Q-60.72 6.86-61.27 7.09Q-61.82 7.31-62.67 7.31L-62.67 7.31L-94.37 7.31Q-93.82 15.21-90.12 18.91Q-86.42 22.61-80.32 22.61L-80.32 22.61Q-77.32 22.61-75.15 21.91Q-72.97 21.21-71.35 20.36Q-69.72 19.51-68.50 18.81Q-67.27 18.11-66.12 18.11L-66.12 18.11Q-65.37 18.11-64.82 18.41Q-64.27 18.71-63.87 19.26L-63.87 19.26L-60.27 23.76Q-62.32 26.16-64.87 27.79Q-67.42 29.41-70.20 30.39Q-72.97 31.36-75.85 31.76Q-78.72 32.16-81.42 32.16L-81.42 32.16Q-86.77 32.16-91.37 30.39Q-95.97 28.61-99.37 25.14Q-102.77 21.66-104.72 16.54Q-106.67 11.41-106.67 4.66L-106.67 4.66Q-106.67-0.59-104.97-5.21Q-103.27-9.84-100.10-13.26Q-96.92-16.69-92.35-18.69Q-87.77-20.69-82.02-20.69ZM-81.77-11.84L-81.77-11.84Q-87.17-11.84-90.22-8.79Q-93.27-5.74-94.12-0.14L-94.12-0.14L-70.92-0.14Q-70.92-2.54-71.57-4.66Q-72.22-6.79-73.57-8.39Q-74.92-9.99-76.97-10.91Q-79.02-11.84-81.77-11.84ZM-6.12 31.41L-13.67 31.41Q-16.07 31.41-16.82 29.16L-16.82 29.16L-17.82 24.21Q-19.42 26.01-21.17 27.46Q-22.92 28.91-24.95 29.96Q-26.97 31.01-29.30 31.59Q-31.62 32.16-34.32 32.16L-34.32 32.16Q-38.52 32.16-42.02 30.41Q-45.52 28.66-48.05 25.34Q-50.57 22.01-51.95 17.11Q-53.32 12.21-53.32 5.91L-53.32 5.91Q-53.32 0.21-51.77-4.69Q-50.22-9.59-47.32-13.19Q-44.42-16.79-40.37-18.81Q-36.32-20.84-31.27-20.84L-31.27-20.84Q-26.97-20.84-23.92-19.46Q-20.87-18.09-18.47-15.79L-18.47-15.79L-18.47-42.89L-6.12-42.89L-6.12 31.41ZM-30.12 22.36L-30.12 22.36Q-26.27 22.36-23.57 20.76Q-20.87 19.16-18.47 16.21L-18.47 16.21L-18.47-6.79Q-20.57-9.34-23.05-10.39Q-25.52-11.44-28.37-11.44L-28.37-11.44Q-31.17-11.44-33.45-10.39Q-35.72-9.34-37.30-7.21Q-38.87-5.09-39.72-1.81Q-40.57 1.46-40.57 5.91L-40.57 5.91Q-40.57 10.41-39.85 13.54Q-39.12 16.66-37.77 18.64Q-36.42 20.61-34.47 21.49Q-32.52 22.36-30.12 22.36ZM41.08 31.41L28.73 31.41L28.73-10.59L24.28-11.29Q22.83-11.54 21.95-12.29Q21.08-13.04 21.08-14.39L21.08-14.39L21.08-19.44L28.73-19.44L28.73-23.24Q28.73-27.64 30.05-31.14Q31.38-34.64 33.85-37.09Q36.33-39.54 39.88-40.84Q43.43-42.14 47.88-42.14L47.88-42.14Q51.43-42.14 54.48-41.19L54.48-41.19L54.23-34.99Q54.13-33.54 52.88-33.19Q51.63-32.84 49.98-32.84L49.98-32.84Q47.78-32.84 46.05-32.36Q44.33-31.89 43.13-30.71Q41.93-29.54 41.30-27.61Q40.68-25.69 40.68-22.84L40.68-22.84L40.68-19.44L54.03-19.44L54.03-10.64L41.08-10.64L41.08 31.41ZM101.78-0.99L101.78 31.41L96.23 31.41Q94.48 31.41 93.48 30.89Q92.48 30.36 91.98 28.76L91.98 28.76L90.88 25.11Q88.93 26.86 87.05 28.19Q85.18 29.51 83.18 30.41Q81.18 31.31 78.93 31.76Q76.68 32.21 73.93 32.21L73.93 32.21Q70.68 32.21 67.93 31.34Q65.18 30.46 63.20 28.71Q61.23 26.96 60.13 24.36Q59.03 21.76 59.03 18.31L59.03 18.31Q59.03 15.41 60.55 12.59Q62.08 9.76 65.63 7.49Q69.18 5.21 75.08 3.71Q80.98 2.21 89.73 2.01L89.73 2.01L89.73-0.99Q89.73-6.14 87.55-8.61Q85.38-11.09 81.23-11.09L81.23-11.09Q78.23-11.09 76.23-10.39Q74.23-9.69 72.75-8.81Q71.28-7.94 70.03-7.24Q68.78-6.54 67.28-6.54L67.28-6.54Q66.03-6.54 65.13-7.19Q64.23-7.84 63.68-8.79L63.68-8.79L61.43-12.74Q70.28-20.84 82.78-20.84L82.78-20.84Q87.28-20.84 90.80-19.36Q94.33-17.89 96.78-15.26Q99.23-12.64 100.50-8.99Q101.78-5.34 101.78-0.99L101.78-0.99ZM77.78 23.71L77.78 23.71Q79.68 23.71 81.28 23.36Q82.88 23.01 84.30 22.31Q85.73 21.61 87.05 20.59Q88.38 19.56 89.73 18.16L89.73 18.16L89.73 9.51Q84.33 9.76 80.70 10.44Q77.08 11.11 74.88 12.16Q72.68 13.21 71.75 14.61Q70.83 16.01 70.83 17.66L70.83 17.66Q70.83 20.91 72.75 22.31Q74.68 23.71 77.78 23.71ZM153.23-13.54L149.98-9.04Q149.43-8.34 148.90-7.94Q148.38-7.54 147.38-7.54L147.38-7.54Q146.43-7.54 145.53-8.11Q144.63-8.69 143.38-9.41Q142.13-10.14 140.40-10.71Q138.68-11.29 136.13-11.29L136.13-11.29Q132.88-11.29 130.43-10.11Q127.98-8.94 126.35-6.74Q124.73-4.54 123.93-1.41Q123.13 1.71 123.13 5.66L123.13 5.66Q123.13 9.76 124.00 12.96Q124.88 16.16 126.53 18.34Q128.18 20.51 130.53 21.64Q132.88 22.76 135.83 22.76L135.83 22.76Q138.78 22.76 140.60 22.04Q142.43 21.31 143.68 20.44Q144.93 19.56 145.85 18.84Q146.78 18.11 147.93 18.11L147.93 18.11Q149.43 18.11 150.18 19.26L150.18 19.26L153.73 23.76Q151.68 26.16 149.28 27.79Q146.88 29.41 144.30 30.39Q141.73 31.36 138.98 31.76Q136.23 32.16 133.53 32.16L133.53 32.16Q128.78 32.16 124.58 30.39Q120.38 28.61 117.25 25.21Q114.13 21.81 112.30 16.89Q110.48 11.96 110.48 5.66L110.48 5.66Q110.48 0.01 112.10-4.81Q113.73-9.64 116.88-13.16Q120.03-16.69 124.68-18.69Q129.33-20.69 135.38-20.69L135.38-20.69Q141.13-20.69 145.45-18.84Q149.78-16.99 153.23-13.54L153.23-13.54ZM162.43-19.89L174.78-19.89L174.78 31.41L162.43 31.41L162.43-19.89ZM176.63-34.84L176.63-34.84Q176.63-33.24 175.98-31.84Q175.33-30.44 174.25-29.39Q173.18-28.34 171.73-27.71Q170.28-27.09 168.63-27.09L168.63-27.09Q167.03-27.09 165.60-27.71Q164.18-28.34 163.13-29.39Q162.08-30.44 161.45-31.84Q160.83-33.24 160.83-34.84L160.83-34.84Q160.83-36.49 161.45-37.94Q162.08-39.39 163.13-40.44Q164.18-41.49 165.60-42.11Q167.03-42.74 168.63-42.74L168.63-42.74Q170.28-42.74 171.73-42.11Q173.18-41.49 174.25-40.44Q175.33-39.39 175.98-37.94Q176.63-36.49 176.63-34.84ZM189.53-42.89L201.88-42.89L201.88 31.41L189.53 31.41L189.53-42.89ZM216.63-19.89L228.98-19.89L228.98 31.41L216.63 31.41L216.63-19.89ZM230.83-34.84L230.83-34.84Q230.83-33.24 230.18-31.84Q229.53-30.44 228.45-29.39Q227.38-28.34 225.93-27.71Q224.48-27.09 222.83-27.09L222.83-27.09Q221.23-27.09 219.80-27.71Q218.38-28.34 217.33-29.39Q216.28-30.44 215.65-31.84Q215.03-33.24 215.03-34.84L215.03-34.84Q215.03-36.49 215.65-37.94Q216.28-39.39 217.33-40.44Q218.38-41.49 219.80-42.11Q221.23-42.74 222.83-42.74L222.83-42.74Q224.48-42.74 225.93-42.11Q227.38-41.49 228.45-40.44Q229.53-39.39 230.18-37.94Q230.83-36.49 230.83-34.84ZM259.78 32.21L259.78 32.21Q253.13 32.21 249.53 28.44Q245.93 24.66 245.93 18.01L245.93 18.01L245.93-10.64L240.73-10.64Q239.73-10.64 239.00-11.29Q238.28-11.94 238.28-13.24L238.28-13.24L238.28-18.14L246.53-19.49L249.13-33.49Q249.33-34.49 250.05-35.04Q250.78-35.59 251.88-35.59L251.88-35.59L258.28-35.59L258.28-19.44L271.78-19.44L271.78-10.64L258.28-10.64L258.28 17.16Q258.28 19.56 259.48 20.91Q260.68 22.26 262.68 22.26L262.68 22.26Q263.83 22.26 264.60 21.99Q265.38 21.71 265.95 21.41Q266.53 21.11 266.98 20.84Q267.43 20.56 267.88 20.56L267.88 20.56Q268.43 20.56 268.78 20.84Q269.13 21.11 269.53 21.66L269.53 21.66L273.23 27.66Q270.53 29.91 267.03 31.06Q263.53 32.21 259.78 32.21ZM282.43-19.89L294.78-19.89L294.78 31.41L282.43 31.41L282.43-19.89ZM296.63-34.84L296.63-34.84Q296.63-33.24 295.98-31.84Q295.33-30.44 294.25-29.39Q293.18-28.34 291.73-27.71Q290.28-27.09 288.63-27.09L288.63-27.09Q287.03-27.09 285.60-27.71Q284.18-28.34 283.13-29.39Q282.08-30.44 281.45-31.84Q280.83-33.24 280.83-34.84L280.83-34.84Q280.83-36.49 281.45-37.94Q282.08-39.39 283.13-40.44Q284.18-41.49 285.60-42.11Q287.03-42.74 288.63-42.74L288.63-42.74Q290.28-42.74 291.73-42.11Q293.18-41.49 294.25-40.44Q295.33-39.39 295.98-37.94Q296.63-36.49 296.63-34.84ZM329.93-20.69L329.93-20.69Q334.78-20.69 338.85-19.14Q342.93-17.59 345.88-14.61Q348.83-11.64 350.48-7.31Q352.13-2.99 352.13 2.56L352.13 2.56Q352.13 3.96 352.00 4.89Q351.88 5.81 351.55 6.34Q351.23 6.86 350.68 7.09Q350.13 7.31 349.28 7.31L349.28 7.31L317.58 7.31Q318.13 15.21 321.83 18.91Q325.53 22.61 331.63 22.61L331.63 22.61Q334.63 22.61 336.80 21.91Q338.98 21.21 340.60 20.36Q342.23 19.51 343.45 18.81Q344.68 18.11 345.83 18.11L345.83 18.11Q346.58 18.11 347.13 18.41Q347.68 18.71 348.08 19.26L348.08 19.26L351.68 23.76Q349.63 26.16 347.08 27.79Q344.53 29.41 341.75 30.39Q338.98 31.36 336.10 31.76Q333.23 32.16 330.53 32.16L330.53 32.16Q325.18 32.16 320.58 30.39Q315.98 28.61 312.58 25.14Q309.18 21.66 307.23 16.54Q305.28 11.41 305.28 4.66L305.28 4.66Q305.28-0.59 306.98-5.21Q308.68-9.84 311.85-13.26Q315.03-16.69 319.60-18.69Q324.18-20.69 329.93-20.69ZM330.18-11.84L330.18-11.84Q324.78-11.84 321.73-8.79Q318.68-5.74 317.83-0.14L317.83-0.14L341.03-0.14Q341.03-2.54 340.38-4.66Q339.73-6.79 338.38-8.39Q337.03-9.99 334.98-10.91Q332.93-11.84 330.18-11.84ZM395.43-14.34L392.63-9.89Q392.13-9.09 391.58-8.76Q391.03-8.44 390.18-8.44L390.18-8.44Q389.28-8.44 388.25-8.94Q387.23-9.44 385.88-10.06Q384.53-10.69 382.80-11.19Q381.08-11.69 378.73-11.69L378.73-11.69Q375.08-11.69 372.98-10.14Q370.88-8.59 370.88-6.09L370.88-6.09Q370.88-4.44 371.95-3.31Q373.03-2.19 374.80-1.34Q376.58-0.49 378.83 0.19Q381.08 0.86 383.43 1.66Q385.78 2.46 388.03 3.49Q390.28 4.51 392.05 6.09Q393.83 7.66 394.90 9.86Q395.98 12.06 395.98 15.16L395.98 15.16Q395.98 18.86 394.63 21.99Q393.28 25.11 390.68 27.39Q388.08 29.66 384.25 30.94Q380.43 32.21 375.48 32.21L375.48 32.21Q372.83 32.21 370.30 31.74Q367.78 31.26 365.45 30.41Q363.13 29.56 361.15 28.41Q359.18 27.26 357.68 25.91L357.68 25.91L360.53 21.21Q361.08 20.36 361.83 19.91Q362.58 19.46 363.73 19.46L363.73 19.46Q364.88 19.46 365.90 20.11Q366.93 20.76 368.28 21.51Q369.63 22.26 371.45 22.91Q373.28 23.56 376.08 23.56L376.08 23.56Q378.28 23.56 379.85 23.04Q381.43 22.51 382.45 21.66Q383.48 20.81 383.95 19.69Q384.43 18.56 384.43 17.36L384.43 17.36Q384.43 15.56 383.35 14.41Q382.28 13.26 380.50 12.41Q378.73 11.56 376.45 10.89Q374.18 10.21 371.80 9.41Q369.43 8.61 367.15 7.54Q364.88 6.46 363.10 4.81Q361.33 3.16 360.25 0.76Q359.18-1.64 359.18-5.04L359.18-5.04Q359.18-8.19 360.43-11.04Q361.68-13.89 364.10-16.01Q366.53-18.14 370.15-19.41Q373.78-20.69 378.53-20.69L378.53-20.69Q383.83-20.69 388.18-18.94Q392.53-17.19 395.43-14.34L395.43-14.34ZM404.38-29.19L401.13-34.34Q402.83-35.84 404.85-37.19Q406.88-38.54 409.23-39.54Q411.58-40.54 414.33-41.11Q417.08-41.69 420.28-41.69L420.28-41.69Q424.63-41.69 428.20-40.49Q431.78-39.29 434.33-37.06Q436.88-34.84 438.28-31.69Q439.68-28.54 439.68-24.64L439.68-24.64Q439.68-20.84 438.58-18.06Q437.48-15.29 435.80-13.24Q434.13-11.19 432.18-9.69Q430.23-8.19 428.48-6.86Q426.73-5.54 425.45-4.26Q424.18-2.99 423.93-1.39L423.93-1.39L422.78 5.91L414.33 5.91L413.48-2.24Q413.43-2.49 413.43-2.66Q413.43-2.84 413.43-3.09L413.43-3.09Q413.43-5.29 414.53-6.91Q415.63-8.54 417.28-9.96Q418.93-11.39 420.83-12.74Q422.73-14.09 424.38-15.66Q426.03-17.24 427.13-19.24Q428.23-21.24 428.23-23.94L428.23-23.94Q428.23-25.74 427.55-27.16Q426.88-28.59 425.68-29.61Q424.48-30.64 422.80-31.19Q421.13-31.74 419.18-31.74L419.18-31.74Q416.33-31.74 414.35-31.11Q412.38-30.49 411.00-29.71Q409.63-28.94 408.68-28.31Q407.73-27.69 406.98-27.69L406.98-27.69Q405.18-27.69 404.38-29.19L404.38-29.19ZM410.58 24.61L410.58 24.61Q410.58 23.06 411.15 21.66Q411.73 20.26 412.75 19.26Q413.78 18.26 415.18 17.66Q416.58 17.06 418.18 17.06L418.18 17.06Q419.73 17.06 421.13 17.66Q422.53 18.26 423.53 19.26Q424.53 20.26 425.13 21.66Q425.73 23.06 425.73 24.61L425.73 24.61Q425.73 26.21 425.13 27.59Q424.53 28.96 423.53 29.96Q422.53 30.96 421.13 31.54Q419.73 32.11 418.18 32.11L418.18 32.11Q416.58 32.11 415.18 31.54Q413.78 30.96 412.75 29.96Q411.73 28.96 411.15 27.59Q410.58 26.21 410.58 24.61Z"/></g>
</svg>