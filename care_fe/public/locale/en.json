{"2FA_backup_code": "2FA Backup Code", "404_message": "This page doesn't exist or may have been moved. Please check the URL and try again.", "APPETITE__CANNOT_BE_ASSESSED": "Cannot be assessed", "APPETITE__INCREASED": "Increased", "APPETITE__NO_TASTE_FOR_FOOD": "No taste for food", "APPETITE__REDUCED": "Reduced", "APPETITE__SATISFACTORY": "Satisfactory", "APPROVED": "Approved", "AUTOMATED": "Automated", "BED_WITH_OXYGEN_SUPPORT": "Bed with Oxygen Support", "BLADDER_DRAINAGE__CONDOM_CATHETER": "<PERSON>dom Catheter", "BLADDER_DRAINAGE__CONTINUOUS_INDWELLING_CATHETER": "Continuous Indwelling Catheter", "BLADDER_DRAINAGE__CONTINUOUS_SUPRAPUBIC_CATHETER": "Continuous Suprapubic Catheter", "BLADDER_DRAINAGE__DIAPER": "Diaper", "BLADDER_DRAINAGE__INTERMITTENT_CATHETER": "Intermittent Catheter", "BLADDER_DRAINAGE__NORMAL": "Normal", "BLADDER_DRAINAGE__UROSTOMY": "Urostomy", "BLADDER_ISSUE__HESITANCY": "Hesitancy", "BLADDER_ISSUE__INCONTINENCE": "Incontinence", "BLADDER_ISSUE__NO_ISSUES": "No issues", "BLADDER_ISSUE__RETENTION": "Retention", "BLOOD_GROUP_LONG__AB_negative": "AB Negative", "BLOOD_GROUP_LONG__AB_positive": "AB Positive", "BLOOD_GROUP_LONG__A_negative": "A Negative", "BLOOD_GROUP_LONG__A_positive": "A Positive", "BLOOD_GROUP_LONG__B_negative": "B Negative", "BLOOD_GROUP_LONG__B_positive": "B Positive", "BLOOD_GROUP_LONG__O_negative": "O Negative", "BLOOD_GROUP_LONG__O_positive": "O Positive", "BLOOD_GROUP_LONG__unknown": "Unknown", "BOWEL_ISSUE__CONSTIPATION": "Constipation", "BOWEL_ISSUE__DIARRHOEA": "Diarr<PERSON>a", "BOWEL_ISSUE__NO_DIFFICULTY": "No difficulty", "CONSCIOUSNESS_LEVEL__AGITATED_OR_CONFUSED": "Agitated or Confused", "CONSCIOUSNESS_LEVEL__ALERT": "<PERSON><PERSON>", "CONSCIOUSNESS_LEVEL__ONSET_OF_AGITATION_AND_CONFUSION": "Onset of Agitation and Confusion", "CONSCIOUSNESS_LEVEL__RESPONDS_TO_PAIN": "Responds to Pain", "CONSCIOUSNESS_LEVEL__RESPONDS_TO_VOICE": "Responds to Voice", "CONSCIOUSNESS_LEVEL__UNRESPONSIVE": "Unresponsive", "Customize how this service appears in the UI": "Customize how this service appears in the UI", "DAYS_OF_WEEK_SHORT__0": "Mon", "DAYS_OF_WEEK_SHORT__1": "<PERSON><PERSON>", "DAYS_OF_WEEK_SHORT__2": "Wed", "DAYS_OF_WEEK_SHORT__3": "<PERSON>hu", "DAYS_OF_WEEK_SHORT__4": "<PERSON><PERSON>", "DAYS_OF_WEEK_SHORT__5": "Sat", "DAYS_OF_WEEK_SHORT__6": "Sun", "DAYS_OF_WEEK__0": "Monday", "DAYS_OF_WEEK__1": "Tuesday", "DAYS_OF_WEEK__2": "Wednesday", "DAYS_OF_WEEK__3": "Thursday", "DAYS_OF_WEEK__4": "Friday", "DAYS_OF_WEEK__5": "Saturday", "DAYS_OF_WEEK__6": "Sunday", "DD/MM/YYYY": "DD/MM/YYYY", "DOCTORS_LOG": "Progress Note", "DOMESTIC_HEALTHCARE_SUPPORT__FAMILY_MEMBER": "Family member", "DOMESTIC_HEALTHCARE_SUPPORT__NO_SUPPORT": "No support", "DOMESTIC_HEALTHCARE_SUPPORT__PAID_CAREGIVER": "Paid caregiver", "Diagnosis_chronic_condition__description": "Long-term diagnosis (e.g., Hypertension)", "Diagnosis_chronic_condition__title": "Chronic Condition", "Diagnosis_encounter_diagnosis__description": "Specific to this visit only", "Diagnosis_encounter_diagnosis__title": "Diagnosis", "ENCOUNTER_TAB__abdm": "ABDM Records", "ENCOUNTER_TAB__accounts": "Accounts", "ENCOUNTER_TAB__claims": "Insurance Claims", "ENCOUNTER_TAB__consents": "Consents", "ENCOUNTER_TAB__devices": "Devices", "ENCOUNTER_TAB__diagnostic_reports": "Diagnostic Reports", "ENCOUNTER_TAB__drawings": "Drawings", "ENCOUNTER_TAB__feed": "Feed", "ENCOUNTER_TAB__files": "Files", "ENCOUNTER_TAB__medicines": "Medicines", "ENCOUNTER_TAB__neurological_monitoring": "Neur<PERSON>", "ENCOUNTER_TAB__notes": "Notes", "ENCOUNTER_TAB__nursing": "Nursing", "ENCOUNTER_TAB__observations": "Observations", "ENCOUNTER_TAB__plots": "Plots", "ENCOUNTER_TAB__pressure_sore": "Pressure Sore", "ENCOUNTER_TAB__service_requests": "Service Requests", "ENCOUNTER_TAB__summary": "<PERSON><PERSON>", "ENCOUNTER_TAB__updates": "Overview", "GENDER__female": "Female", "GENDER__male": "Male", "GENDER__non_binary": "Non-binary", "GENDER__transgender": "Transgender", "HEARTBEAT_RHYTHM__IRREGULAR": "Irregular", "HEARTBEAT_RHYTHM__REGULAR": "Regular", "HEARTBEAT_RHYTHM__UNKNOWN": "Unknown", "ICU": "ICU", "INSULIN_INTAKE_FREQUENCY__BD": "Twice a day (BD)", "INSULIN_INTAKE_FREQUENCY__OD": "Once a day (OD)", "INSULIN_INTAKE_FREQUENCY__TD": "Thrice a day (TD)", "INSULIN_INTAKE_FREQUENCY__UNKNOWN": "Unknown", "ISOLATION": "Isolation", "LIMB_RESPONSE__EXTENSION": "Extension", "LIMB_RESPONSE__FLEXION": "Flexion", "LIMB_RESPONSE__MODERATE": "Moderate", "LIMB_RESPONSE__NONE": "None", "LIMB_RESPONSE__STRONG": "Strong", "LIMB_RESPONSE__UNKNOWN": "Unknown", "LIMB_RESPONSE__WEAK": "Weak", "LOG_UPDATE_CREATED_NOTIFICATION": "{{ roundType }} created successfully", "LOG_UPDATE_FIELD_LABEL__action": "Action", "LOG_UPDATE_FIELD_LABEL__appetite": "Appetite", "LOG_UPDATE_FIELD_LABEL__bladder_drainage": "Drainage", "LOG_UPDATE_FIELD_LABEL__bladder_issue": "Issues", "LOG_UPDATE_FIELD_LABEL__blood_sugar_level": "Blood Sugar Level", "LOG_UPDATE_FIELD_LABEL__bowel_issue": "Bowel", "LOG_UPDATE_FIELD_LABEL__bp": "Blood Pressure", "LOG_UPDATE_FIELD_LABEL__consciousness_level": "Level of Consciousness", "LOG_UPDATE_FIELD_LABEL__is_experiencing_dysuria": "Experiences Dysuria?", "LOG_UPDATE_FIELD_LABEL__nutrition_route": "Nutrition Route", "LOG_UPDATE_FIELD_LABEL__oral_issue": "Oral issues", "LOG_UPDATE_FIELD_LABEL__other_details": "Other details", "LOG_UPDATE_FIELD_LABEL__patient_category": "Category", "LOG_UPDATE_FIELD_LABEL__physical_examination_info": "Physical Examination Info", "LOG_UPDATE_FIELD_LABEL__pulse": "Pulse", "LOG_UPDATE_FIELD_LABEL__resp": "Respiratory Rate", "LOG_UPDATE_FIELD_LABEL__review_interval": "Review after", "LOG_UPDATE_FIELD_LABEL__rhythm": "Heartbeat Rhythm", "LOG_UPDATE_FIELD_LABEL__rhythm_detail": "Rhythm Description", "LOG_UPDATE_FIELD_LABEL__rounds_type": "Rounds Type", "LOG_UPDATE_FIELD_LABEL__sleep": "Sleep", "LOG_UPDATE_FIELD_LABEL__temperature": "Temperature", "LOG_UPDATE_FIELD_LABEL__urination_frequency": "Frequency of Urination", "LOG_UPDATE_FIELD_LABEL__ventilator_spo2": "SpO₂", "LOG_UPDATE_UPDATED_NOTIFICATION": "{{ roundType }} updated successfully", "NORMAL": "Brief Update", "NURSING_CARE_PROCEDURE__ascitic_tapping": "<PERSON><PERSON><PERSON>pping", "NURSING_CARE_PROCEDURE__bed_bath": "Bed Bath", "NURSING_CARE_PROCEDURE__catheter_care": "Catheter Care", "NURSING_CARE_PROCEDURE__catheter_change": "Catheter Change", "NURSING_CARE_PROCEDURE__chest_tube_care": "Chest Tube Care", "NURSING_CARE_PROCEDURE__colostomy_care": "Colostomy Care", "NURSING_CARE_PROCEDURE__colostomy_change": "Colostomy Change", "NURSING_CARE_PROCEDURE__dressing": "Dressing", "NURSING_CARE_PROCEDURE__dvt_pump_stocking": "DVT Pump Stocking", "NURSING_CARE_PROCEDURE__eye_care": "Eye Care", "NURSING_CARE_PROCEDURE__hair_care": "Hair Care", "NURSING_CARE_PROCEDURE__iv_sitecare": "IV Site Care", "NURSING_CARE_PROCEDURE__lymphedema_care": "Lymphedema Care", "NURSING_CARE_PROCEDURE__nubulisation": "Nubulisation", "NURSING_CARE_PROCEDURE__oral_care": "Oral Care", "NURSING_CARE_PROCEDURE__perineal_care": "Perineal Care", "NURSING_CARE_PROCEDURE__personal_hygiene": "Other Personal Hygiene", "NURSING_CARE_PROCEDURE__positioning": "Positioning", "NURSING_CARE_PROCEDURE__pre_enema": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NURSING_CARE_PROCEDURE__restrain": "Restrain", "NURSING_CARE_PROCEDURE__ryles_tube_care": "R<PERSON>'s Tube Care", "NURSING_CARE_PROCEDURE__ryles_tube_change": "<PERSON><PERSON>'s <PERSON>be Change", "NURSING_CARE_PROCEDURE__skin_care": "Skin Care", "NURSING_CARE_PROCEDURE__stoma_care": "Stoma Care", "NURSING_CARE_PROCEDURE__suctioning": "Suctioning", "NURSING_CARE_PROCEDURE__tracheostomy_care": "Tracheostomy Care", "NURSING_CARE_PROCEDURE__tracheostomy_tube_change": "Tracheostomy Tube Change", "NURSING_CARE_PROCEDURE__wound_dressing": "Wound Dressing", "NUTRITION_ROUTE__GASTROSTOMY_OR_JEJUNOSTOMY": "Gastrostomy / Jejunostomy", "NUTRITION_ROUTE__ORAL": "Oral", "NUTRITION_ROUTE__PARENTERAL_TUBING_FLUID": "<PERSON><PERSON><PERSON> (Fluid)", "NUTRITION_ROUTE__PARENTERAL_TUBING_TPN": "Pa<PERSON><PERSON> Tubing (TPN)", "NUTRITION_ROUTE__PEG": "PEG", "NUTRITION_ROUTE__RYLES_TUBE": "<PERSON><PERSON>'s <PERSON><PERSON>", "Notifications": "Notifications", "ORAL_ISSUE__DYSPHAGIA": "Dysphagia", "ORAL_ISSUE__NO_ISSUE": "No issues", "ORAL_ISSUE__ODYNOPHAGIA": "Odynophagia", "OXYGEN_MODALITY__HIGH_FLOW_NASAL_CANNULA": "High Flow Nasal Cannula", "OXYGEN_MODALITY__HIGH_FLOW_NASAL_CANNULA_short": "HFNC", "OXYGEN_MODALITY__NASAL_PRONGS": "Nasal <PERSON>ngs", "OXYGEN_MODALITY__NASAL_PRONGS_short": "NP", "OXYGEN_MODALITY__NON_REBREATHING_MASK": "Non Rebreathing Mask", "OXYGEN_MODALITY__NON_REBREATHING_MASK_short": "NRM", "OXYGEN_MODALITY__SIMPLE_FACE_MASK": "Simple Face Mask", "OXYGEN_MODALITY__SIMPLE_FACE_MASK_short": "SFM", "PENDING": "Pending", "PRESCRIPTION_FREQUENCY_BD": "Twice daily", "PRESCRIPTION_FREQUENCY_HS": "Night only", "PRESCRIPTION_FREQUENCY_OD": "Once daily", "PRESCRIPTION_FREQUENCY_Q4H": "4th hourly", "PRESCRIPTION_FREQUENCY_QID": "6th hourly", "PRESCRIPTION_FREQUENCY_QOD": "Alternate day", "PRESCRIPTION_FREQUENCY_QWK": "Once a week", "PRESCRIPTION_FREQUENCY_STAT": "Imediately", "PRESCRIPTION_FREQUENCY_TID": "8th hourly", "PRESCRIPTION_ROUTE_IM": "IM", "PRESCRIPTION_ROUTE_INHALATION": "Inhalation", "PRESCRIPTION_ROUTE_INTRATHECAL": "intrathecal injection", "PRESCRIPTION_ROUTE_IV": "IV", "PRESCRIPTION_ROUTE_NASOGASTRIC": "Nasogastric / Gastrostomy tube", "PRESCRIPTION_ROUTE_ORAL": "Oral", "PRESCRIPTION_ROUTE_RECTAL": "Rectal", "PRESCRIPTION_ROUTE_SC": "S/C", "PRESCRIPTION_ROUTE_SUBLINGUAL": "Sublingual", "PRESCRIPTION_ROUTE_TRANSDERMAL": "Transdermal", "PRINTABLE_QR_CODE__print_button": "Print QR code", "PRINTABLE_QR_CODE__print_error": "Failed to open print window. Please check your popup blocker.", "PRINTABLE_QR_CODE__print_title": "QR Code - {{title}}", "PUPIL_REACTION__CANNOT_BE_ASSESSED": "Cannot be assessed", "PUPIL_REACTION__FIXED": "Fixed", "PUPIL_REACTION__SLUGGISH": "Sluggish", "PUPIL_REACTION__UNKNOWN": "Unknown", "REGULAR": "Regular", "RESPIRATORY_SUPPORT_SHORT__INVASIVE": "IV", "RESPIRATORY_SUPPORT_SHORT__NON_INVASIVE": "NIV", "RESPIRATORY_SUPPORT_SHORT__OXYGEN_SUPPORT": "O₂ Support", "RESPIRATORY_SUPPORT_SHORT__UNKNOWN": "None", "RESPIRATORY_SUPPORT__INVASIVE": "Invasive ventilator (IV)", "RESPIRATORY_SUPPORT__NON_INVASIVE": "Non-Invasive ventilator (NIV)", "RESPIRATORY_SUPPORT__OXYGEN_SUPPORT": "Oxygen Support", "RESPIRATORY_SUPPORT__UNKNOWN": "None", "ROUNDS_TYPE__AUTOMATED": "Virtual Nursing Assistant", "ROUNDS_TYPE__COMMUNITY_NURSES_LOG": "Community Nurse's Log", "ROUNDS_TYPE__DOCTORS_LOG": "Progress Note", "ROUNDS_TYPE__NORMAL": "Brief Update", "ROUNDS_TYPE__TELEMEDICINE": "Tele-medicine Log", "ROUNDS_TYPE__VENTILATOR": "Detailed Update", "SCHEDULE_AVAILABILITY_TYPE_DESCRIPTION__appointment": "Patients can be booked for slots in this session", "SCHEDULE_AVAILABILITY_TYPE_DESCRIPTION__closed": "Indicates the practitioner is not available for this session's time", "SCHEDULE_AVAILABILITY_TYPE_DESCRIPTION__open": "Indicates the practitioner is available in this session", "SCHEDULE_AVAILABILITY_TYPE__appointment": "Appointment", "SCHEDULE_AVAILABILITY_TYPE__closed": "Closed", "SCHEDULE_AVAILABILITY_TYPE__open": "Open", "SCHEDULE_EXCEPTION_TYPE__MODIFY_SCHEDULE": "Modify Schedule", "SCHEDULE_EXCEPTION_TYPE__UNAVAILABLE": "Unavailable", "SLEEP__EXCESSIVE": "Excessive", "SLEEP__NO_SLEEP": "No sleep", "SLEEP__SATISFACTORY": "Satisfactory", "SLEEP__UNSATISFACTORY": "Unsatisfactory", "SOCIOECONOMIC_STATUS__MIDDLE_CLASS": "Middle Class", "SOCIOECONOMIC_STATUS__POOR": "Poor", "SOCIOECONOMIC_STATUS__VERY_POOR": "Very Poor", "SOCIOECONOMIC_STATUS__WELL_OFF": "Well Off", "SORT_OPTIONS__-bed__name": "Bed No. N-1", "SORT_OPTIONS__-category_severity": "Highest Severity category first", "SORT_OPTIONS__-created_date": "Latest created date first", "SORT_OPTIONS__-modified_date": "Latest updated date first", "SORT_OPTIONS__-name": "Patient name Z-<PERSON>", "SORT_OPTIONS__-review_time": "Latest review date first", "SORT_OPTIONS__-taken_at": "Latest taken date first", "SORT_OPTIONS__bed__name": "Bed No. 1-N", "SORT_OPTIONS__category_severity": "Lowest Severity category first", "SORT_OPTIONS__created_date": "Oldest created date first", "SORT_OPTIONS__facility__name,-last_consultation__current_bed__bed__name": "Bed No. N-1", "SORT_OPTIONS__facility__name,last_consultation__current_bed__bed__name": "Bed No. 1-N", "SORT_OPTIONS__modified_date": "Oldest updated date first", "SORT_OPTIONS__name": "Patient name A-Z", "SORT_OPTIONS__review_time": "Oldest review date first", "SORT_OPTIONS__taken_at": "Oldest taken date first", "SPO2_LEVEL_MILD_HYPOXEMIA": "Mild Hypoxemia", "SPO2_LEVEL_MODERATE_HYPOXEMIA": "Moderate Hypoxemia", "SPO2_LEVEL_NORMAL": "Normal", "SPO2_LEVEL_SEVERE_HYPOXEMIA": "Severe Hypoxemia", "SRD__action": "Action", "SRD__action_cancelled": "Request Cancelled,", "SRD__action_delivered": "Delivered", "SRD__action_draft": "Draft Request,", "SRD__action_entered_in_error": "Request entered in error", "SRD__action_send": "Send", "SRD__action_sent": "<PERSON><PERSON>", "SRD__action_suspended": "Request Suspended,", "SRD__add_item": "Add Item", "SRD__confirm_dispatch": "Confirm Dispatch", "SRD__device": "<PERSON><PERSON>", "SRD__dispatch_details": "Dispatch Details", "SRD__dispatch_item": "Dispatch Item", "SRD__dispatch_quantity": "Dispatch Quantity", "SRD__dispatched_items": "Dispatched Items", "SRD__dispatched_to": "Dispatched To", "SRD__fulfill_request_confirmation_message": "You have dispatched enough items to fulfill the request. Do you want to mark it as fully dispatched and proceed?", "SRD__fulfill_request_title": "Fulfill Request?", "SRD__item_to_dispatch": "Item to dispatch", "SRD__item_type": "Item Type:", "SRD__lot_template": "(Lot #{{lotNumber}})", "SRD__mark_and_proceed": "Mark as fulfilled and proceed", "SRD__marked_as_fully_dispatched": "Marked as <PERSON><PERSON>ed", "SRD__marked_as_fully_dispatched_description": "Tick if all items have been dispatched. This delivery will be marked fulfilled and removed from the pending dispatch list.", "SRD__page_description_end": "Review and fill in the dispatch details below.", "SRD__page_description_template": "{{action}} items from {{from}} to {{to}} as per the request.", "SRD__page_title": "Dispatch Items", "SRD__proceed_without_marking": "Proceed without marking", "SRD__product": "Product", "SRD__qty_to_dispatch": "<PERSON>ty to Dispatch", "SRD__select_and_dispatch_items": "Select and Dispatch Items", "SRD__select_item_from_lot": "Select Item from Lot", "SRD__select_product_placeholder": "Select product", "SYSTEM__govt_org_type__block_panchayat": "Block Panchayat", "SYSTEM__govt_org_type__corporation": "Corporation", "SYSTEM__govt_org_type__default": "State", "SYSTEM__govt_org_type__district": "District", "SYSTEM__govt_org_type__district_panchayat": "District Panchayat", "SYSTEM__govt_org_type__grama_panchayat": "Grama Panchayat", "SYSTEM__govt_org_type__local_body": "Local Body", "SYSTEM__govt_org_type__municipality": "Municipality", "SYSTEM__govt_org_type__other_local_body": "Local Body", "SYSTEM__govt_org_type__state": "State", "SYSTEM__govt_org_type__ward": "Ward", "SYSTEM__org_type__default": "Organization", "SYSTEM__org_type__govt": "Government", "SYSTEM__org_type__role": "Role", "SYSTEM__org_type__team": "Team", "Select the locations where this service is available": "Select the locations where this service is available", "Specify the requirements for this activity": "Specify the requirements for this activity", "Submit": "Submit", "TELEMEDICINE": "Telemedicine", "TRANSPORTATION_TO_BE_ARRANGED": "Transportation", "URINATION_FREQUENCY__DECREASED": "Decreased", "URINATION_FREQUENCY__INCREASED": "Increased", "URINATION_FREQUENCY__NORMAL": "Normal", "USERMANAGEMENT_TAB__AVAILABILITY": "Availability", "USERMANAGEMENT_TAB__FACILITIES": "Linked Facilities", "USERMANAGEMENT_TAB__PROFILE": "User Information", "USERMANAGEMENT_TAB__SKILLS": "Linked Skills", "VENTILATOR": "Detailed Update", "VENTILATOR_MODE__CMV": "Control Mechanical Ventilation (CMV)", "VENTILATOR_MODE__CMV_short": "CMV", "VENTILATOR_MODE__PCV": "Pressure Control Ventilation (PCV)", "VENTILATOR_MODE__PCV_short": "PCV", "VENTILATOR_MODE__PC_SIMV": "Pressure Controlled SIMV (PC-SIMV)", "VENTILATOR_MODE__PC_SIMV_short": "PC-SIMV", "VENTILATOR_MODE__PSV": "C-PAP / Pressure Support Ventilation (PSV)", "VENTILATOR_MODE__PSV_short": "C-PAP/PSV", "VENTILATOR_MODE__SIMV": "Synchronised Intermittent Mandatory Ventilation (SIMV)", "VENTILATOR_MODE__SIMV_short": "SIMV", "VENTILATOR_MODE__VCV": "Volume Control Ventilation (VCV)", "VENTILATOR_MODE__VCV_short": "VCV", "VENTILATOR_MODE__VC_SIMV": "Volume Controlled SIMV (VC-SIMV)", "VENTILATOR_MODE__VC_SIMV_short": "VC-SIMV", "View_Facility": "View Facility", "a_new_version_of_care_is_available": "A new version of CARE is available", "aadhaar_number": "<PERSON><PERSON><PERSON><PERSON>", "aadhaar_number_will_not_be_stored": "<PERSON><PERSON><PERSON><PERSON> number will not be stored by CARE", "aadhaar_otp_send_error": "Failed to send <PERSON><PERSON>. Please try again later.", "aadhaar_otp_send_success": "OTP has been sent to the mobile number registered with the A<PERSON>har number.", "aadhaar_validation_length_error": "Should be a 12-digit aadhaar number or 16-digit virtual ID", "aadhaar_validation_space_error": "<PERSON><PERSON><PERSON><PERSON> number should not contain spaces", "abandoned": "Abandoned", "abnormal": "Abnormal", "aborted": "Aborted", "absolute_date": "Absolute Date", "accept": "Accept", "accept_all": "Accept All", "access_level": "Access Level", "account": "Account", "account_closed_failed": "Failed to close account", "account_closed_successfully": "Account closed successfully", "account_details": "Account Details", "account_id": "Account ID", "account_information": "Account Information", "account_management": "Account Management", "account_may_not_exist": "The account you are looking for may not exist", "account_not_found": "Account not found", "account_rebalance_failed": "Failed to rebalance account", "account_rebalanced_successfully": "Account rebalanced successfully", "account_status": "Account Status", "account_summary": "Account Summary", "accounts": "Accounts", "accurate_diagnostic_tests": "Accurate diagnostic tests across departments.", "action": "Action", "action_irreversible": "This action is irreversible", "actions": "Actions", "activate_this_request": "activate this request", "active": "Active", "active_encounters": "Active Encounters", "active_files": "Active Files", "active_location_cannot_be_in_future": "Active location cannot be in the future", "active_prescriptions": "Active Prescriptions", "activity": "Activity", "activity_definition": "Activity Definition", "activity_definition_created_successfully": "Activity definition created successfully", "activity_definition_updated_successfully": "Activity definition updated successfully", "activity_definitions": "Activity Definitions", "add": "Add", "add_activity_definition": "Add Activity Definition", "add_allergy_one": "Add Allergy", "add_allergy_other": "Add another Allergy", "add_another": "Add Another", "add_another_diagnosis": "Add another Diagnosis", "add_another_item": "Add Another Item", "add_another_medication": "Add another Medication", "add_another_result": "Add Another Result", "add_another_session": "Add another session", "add_another_symptom": "Add another Symptom", "add_as": "Add as", "add_attachments": "Add Attachments", "add_beds": "Add Bed(s)", "add_beds_to_configure_presets": "Add beds to this location to configure presets for them.", "add_care_team_members": "Add Care Team Members", "add_charge_item": "Add Charge Item", "add_charge_items": "Add Charge Items", "add_charge_items_invoice": "Add Charge Items to Invoice", "add_child_tag": "Add child tag", "add_child_tags_to_organize_better": "Add child tags to organize better", "add_coding": "Add Coding", "add_column": "Add Column", "add_component": "Add component", "add_concept": "Add Concept", "add_condition": "Add Condition", "add_consent": "Add Consent", "add_consent_description": "Record the patient's consent details and attach supporting documents.", "add_consultation": "Add consultation", "add_consultation_update": "Add Consultation Update", "add_contact_point": "Add Contact Point", "add_datetime": "Add Date/Time", "add_definition": "Add Definition", "add_department_team": "Add Department/Team", "add_details_of_patient": "Add Details of Patient", "add_device": "Add <PERSON>", "add_device_with_type": "Add {{type}} Device", "add_diagnosis": "Add Diagnosis", "add_diagnosis_one": "Add Diagnosis", "add_diagnosis_other": "Add another Diagnosis", "add_discount": "Add discount", "add_drawings": "Add Drawings", "add_element": "Add element", "add_encounter": "Add Encounter", "add_exception": "Add Exception", "add_facility": "Add Facility", "add_field": "Add Field", "add_file": "Add File", "add_files": "Add Files", "add_filter": "Add Filter", "add_guideline": "Add Guideline", "add_healthcare_service": "Add Healthcare Service", "add_image": "Add Image", "add_insurance_details": "Add Insurance Details", "add_item": "Add Item", "add_items": "Add items", "add_location": "Add Location", "add_location_description": "Create a Location such as Rooms/Beds", "add_medication": "Add Medication", "add_medication_one": "Add Medication", "add_medication_other": "Add another Medication", "add_member": "Add Member", "add_more_instructions": "Add more instructions", "add_name": "Add Name", "add_new_beds": "Add New Bed(s)", "add_new_facility": "Add New Facility", "add_new_location": "Add New Location", "add_new_medications": "Add New Medications", "add_new_patient": "Add New Patient", "add_new_user": "Add New User", "add_notes": "Add notes", "add_notes_about_symptom": "Add notes about the symptom...", "add_notes_about_the_allergy": "Add notes about the allergy", "add_observation_definition": "Add Definition", "add_option": "Add Option", "add_organization": "Add Organization", "add_organization_one": "Add Organization", "add_organization_other": "Add Organizations", "add_organizations": "Add organizations", "add_patient_identifier_config": "Add patient identifier config", "add_patient_updates": "Add Patient Updates", "add_policy": "Add Insurance Policy", "add_prescription_medication": "Add Prescription Medication", "add_prescription_to_consultation_note": "Add a new prescription to this consultation.", "add_preset": "Add preset", "add_price_component": "Add Price Component", "add_price_component_instruction": "Click the Add Price Component button to add pricing information", "add_prn_prescription": "Add PRN Prescription", "add_product": "Add Product", "add_product_alternative name": "Add alternative names for this product", "add_product_knowledge": "Add Product Knowledge", "add_products_to_receive": "Add products to receive", "add_question": "Add Question", "add_questionnaire": "Add Questionnaire", "add_remarks": "Add remarks", "add_remarks_if_quantity_is_less_or_items_are_damaged": "Add remarks if quantity is less or items are damaged", "add_role": "Add Role", "add_route": "Add Route", "add_row": "Add Row", "add_rule": "Add Rule", "add_schedule_exceptions": "Add Schedule Exceptions", "add_schedule_exceptions_description": "Configure absences by adding unavailability beyond the regular schedule.", "add_section": "Add Section", "add_selected": "Add Selected", "add_selected_items": "Add Selected Items", "add_service_request": "Add Service Request", "add_skill": "<PERSON><PERSON>", "add_spoke": "Add Spoke Facility", "add_sub_question": "Add Sub-Question", "add_symptom": "Add Symptom", "add_symptom_one": "Add Symptom", "add_symptom_other": "Add another Symptom", "add_tag_config": "Add tag config", "add_tags": "Add Tags", "add_tax": "Add tax", "add_text": "Add Text", "add_to_list": "Add to list", "add_to_organization": "Add to Organization", "add_unit": "Add a Unit", "add_user": "Add User", "add_your_first_component": "Add your first component", "added_on": "Added on", "additional_details": "Additional Details", "additional_information": "Additional Information", "additional_instructions": "Additional Instructions", "additional_item_other": "Additional Items", "additional_item_zero": "Additional Item", "additional_notes": "Enter additional notes", "additional_payment_notes": "Add any notes about this payment...", "address": "Address", "address_is_required": "Address is required", "adjust_account_filters": "Try adjusting your account filters", "adjust_activity_definition_filters": "Adjust activity definition filters to find what you're looking for", "adjust_appointments_filters": "Try adjusting your filters or create a new appointment", "adjust_config_filters": "Adjust config filters", "adjust_crop_area_for_captured_image": "Adjust crop area for captured image", "adjust_filters": "Try adjusting your filters", "adjust_healthcare_service_filters": "Try adjusting your filters or create a new healthcare service", "adjust_invoice_filters": "Adjust invoice filters", "adjust_observation_definition_filters": "Try adjusting your filters", "adjust_product_filters": "Try adjusting your filters or adding a new product", "adjust_questionnaire_filters": "Try adjusting your filters or create a new questionnaire", "adjust_resource_filters": "Try adjusting your filters or create a new resource", "adjust_role_filters": "Adjust role filters", "adjust_service_request_filters": "Try adjusting your filters or search term", "adjust_tag_config_filters": "Adjust tag config filters", "adjust_valueset_filters": "Try adjusting your filters or create a new valueset", "adjustment": "Adjustment", "admin_dashboard": "Admin Dashboard", "administer": "Administer", "administer_medicine": "Administer Medicine", "administer_medicines": "Administer Medicines", "administer_selected_medicines": "Administer Selected Medicines", "administered_at": "Administered at", "administered_at_site": "at {{site}}", "administered_by": "Administered by", "administered_on": "Administered on", "administered_through_route": "through {{route}}", "administered_via_method": "via {{method}}", "administration_dosage_range_error": "Dosage should be between start and target dosage", "administration_notes": "Administration Notes", "administrator": "Administrator", "admission_source": "Admission Source", "admit_source": "Admit Source", "admitted": "Admitted", "admitted_on": "Admitted On", "advance": "Advance", "advance_directive": "Advance Directive", "advanced_filters": "Advanced Filters", "after": "after", "after_adjustments": "After Adjustments", "age": "Age", "age_input_warning": "While entering a patient's age is an option, please note that only the year of birth will be captured from this information.", "age_input_warning_bold": "Recommended only when the patient's date of birth is unknown", "age_less_than_0": "Age cannot be less than 0", "age_must_be_below_120": "Age must be below 120", "age_must_be_positive": "Age must be greater than 0", "age_must_be_present": "Age must be present", "age_notice": "Only year of birth will be stored for age.", "ago": "ago", "alias": "<PERSON><PERSON>", "align_center": "Center", "align_left": "Left", "align_qr_code_in_frame": "Align the QR code within the frame", "align_right": "Right", "alignment": "Alignment", "all": "All", "all_accounts": "All Accounts", "all_billing_statuses": "All Billing Statuses", "all_categories": "All Categories", "all_changes_have_been_saved": "All changes have been saved", "all_details": "All Details", "all_existing_data_will_be_replaced": "All existing data will be replaced", "all_given": "All Given", "all_items_dispatched_successfully": "All items dispatched successfully", "all_organizations": "All Organizations", "all_patients": "All Patients", "all_prescriptions": "All ℞", "all_priorities": "All Priorities", "all_status": "All Status", "all_statuses": "All statuses", "all_stock": "All Stock", "all_type": "All Type", "all_types": "All Types", "allergen": "Allergen", "allergies": "Allergies", "allergies_empty_message": "No allergies recorded", "allergy_history": "Allergy History", "allow_camera_access": "Please allow camera permissions in your browser", "allow_transfer": "Allow Transfer", "allowed_formats_are": "Allowed formats are {{formats}}.", "already_a_member": "Already a member?", "already_marked_as_error": "Already marked as error", "already_selected": "Already selected", "alt": "Alt.", "alternate": "Alternate", "alternate_phone_number": "Alternate Phone Number", "alternative_names": "Alternative Names", "alternatives_coming_soon": "Alternatives coming soon", "ambulance_driver_name": "Name of ambulance driver", "ambulance_number": "Ambulance No", "ambulance_phone_number": "Phone number of Ambulance", "amount": "Amount", "amount_due": "Amount Due", "amount_given_by_customer": "Amount given by the customer", "amount_tendered": "Amount Tendered", "and_the_status_of_request_is": "and the status of request is", "answer": "Answer", "answer_options": "Answer options", "answer_options_description": "Define possible answers for this question", "antenatal": "Antenatal", "any_additional_information": "Any Additional Information", "any_other_comments": "Any other comments", "app_settings": "App Settings", "application_successfully_updated": "Application successfully updated!", "applied_last_in_calculation": "Applied last in the calculation", "applies_at_end": "Applies at end", "applies_before_tax": "Applies before tax", "applies_before_taxes": "Applied before taxes", "apply": "Apply", "appointment_booking_success": "Your appointment has been successfully booked!", "appointment_cancelled": "Appointment has been cancelled!", "appointment_created_success": "Appointment created successfully", "appointment_details": "Appointment Details", "appointment_not_found": "Appointment not found", "appointment_note": "Enter a note for this appointment", "appointment_rescheduled": "Appointment has been rescheduled!", "appointment_rescheduled_successfully": "Appointment rescheduled successfully", "appointment_slot": "Appointment Slot", "appointment_type": "Appointment Type", "appointments": "Appointments", "appointments_scheduled_for_day_link_one": "View {{count}} scheduled appointment", "appointments_scheduled_for_day_link_other": "View {{count}} scheduled appointments", "approve": "Approve", "approve_external_supply_deliveries": "Approve External Supply Deliveries", "approved": "Approved", "approved_by_district_covid_control_room": "Approved by District COVID Control Room", "approving_facility": "Name of Approving Facility", "archive": "Archive", "archive_child_tag": "Archive Child Tag", "archive_child_tag_confirmation": "Are you sure you want to archive {{name}} child tag? This action cannot be undone.", "archive_file": "Archive File", "archived": "Archived", "archived_at": "Archived at", "archived_by": "Archived by", "archived_file": "Archived file", "archived_files": "Archived Files", "archived_reason": "Archived reason", "are_non_editable_fields": "are non-editable fields", "are_you_still_watching": "Are you still watching?", "are_you_sure": "Are you sure?", "are_you_sure_cancel_planned_bed": "Are you sure you want to cancel the planned bed assignment?", "are_you_sure_clear_starred": "Are you sure you want to clear all starred?", "are_you_sure_mark_as_error_active_bed": "Are you sure you want to mark the active bed assignment as an error?", "are_you_sure_want_to_cancel_invoice": "Are you sure you want to mark this invoice as cancelled?", "are_you_sure_want_to_delete": "Are you sure you want to delete {{name}}?", "are_you_sure_want_to_delete_this_record": "Are you sure want to delete this record?", "are_you_sure_want_to_mark_as_balanced": "Are you sure you want to mark this invoice as balanced?", "are_you_sure_want_to_mark_as_error": "Are you sure you want to mark this invoice as entered in error?", "are_you_sure_want_to_remove": "Are you sure you want to remove <strong>{{name}}</strong> from the patient? This action cannot be undone", "are_you_sure_you_want_to_delete_user": "Are you sure you want to delete this user: ", "are_you_sure_you_want_to_mark_as_received": "Are you sure you want to mark as received?", "ari": "ARI - Acute Respiratory illness", "arithmetic_help": "You can start with any number and use other characters. For example, you might begin from a specific value like 100.", "arrived": "Arrived", "as_needed_prn": "As Needed / PRN", "asap": "ASAP", "asset_class": "Asset Class", "asset_location": "Asset Location", "asset_name": "Asset Name", "asset_not_found_msg": "Oops! The asset you are looking for does not exist. Please check the asset id.", "asset_qr_id": "Asset QR ID", "asset_type": "Asset Type", "assets": "Assets", "assign": "Assign", "assign_a_volunteer_to": "Assign a volunteer to {{name}}", "assign_bed": "Assign Bed", "assign_bed_now": "Assign Bed Now", "assign_location": "Assign Location", "assign_to_patient": "Assign to <PERSON><PERSON>", "assign_to_volunteer": "Assign to a Volunteer", "assign_user": "Assign User", "assign_user_to_patient": "Assign User to Patient", "assigned_doctor": "Assigned Doctor", "assigned_facility": "Facility assigned", "assigned_to": "Assigned to", "assigned_volunteer": "Assigned Volunteer", "associate": "Associate", "associate_device": "Associate device", "associate_device_confirmation": "This device is currently linked to another encounter. Proceeding with this association will unlink it from the previous encounter.", "associate_device_description": "Select a device to associate with this encounter", "associate_device_to_encounter": "Associate a device to this encounter", "associate_location": "Associate Location", "associate_location_description": "Select a location to associate with this device", "associated_by": "Associated by {{name}}", "associating": "Associating...", "association_end_date": "Association end date", "association_start_date": "Association start date", "at_least_one_department_is_required": "At least one department is required", "at_least_one_item_required": "At least one item is required. Please add one item or remove the row.", "at_least_one_price_component_required": "At least one price component is required", "at_least_one_result": "At least one result value or conclusion must be provided", "at_time": "at <strong>{{time}}</strong>", "attach_files_to_consent_description": "Attach supporting documents to the consent.", "atypical_presentation_details": "Atypical presentation details", "audio__allow_permission": "Please allow microphone permission in site settings", "audio__allow_permission_button": "Click here to know how to allow", "audio__allow_permission_helper": "You might have denied microphone access in the past.", "audio__permission_message": "Please grant microphone permission to record audio.", "audio__record": "Record Audio", "audio__record_helper": "Click the button to start recording", "audio__recorded": "Audio Recorded", "audio__recording": "Recording", "audio__recording_helper": "Please speak into your microphone.", "audio__recording_helper_2": "Click on the button to stop recording.", "audio__start_again": "Start Again", "audit_information": "Audit Information", "audit_log": "<PERSON>t Log", "auth_login_title": "Authorized Login", "auth_method_unsupported": "This authentication method is not supported, please try a different method", "authenticate_your_account": "Authenticate Your Account", "authored_on": "Authored On", "authorization": "Authorization", "authorize_shift_delete": "Authorize shift delete", "auto_fill_slot_duration": "Auto-fill slot duration", "auto_generated_for_care": "Auto Generated for Care", "autofilled_fields": "Autofilled Fields", "availabilities": "Availabilities", "availability": "Availability", "availability_status": "Availability Status", "available": "Available", "available_at_count_locations": "Available at {{count}} locations", "available_beds": "Available beds", "available_features": "Available Features", "available_in": "Available in", "available_locations": "Available Locations", "available_reports": "Available Reports", "available_selected": "Available Selected", "available_templates": "Available Templates", "available_time_slots": "Available Time Slots", "avatar_gif_not_allowed": "GIFs are not allowed as avatars.", "avatar_updated_success": "Avatar updated successfully", "average_weekly_working_hours": "Average weekly working hours", "awaiting_destination_approval": "AWAITING DESTINATION APPROVAL", "back": "Back", "back_dated_encounter_date_caution": "You are creating an encounter for", "back_to_account": "Back to Account", "back_to_accounts": "Back to accounts", "back_to_consultation": "Go back to Consultation", "back_to_dispense_queue": "Back to Dispense Queue", "back_to_encounter": "Back to encounter", "back_to_facilities": "Back to Facilities", "back_to_home": "Back to Home", "back_to_invoices": "Back to invoices", "back_to_list": "Back to List", "back_to_login": "Back to login", "back_to_payments": "Back to Payments", "back_to_prescription_queue": "Back to Prescription Queue", "back_to_requests": "Back to Requests", "back_to_services": "Back to Services", "back_to_tags": "Back to tags", "backup_codes_copied": "Backup codes copied to clipboard", "backup_codes_description": "You can use Backup codes as a second factor to authenticate in case you loose access to your device. Each backup code can be used only once. We recommend saving them with a secure password manager.", "backup_codes_warning": "Your old backup codes will no longer work.", "balance": "Balance", "balanced": "Balanced", "barcode_number": "Barcode Number", "barcode_scanned_successfully": "Barcode Scanned Successfully", "base_amount": "Base Amount", "base_dosage": "Dosage", "base_price": "Base Price", "base_price_explanation": "The starting point for pricing calculation", "basic_details_of_the_activity": "Enter the basic details of the activity", "basic_details_of_the_healthcare_service": "Enter the basic details of the healthcare service", "basic_info": "Basic Information", "basic_information": "Basic Information", "basic_settings": "Basic Settings", "batch": "<PERSON><PERSON>", "bed_active_removed_due_to_error": "Active bed assignment has been marked as an error and removed successfully.", "bed_assigned_successfully": "Bed Assigned Successfully", "bed_available_soon_discharged_message": "The patient in this bed is being discharged. This bed will be available soon.", "bed_capacity": "Bed Capacity", "bed_count_validation_error": "Number of beds must be greater than 0", "bed_created_notification_one": "{{count}} Bed created successfully", "bed_created_notification_other": "{{count}} Beds created successfully", "bed_history": "Bed History", "bed_name_placeholder": "Name of Bed no. {{number}}", "bed_not_linked_to_camera": "This bed has not been linked to this camera.", "bed_number": "Bed Number {{number}}", "bed_occupied": "Bed Occupied", "bed_planned_cancelled": "Planned bed assignment has been cancelled.", "bed_requires_parent_location": "Beds can only be created under a parent location", "bed_search_placeholder": "Search by beds name", "bed_type": "Bed Type", "bed_type__100": "ICU Bed", "bed_type__200": "Ordinary Bed", "bed_type__300": "Oxygen Supported Bed", "bed_type__400": "Isolation Bed", "bed_type__500": "Others", "bed_unavailable_message": "This bed is currently occupied. We are unsure when it'll become available.", "beds": "Beds", "beds_creation_failed": "Failed to create beds", "before": "before", "begin_clinical_encounter": "Begin a new clinical encounter for {{patientName}}. Select the appropriate encounter type, status, and priority to ensure proper documentation and care delivery.", "beta": "beta", "bill_medication": "Bill Medication", "bill_medications": "Bill Me<PERSON>s", "bill_selected": "<PERSON>", "bill_to": "Bill <PERSON>", "billable": "Billable", "billable_charge_items": "Billable Charge Items", "billable_items_for_account": "Billing Items for Account", "billed": "Billed", "billed_gross": "Billed (Gross)", "billing": "Billing", "billing_delete_discount_code_confirmation": "Are you sure you want to delete this discount code? This action cannot be undone.", "billing_delete_discount_component_confirmation": "Are you sure you want to delete this discount component from this facility?", "billing_information": "Billing Information", "billing_pending": "Billing Pending", "billing_statements": "Billing Statements", "billing_status": "Billing Status", "billing_status_inactive_warning": "This will set the account status as inactive.", "biologic": "Biologic", "bladder": "<PERSON><PERSON><PERSON>", "blood_group": "Blood Group", "blood_group_is_required": "Blood group is required", "blood_pressure_error": {"missing": "Field is required. Either specify both or clear both.", "exceed": "Value cannot exceed 250 mmHg.", "systolic_less_than_diastolic": "Systolic must be greater than diastolic."}, "blood_pressure_systolic_diastolic": "Blood Pressure (Systolic + Diastolic)", "board": "Board", "board_view": "Board View", "body_site": "Body Site", "book_a_new_appointment": "Book a new appointment", "book_an_appointment_with": "Book an appointment with", "book_appointment": "Book Appointment", "booked": "Booked", "booked_by": "Booked by", "boolean": "Boolean", "bottom": "Bottom", "bradycardia": "Bradycardia", "breathe": "Breathe", "breathlessness_level": "Breathlessness level", "built_in": "Built-in", "bulk_bed_creation_info": "You are about to create multiple beds. Each bed will have a unique name based on the beds names you provided.", "by": "by", "by_name": "filed by <strong>{{by}}</strong>", "calculation": "Calculation", "calculation_preview": "Calculation Preview", "camera": "Camera", "camera_bed_link_success": "Camera linked to bed successfully.", "camera_permission_denied": "Camera permission denied", "camera_was_linked_to_bed": "This camera was linked to this bed", "can_be_factor_of_reference_value": "Can be a factor of a reference value", "can_be_fixed_amount": "Can be a fixed amount", "cancel": "Cancel", "cancel_appointment": "<PERSON>cel Appointment", "cancel_appointment_warning": "This action cannot be undone. The appointment will be cancelled and the patient will be notified.", "cancel_plan": "Cancel Plan", "cancel_request": "Cancel Request", "cancel_this_request": "cancel this request", "cancelled": "Cancelled", "cannot_add_conclusion_without_results": "Cannot add conclusion without results", "cannot_close_account_with_pending_items": "You cannot close an account with pending charge items.", "cannot_complete_unpaid_medication": "Cannot complete unpaid medication. Please ensure payment is completed first.", "cannot_delete_organization_with_children": "Cannot delete an organization with children", "cannot_go_before_prescription_date": "Cannot view slots before the earliest prescription date", "cannot_select_date_out_of_range": "Cannot select date out of range", "cannot_select_month_out_of_range": "Cannot select month out of range", "cannot_select_year_out_of_range": "Cannot select year out of range", "cant_access_code": "Can't access your code?", "cant_scan_copy_key": "Can't scan? <strong>Copy setup key</strong> <CareIcon /> to add it to your authenticator app.", "cap": "Cap", "capacity": "Capacity", "capture": "Capture", "card": "Card", "cardiology": "Cardiology", "care": "CARE", "care_backend": "Care Backend", "care_frontend": "Care Frontend", "care_icons": "Care Icons", "care_team": "Care Team", "carecomplete_notbilled": "Care Complete Not Billed", "cash": "Cash", "cash_transaction_details": "Cash Transaction Details", "category": "Category", "category_description": "Choose the category  ", "caution": "Caution", "center": "Center", "central": "Central", "central_nursing_station": "Central Nursing Station", "cgst": "CGST", "change": "Change", "change_avatar": "Change Avatar", "change_avatar_note": "JPG or PNG. {{maxSize}}MB max.", "change_file": "Change File", "change_location": "Change Location", "change_phone_number": "Change Phone Number", "change_returned": "Change Returned", "change_status": "Change Status", "change_to_return": "Change to Return", "character_count_validation": "Must be atleast {{min}} characters and atmost {{max}} characters", "charge_item": "Charge Item", "charge_item_definition": "Charge Item Definition", "charge_item_definition_description": "Billing information for this product", "charge_item_definition_not_found": "Charge Item Definition Not Found", "charge_item_definitions": "Charge Item Definitions", "charge_item_definitions_description": "Charge Items that are required to be billed", "charge_item_details": "Charge Item Details", "charge_item_removed_successfully": "Charge item removed successfully", "charge_item_updated": "Charge Item updated successfully", "charge_items": "Charge Items", "charge_items_added_successfully": "Charge Items added ssucccessfully", "chat_on_whatsapp": "Chat on Whatsapp", "check": "Check", "check_browser_settings": "Check your browser's address bar for permission settings", "check_eligibility": "Check Eligibility", "check_for_available_update": "Check for available update", "check_for_update": "Check for Update", "check_in": "Check-In", "check_item_condition_and_verify_receipt": "Check item condition and verify receipt", "check_policy_eligibility": "Check Policy Eligibility", "check_status": "Check Status", "checked_in": "Checked-In", "checking_availability": "Checking Availability", "checking_eligibility": "Checking Eligibility", "checking_for_update": "Checking for update", "checking_policy_eligibility": "Checking Policy Eligibility", "child_tag_archived_successfully": "Child tag archived successfully", "child_tags": "Child tags", "children": "Children", "choose_date_time": "Choose Date and Time", "choose_district": "Choose District", "choose_file": "Upload From Device", "choose_file_or_drag": "Choose file or drag & drop it here", "choose_layout_style": "Choose the layout style that best fits your sub-questions from the available options.", "choose_localbody": "Choose Local Body", "choose_location": "Choose Location", "choose_other_search_type": "Choose other search types", "choose_questionnaire": "Choose Questionnaire", "choose_state": "Choose State", "chronic_condition_one": "Chronic Condition", "chronic_condition_other": "Chronic Conditions", "claim__add_item": "Add Item", "claim__create_claim": "Create <PERSON><PERSON><PERSON>", "claim__create_preauthorization": "Create Pre Authorization", "claim__creating_claim": "Creating <PERSON><PERSON><PERSON>", "claim__creating_preauthorization": "Creating Pre Authorization", "claim__error_fetching_claim_approval_results": "Error <PERSON>tching <PERSON><PERSON><PERSON> Results", "claim__failed_to_create_claim": "Failed to create Claim", "claim__failed_to_create_preauthorization": "Failed to create Pre Authorization", "claim__fetched_claim_approval_results": "Fetched <PERSON><PERSON><PERSON> Results", "claim__item": "<PERSON><PERSON>", "claim__item__add_at_least_one": "Add at least one item", "claim__item__category": "Category", "claim__item__fill_all_details": "Fill all the item details", "claim__item__id": "ID", "claim__item__id__example": "PROC001", "claim__item__name": "Name", "claim__item__name__example": "Knee Replacement", "claim__item__price": "Price", "claim__item__price__example": "100.00", "claim__item__procedure": "Procedure", "claim__items": "Items", "claim__request_claim": "Request Claim", "claim__requesting_claim": "Requesting <PERSON><PERSON><PERSON>", "claim__status__approved": "Approved", "claim__status__pending": "Pending", "claim__status__rejected": "Rejected", "claim__total_approved_amount": "Total Approved Amount", "claim__total_claim_amount": "Total Claim Amount", "claim__use": "Use", "claim__use__claim": "<PERSON><PERSON><PERSON>", "claim__use__preauthorization": "Pre Authorization", "claims": "<PERSON><PERSON><PERSON>", "class_history": "Class History", "clear": "Clear", "clear_all_filters": "Clear all filters", "clear_blocked_camera_permissions": "Clear blocked camera permissions", "clear_filters": "Clear filters", "clear_home_facility": "Clear Home Facility", "clear_home_facility_confirm": "Are you sure you want to clear the home facility", "clear_home_facility_error": "Error while clearing home facility. Try again later.", "clear_search": "Clear search", "clear_selection": "Clear Selection", "clear_skill": "Clear Skill", "clearing": "Clearing", "click": "Click", "click_add_department_team": "Click <strong>Add Department/Team</strong> to create a new department/team.", "click_add_main_location": "Click <strong>Add Location</strong> to add a main location.", "click_manage_create_users": "Click <strong>See Details</strong><CareIcon /> to create or manage users and departments/teams within the corresponding dept/team.", "click_manage_create_users_mobile": "Click <CareIcon /> to create or manage users and departments/teams within the corresponding dept/team.", "click_manage_sub_locations": "Click <strong>See Details</strong><ArrowIcon/> to manage sub-locations.", "click_manage_sub_locations_mobile": "Click <PenLine/>to edit and <ArrowIcon/> to manage sub-locations.", "click_on": "Click on", "click_on_map_to_select_location": "Click on the map to select the location", "click_the_settings_icon_in_browser_address_bar": "Click the settings icon in browser address bar", "click_to_add_dosage_instructions": "Click to add dosage instructions", "clone": "<PERSON><PERSON>", "clone_questionnaire": "Clone Questionnaire", "clone_questionnaire_description": "Create a copy of this questionnaire with a new slug and select organizations.", "cloning": "Cloning...", "close": "Close", "close_account": "Close Account", "close_account_description": "Closing the account will also set the account to inactive.", "close_account_help_closed_baddebt": "Organization has been unable to recover the amount and has decided not to pursue debt recovery.", "close_account_help_closed_combined": "This account has been combined with another account, and all charges have been migrated.", "close_account_help_closed_completed": "Account is closed and all charged are processed and accounted for.", "close_account_help_closed_voided": "The account was not created in error, however the organization has decided not charge any transactions for this account.", "close_account_negative_balance": "This account has negative balance, closing this account will mark it as inactive.", "close_scanner": "<PERSON>r", "closed_baddebt": "Closed Bad Debt", "closed_combined": "Closed Combined", "closed_completed": "Closed Completed", "closed_voided": "Closed Voided", "code": "Code", "code_verification_required": "Code Verification is required", "code_verified_successfully": "Code verified successfully", "coding_details": "Coding Details", "collapse_all": "Collapse All", "collapse_sidebar": "Collapse Sidebar", "collect": "Collect", "collect_body_site": "Collect Body Site", "collect_method": "Collect Method", "collect_performer": "Collect Performer", "collect_specimen": "Collect Specimen", "collect_specimen_before_report": "Please collect the required specimen before creating a report", "collect_time": "Collect Time", "collected_at": "Collected At", "collection": "Collection", "collection_date_time": "Collection Date & Time", "collection_information": "Collection Information", "collection_method": "Collection Method", "collection_pending": "Collection Pending", "collector": "Collector", "columns_required_for_table_sections": "Columns are required for table sections", "combine_files_pdf": "Combine Files To PDF", "comment_added_successfully": "Comment added successfully", "comment_min_length": "Comment Should Contain At Least 1 Character", "comments": "Comments", "communication__sent_to_hcx": "Sent communication to HCX", "complete": "Complete", "complete_bed_stay": "Complete Bed Stay", "complete_blood_count_rbc_wbc_platelets": "Complete Blood Count (RBC + WBC + Platelets)", "complete_delivery": "Complete Delivery", "complete_dispense": "Complete Dispense", "completed": "Completed", "completed_encounters": "Completed Encounters", "component": "Component", "component_type": "Component Type", "component_value": "Component value", "component_wise_breakdown": "Component Wise Breakdown", "components": "Components", "computer_generated_medication_administration": "This is a computer generated medication administration.", "computer_generated_prescription": "This is a computer generated prescription.", "concepts": "Concepts", "conclusion": "Conclusion", "conclusion_updated_successfully": "Conclusion updated successfully", "condition": "Condition", "configure": "Configure", "configure_facility": "Configure Facility", "confirm": "Confirm", "confirm_action_description": "Are you sure you want to <1>{{action}}</1>?", "confirm_bed_assignment": "Confirm Bed Assignment", "confirm_completion": "Confirm Completion", "confirm_delete": "Confirm Delete", "confirm_discharge": "Confirm Patient Discharge", "confirm_discontinue": "Confirm Discontinue", "confirm_password": "Confirm Password", "confirm_remove_questions": "Confirm Remove Questions", "confirm_removing_member": "Confirm Removing Member", "confirm_removing_member_description": "Are you sure you want to remove {{member}} from the care team?", "confirm_selection": "Confirm Selection", "confirm_submission": "Confirm Submission", "confirm_transfer_complete": "Confirm Transfer Complete!", "confirm_unavailability": "Confirm Unavailability", "confirmed": "Confirmed", "consent": "Consent", "consent_attachments": "Consent Attachments", "consent_category__acd": "Advance Directive", "consent_category__acd_description": "Consent given in anticipation of a potential need for medical treatment", "consent_category__adr": "Advance Care Directive", "consent_category__adr_description": "Consent for actions to be taken if they are no longer able to make decisions for themselves", "consent_category__dnr": "Do Not Resuscitate", "consent_category__dnr_description": "Consent to not receive CPR or resuscitation initiated in case of a cardiac event", "consent_category__patient_privacy": "Privacy Consent", "consent_category__patient_privacy_description": "Consent to collect, access, use or disclose (share) information", "consent_category__research": "Research", "consent_category__research_description": "Consent to participate in research protocol and information sharing", "consent_category__treatment": "Treatment", "consent_category__treatment_description": "Consent to undergo a specific treatment", "consent_created_successfully": "<PERSON><PERSON> created successfully", "consent_decision": "Consent Decision", "consent_decision__deny": "<PERSON><PERSON>", "consent_decision__permit": "Permit", "consent_details": "Consent Details", "consent_expired": "Consent has expired", "consent_form": "Consent Form", "consent_given_on": "Consent Given On", "consent_not_active": "<PERSON><PERSON> is not active", "consent_not_found": "Consent not found", "consent_not_found_description": "The requested consent document could not be found or you don't have permission to view it.", "consent_option": "Choose an option", "consent_period_start_before_consent_date_validation": "Valid from date cannot be before the given date of the consent", "consent_status__active": "Active", "consent_status__draft": "Draft", "consent_status__entered_in_error": "Entered in Error", "consent_status__inactive": "Inactive", "consent_status__not_done": "Not Done", "consent_updated_successfully": "Consent updated successfully", "consent_valid_from": "Consent <PERSON>", "consent_valid_until": "Consent <PERSON>id <PERSON>", "consent_verification_type": "Verification Type", "consent_verification_type__family": "Family", "consent_verification_type__family_description": "Verification by a family member or representative.", "consent_verification_type__validation": "Validation", "consent_verification_type__validation_description": "Confirmation provided by the individual themselves.", "consents": "Consents", "consult": "Consult", "consultation": "Consultation", "consultation_history": "Consultation History", "consultation_missing_warning": "You have not created a consultation for the patient in", "consultation_not_filed": "You have not filed a consultation for this patient yet.", "consultation_not_filed_description": "Please file a consultation for this patient to continue.", "consultation_notes": "General Instructions (Advice)", "consultation_updates": "Consultation updates", "consumable": "Consumable", "contact_info": "Contact Information", "contact_info_note": "View or update user's contact information", "contact_info_note_self": "View or update your contact information", "contact_info_note_view": "View user's contact information", "contact_information": "Contact Information", "contact_information_description": "Provide contact details for follow-up communication.", "contact_number": "Contact Number", "contact_person": "Name of Contact Person at Facility", "contact_person_at_the_facility": "Contact person at the current facility", "contact_person_description": "Name of the person to contact regarding this request.", "contact_person_number": "Contact person number", "contact_phone": "Contact Person Number", "contact_phone_description": "Phone number to reach the contact person.", "contact_point_placeholder__email": "Enter email address", "contact_point_placeholder__fax": "Enter fax number", "contact_point_placeholder__other": "Enter contact value", "contact_point_placeholder__pager": "Enter pager number", "contact_point_placeholder__phone": "Enter phone number", "contact_point_placeholder__sms": "Enter SMS number", "contact_point_placeholder__url": "Enter URL", "contact_points": "Contact Points", "contact_system_email": "Email", "contact_system_fax": "Fax", "contact_system_other": "Other", "contact_system_pager": "Pager", "contact_system_phone": "Phone", "contact_system_sms": "SMS", "contact_system_url": "URL", "contact_use_home": "Home", "contact_use_mobile": "Mobile", "contact_use_old": "Old", "contact_use_temp": "Temporary", "contact_use_work": "Work", "contact_with_confirmed_carrier": "Contact with confirmed carrier", "contact_with_suspected_carrier": "Contact with suspected carrier", "contact_your_admin_to_add_facilities": "Contact your admin to add facilities", "contact_your_admin_to_add_skills": "Contact your admin to add skills", "container": "Container", "container_cap": "Container Cap", "container_capacity": "Container Capacity", "container_information": "Container Information", "container_min_volume": "Minimum Volume", "container_requirements": "Container Requirements", "continue": "Continue", "continue_watching": "Continue watching", "contribute_github": "Contribute on Github", "copied_to_clipboard": "Copied to clipboard!", "copilot_thinking": "<PERSON><PERSON><PERSON> is thinking...", "copy": "Copy", "copy_bom_json": "Copy BOM JSON", "copy_phone_number": "Copy Phone Number", "copying_is_not_allowed": "Copying is not allowed", "could_not_load_page": "We are facing some difficulties showing the Page you were looking for. Our Engineers have been notified and we'll make sure that this is resolved on the fly!", "counselling": "Counselling", "countries_travelled": "Countries travelled", "cover_image_deleted": "Cover Image Deleted", "cover_image_updated": "Cover Image Updated", "covid_19_cat_gov": "Covid_19 Clinical Category as per Govt. of Kerala guideline (A/B/C)", "covid_19_death_reporting_form_1": "Covid-19 Death Reporting : Form 1", "covid_details": "<PERSON><PERSON>", "create": "Create", "create_a_new_encounter_to_get_started": "Create a new encounter to get started", "create_account": "Create Account", "create_activity_definition": "Create Activity Definition", "create_add_more": "Create & Add More", "create_asset": "Create Asset", "create_charge_item_definition": "Create Charge Item Definition", "create_charge_item_definition_description": "Define pricing components for services or items", "create_consent": "Create Consent", "create_consultation": "Create Consultation", "create_delivery": "Create Delivery", "create_department_team": "Create Department/Team", "create_department_team_description": "Create a new department/team in this facility", "create_discount_code": "Create Discount Code", "create_discount_component": "Create Discount Component", "create_encounter": "Create Encounter", "create_facility": "Create Facility", "create_healthcare_service": "Create Healthcare Service", "create_invoice": "Create Invoice", "create_invoice_description": "Enter invoice details and check items to include in billing.", "create_location_association": "Create Location Association", "create_multiple_beds": "Create Multiple Beds", "create_multiple_beds_description": "Enable this option to create multiple beds at once", "create_new": "Create New", "create_new_asset": "Create New Asset", "create_new_consent": "Create New Consent", "create_new_consent_description": "Create a new consent record for this encounter", "create_new_drawing_message": "Click on 'New Drawing' to create your first drawing", "create_new_encounter": "Create a new encounter to get started", "create_new_facility": "Create a new facility and add it to the organization.", "create_new_product_instead": "Create new product instead", "create_new_report": "Create New Report", "create_new_tag": "Create New Tag", "create_new_value_set": "Create New ValueSet", "create_observation_definition": "Create Observation Definition", "create_organization": "Create Organization", "create_payment": "Create payment", "create_position_preset": "Create a new position preset", "create_position_preset_description": "Creates a new position preset in <PERSON> from the current position of the camera for the given name", "create_preset_prerequisite": "To create presets for this bed, you'll need to link the camera to the bed first.", "create_product": "Create Product", "create_product_description": "Create a new product that will be available in your facility's inventory.", "create_product_knowledge": "Create Product Knowledge", "create_purchase_order": "Create Purchase Order", "create_questionnaire": "Create Questionnaire", "create_report": "Create Report", "create_resource_request": "Create Request", "create_role": "Create Role", "create_schedule_template": "Create Schedule Template", "create_service_request": "Create Service Request", "create_specimen_definition": "Create Specimen Definition", "create_summaries": "Create Summaries", "create_supply_delivery": "Create Supply Delivery", "create_supply_request": "Create Supply Request", "create_tag": "Create Tag", "create_tag_config": "Create tag config", "create_tag_description": "Create a new tag for the questionnaire", "create_template": "Create Template", "create_user": "Create User", "create_user_and_add_to_org": "Create a new user and add them to the organization.", "create_valueset": "Create ValueSet", "created": "Created", "created_by": "Created By", "created_date": "Created Date", "created_on": "Created On", "creating": "Creating...", "creating_child_tag_for": "Creating child tag for", "creating_facility": "Creating Facility...", "credit_card": "Credit Card", "critical": "Critical", "criticality": "Criticality", "crop": "Crop", "cropping": "Cropping", "cropping_indicator": "Cropping...", "csv_file_in_the_specified_format": "Select a CSV file in the specified format", "current_address": "Current Address and Route to Home", "current_balance": "Current Balance", "current_encounter": "Current Encounter", "current_location": "Current Location", "current_location_description": "The current location of this device", "current_order": "Current Order", "current_organization_one": "Current Organization", "current_organization_other": "Current Organizations", "current_password": "Current Password", "current_position": "Current position", "current_role": "Current Role", "current_status": "Current Status", "current_year_yy_help": "Current year (2 digits)", "current_year_yyyy_help": "Current year (4 digits)", "custom": "Custom", "custom_options": "Custom Options", "customer_support_email": "Customer Support Email", "customer_support_name": "Customer Support Name", "customer_support_number": "Customer support number", "customize_bed_names": "Customize individual bed names", "customize_bed_names_description": "Edit names of each bed individually", "cylinders": "Cylinders", "cylinders_per_day": "Cylinders/day", "daily_rounds": "Daily Rounds", "damaged": "Damaged", "danger_zone": "Danger Zone", "danger_zone_description": "These actions cannot be undone. Please proceed with caution.", "dashboard": "Dashboard", "dashboard_tab_associations": "These are the professional or role-based groups - such as doctors, nurses, technicians, volunteers, or staff - that you are affiliated with.", "dashboard_tab_facilities": "These are the clinics, hospitals, or health centers you manage or belong to.", "dashboard_tab_governance": "These are the Local Self-Government (LSG) networks or groups you're affiliated with, including State, District, Block, Municipality, Corporation, Panchayat, and Ward-level governance bodies.", "data_collection_details": "Data collection details", "data_collection_details_description": "Specify key collection info: time, performer, body site, and method.", "data_source": "Data Source", "data_type": "Data Type", "date": "Date", "dateTime": "DateTime", "date_and_time": "Date and Time", "date_and_time_of_death": "Date and Time of Death", "date_declared_positive": "Date of declaring positive", "date_of_admission": "Date of Admission", "date_of_birth": "Date of Birth", "date_of_birth_age": "Date of Birth / Age", "date_of_birth_format": "Date of birth must be in YYYY-MM-DD format", "date_of_birth_must_be_present": "Date of birth must be present", "date_of_birth_or_age": "Date of Birth or Age", "date_of_positive_covid_19_swab": "Date of Positive Covid 19 Swab", "date_of_result": "Covid confirmation date", "date_of_return": "Date of Return", "date_of_test": "Date of sample collection for Covid testing", "date_range": "Date Range", "date_range_from_till": "From {{from}} till {{till}}", "dates_and_identifiers": "Dates & Identifiers", "datetime": "Date/Time", "day": "Day", "days": "Days", "days_supply": "Days Supply", "death_date_must_be_after_dob": "Death date must be after date of birth", "debit_card": "Debit Card", "deceased": "Deceased", "deceased_datetime": "Deceased Date/Time", "deceased_disclaimer": "Please provide the date and time of death for record-keeping purposes. This information is handled with utmost sensitivity and respect.", "deceased_status": "Deceased Status", "decimal": "Decimal", "decision": "Decision", "declined": "Declined", "default_value": "Serial Number", "default_value_help": "You can use a template string to auto-generate serial numbers.", "default_value_title": "Default pattern for this identifier type", "definition": "Definition", "definition_deleted_successfully": "Definition deleted successfully", "definition_not_found": "Definition not found", "delete": "Delete", "delete_account": "Delete account", "delete_account_btn": "Yes, delete this account", "delete_account_note": "Deleting this account will remove all associated data and cannot be undone.", "delete_activity_definition": "Delete Activity Definition", "delete_charge_item_definition": "Delete charge item definition", "delete_device": "Delete Device", "delete_device_confirmation": "Are you sure you want to delete this device? This action cannot be undone.", "delete_device_description": "Once you delete a device, there is no going back. Please be certain.", "delete_facility": "Delete Facility", "delete_facility_confirmation": "Are you sure you want to delete {{name}}? This action cannot be undone.", "delete_facility_description": "Once you delete a facility, there is no going back. Please be certain.", "delete_item": "Delete {{name}}", "delete_observation": "Delete Observation", "delete_observation_definition": "Delete Obsertvation Definition", "delete_organization": "Delete Organization", "delete_record": "Delete Record", "delete_this_device": "Delete this device", "delete_user": "Delete User", "deleting": "Deleting...", "deliver_from": "Deliver From", "deliver_to": "Deliver To", "delivered_at": "Delivered At", "delivered_by": "Delivered By", "delivered_to": "Delivered To", "deliveries": "Deliveries", "deliveries_approved": "Deliveries approved successfully", "delivery_conditions": "Delivery conditions", "delivery_details": "Delivery Details", "demography": "Demography", "denied": "Denied", "denied_on": "Denied On", "department": "Department", "departments": "Departments", "departments_and_teams": "Departments and Teams", "departments_or_teams": "Departments/Teams", "derived_from": "Derived From", "derived_from_uri": "Derived From URI", "describe_why_the_asset_is_not_working": "Describe why the asset is not working", "description": "Description", "description_help": "A short description of this identifier type (e.g., National ID, Hospital MRN).", "destination": "Destination", "destination_question": "Destination Question", "details": "Details", "details_about_the_equipment": "Details about the equipment", "details_of_assigned_facility": "Details of assigned facility", "details_of_origin_facility": "Details of origin facility", "details_of_patient": "Details of patient", "details_of_shifting_approving_facility": "Details of shifting approving facility", "device": "<PERSON><PERSON>", "device_associated_successfully": "Device associated successfully", "device_association_exist": "Device association exist!", "device_availability_status_available": "Available", "device_availability_status_damaged": "Damaged", "device_availability_status_destroyed": "Destroyed", "device_availability_status_lost": "Lost", "device_contact_description": "Contact points associated with this device", "device_encounter_history": "<PERSON><PERSON>er History", "device_encounter_history_description": "To manage encounter association of this device, open the 'Devices' tab from the encounter.", "device_information": "Device Information", "device_location_history": "Device Location History", "device_not_found": "Devi<PERSON> not found", "device_registered": "Device registered successfully", "device_status_active": "Active", "device_status_entered_in_error": "Entered in Error", "device_status_inactive": "Inactive", "device_updated": "Device updated successfully", "devices": "Devices", "diagnosed_on": "Diagnosed On", "diagnoses": "Diagnoses", "diagnosis": "Diagnosis", "diagnosis__confirmed": "Confirmed", "diagnosis__differential": "Differential", "diagnosis__principal": "Principal", "diagnosis__provisional": "Provisional", "diagnosis__unconfirmed": "Unconfirmed", "diagnosis_already_added": "This diagnosis was already added", "diagnosis_already_exist_warning": "Diagnosis already exists!", "diagnosis_at_discharge": "Diagnosis at Discharge", "diagnosis_empty_message": "No diagnoses recorded", "diagnosis_history": "Diagnosis History", "diagnosis_status_placeholder": "Select diagnosis status", "diagnosis_verification_placeholder": "Select verification status", "diagnostic_report": "Diagnostic Report", "diagnostic_report_codes": "Diagnostic Report Codes", "diagnostic_report_codes_description": "Diagnostic Reports that will be generated.", "diagnostic_report_not_found": "Diagnostic Report Not Found", "diastolic": "Diastolic", "didnt_receive_a_message": "Didn't receive a message?", "diet_preference": "Diet Preference", "differential": "Differential", "differential_diagnosis": "Differential diagnosis", "direct_deposit": "Direct Deposit", "directive": "Directive", "disable_2fa_confirmation": "Disabling 2FA will remove the extra layer of protection that helps prevent unauthorized access to your account.", "disable_transfer": "Disable Transfer", "disable_two_factor_authentication": "Disable two-factor authentication", "disassociate": "Disassociate", "disassociate_device_from_encounter": "Disassociate device from encounter", "disassociating": "Disassociating...", "discard": "Discard", "discard_changes": "Discard Changes", "discard_damaged_item": "Discard Damaged Item", "discarding": "Discarding...", "discharge": "Discharge", "discharge_confirmation_date": "Discharge date and time will be automatically set to current date and time", "discharge_confirmation_message": "You are about to mark this patient as discharged.", "discharge_confirmation_status_change": "The encounter status will be changed to 'Discharged'", "discharge_confirmation_summary_required": "You will have an option to provide discharge summary advice details", "discharge_date_time": "Discharge Date and Time", "discharge_details": "Discharge Details", "discharge_disposition": "Discharge Disposition", "discharge_from_care": "Discharge from CARE", "discharge_patient": "Discharge <PERSON>", "discharge_patient_description": "Ready to discharge? This will update the patient's status and require discharge summary details.", "discharge_prescription": "Discharge Prescription", "discharge_summaries": "Discharge Summaries", "discharge_summary": "Discharge Summary", "discharge_summary_advice": "Discharge Summary Advice", "discharge_summary_emailed": "Discharged Summary emailed", "discharge_summary_generated": "Discharge Summary generated", "discharge_summary_not_ready": "Discharge summary is not ready yet.", "discharged": "Discharged", "discharged_on": "Discharged On", "discharged_patients": "Discharged Patients", "discharged_patients_empty": "No discharged patients present in this facility", "discharged_to": "Discharged to", "disclaimer": "Disclaimer", "disclaimer_computer_generated_summary": "Disclaimer: This is a computer generated summary and may not be 100% accurate. Please verify the details before using it.", "discontinue": "Discontinue", "discontinue_caution_note": "Are you sure you want to discontinue this prescription?", "discontinued": "Discontinued", "discount": "Discount", "discount_amount_description": "Enter a fixed amount to deduct from the bill", "discount_amount_range_description": "Min. amount must be 0", "discount_code": "Discount Code", "discount_code_code_description": "A unique code that users can enter to apply this discount", "discount_code_created": "Discount code created successfully", "discount_code_deleted": "Discount code deleted successfully", "discount_code_name_description": "A descriptive name for the discount code that will be displayed to users", "discount_code_updated": "Discount code updated successfully", "discount_codes": "Discount Codes", "discount_component_code_description": "Select a discount code to associate with this component. Leave empty if no code is required", "discount_component_created": "Discount component created!", "discount_component_deleted": "Discount component deleted!", "discount_component_name_description": "A descriptive name for the discount component that will be displayed in bills and reports", "discount_component_updated": "Discount component updated!", "discount_explanation": "Reductions applied before taxes", "discount_factor_applies_to_subtotal_before_discounts": "Factor applies to subtotal before discounts", "discount_factor_description": "Enter a percentage value between {{min}}% and {{max}}% to apply as a discount", "discount_factor_or_amount": "Discount amount or factor", "discount_factor_range_description": "Value must be between 0 and 100%", "discount_monetary_components": "Discount Monetary Components", "discounted_price": "Disc. Price/Unit", "discounts": "Discounts", "discover_healthcare_services": "Discover the comprehensive healthcare services we offer", "disease_status": "Disease status", "dispatch_in_progress_from": "Dispatch in progress from", "dispatched_at": "Dispatched At", "dispatched_from": "Dispatched From", "dispatched_quantity": "Dispatched Quantity", "dispense": "Dispense", "dispense_medications": "Dispense Medications", "dispense_selected": "Dispense Selected", "dispense_status_updated": "Medication dispense status updated", "dispensed": "Dispensed", "dispensed_items": "Dispensed Items", "dispensed_on": "Dispensed On", "dispensing": "Dispensing...", "display": "Display", "display_as_table": "Display as Table", "display_help": "How this identifier should be displayed to users (e.g., on cards or printouts).", "display_help_short": "A user-friendly label for this identifier.", "display_name": "Display name", "display_style": "Display Style", "display_text": "Display Text", "display_text_placeholder": "Display text (optional)", "disposition": "Disposition", "distributor": "Distributor", "district": "District", "district_is_required_when_state_is_selected": "District is required when state is selected", "district_program_management_supporting_unit": "District Program Management Supporting Unit", "do not perform": "Do not perform", "do_not_perform": "Do Not Perform", "dob": "DOB", "dob_format": "Please enter date in DD/MM/YYYY format", "doc_will_visit_patient": "will visit the patient at the scheduled time.", "doctor": "Doctor", "doctor_experience_error": "Please enter a valid number between 0 and 100.", "doctor_experience_required": "Years of experience is required", "doctor_not_found": "Doctor not found", "doctor_nurse": "Doctor/Nurse", "doctor_s_medical_council_registration": "Doctor's Medical Council Registration", "doctors_name": "Doctor's Name", "doctors_progress_note": "Doctor's Progress Note", "docx": "DOCX", "domestic_healthcare_support": "Domestic healthcare support", "domestic_international_travel": "Domestic/international Travel (within last 28 days)", "done": "Done", "dont_share_code": "Don't share this verification code to anyone!", "dosage": "Dosage", "dosage_form": "Dosage Form", "dosage_form_placeholder": "Select Dosage Form", "dosage_instructions": "Dosage Instructions", "down": "Down", "download": "Download", "download_discharge_summary": "Download Discharge Summary", "download_to_play": "Download to play locally", "download_type": "Download Type", "downloading": "Downloading", "downloading_abha_card": "Generating ABHA Card, Please hold on", "downloads": "Downloads", "draft": "Draft", "drag_and_drop_or_click_to_select": "Drag and drop file here or click to upload", "drag_drop_image_to_upload": "Drag & drop image to upload", "draw": "Draw", "drawing_saved_successfully": "Drawing saved successfully", "drawings": "Drawings", "drop_file_here": "Drop file here", "due_date": "Due Date", "duplicate": "Duplicate", "duplicate_contact_values_not_allowed": "Duplicate contact values are not allowed across different contact types", "duplicate_patient_record_birth_unknown": "Please contact your district care coordinator, the shifting facility or the patient themselves if you are not sure about the patient's year of birth.", "duplicate_patient_record_confirmation": "Admit the patient record to your facility by adding the year of birth", "duplicate_patient_record_rejection": "I confirm that the suspect / patient I want to create is not on the list.", "duration": "Duration", "duration_unit": "Duration Unit", "duration_unit_placeholder": "Select Duration Unit", "duration_value": "Duration Value", "edit": "Edit", "edit_account": "Edit Account", "edit_activity_definition": "Edit Activity Definition", "edit_administration": "Edit Administration", "edit_avatar": "Edit Avatar", "edit_avatar_note": "Change the avatar of the user", "edit_avatar_note_self": "Change your avatar", "edit_avatar_permission_error": "You do not have permissions to edit the avatar of this user", "edit_bed_names": "Edit {{count}} bed names ", "edit_caution_note": "A new prescription will be added to the consultation with the edited details and the current prescription will be discontinued.", "edit_charge_item": "Edit Charge Item", "edit_charge_item_description": "Edit this charge item", "edit_consent": "Edit Consent", "edit_consent_description": "Edit the patient's consent details and attach supporting documents.", "edit_cover_photo": "Edit Cover Photo", "edit_department_team": "Edit Department/Team", "edit_department_team_description": "Edit the current department/team", "edit_device": "<PERSON>", "edit_device_description": "Edit the details of the device", "edit_discount_code": "Edit Discount Code", "edit_discount_component": "Edit Discount Component", "edit_dosage_instructions": "Edit Dosage Instructions", "edit_facility": "Edit Facility", "edit_facility_details": "Edit Facility Details", "edit_form": "Edit Form", "edit_healthcare_service": "Edit Healthcare Service", "edit_history": "Edit History", "edit_invoice": "Edit Invoice", "edit_invoice_details": "Edit Invoice Details", "edit_invoice_number_expression": "Edit Invoice Number Expression", "edit_location": "Edit Location", "edit_location_description": "Edit the Location to make any changes", "edit_observation_definition": "Edit Observation Definition", "edit_patient_identifier_config": "Edit patient identifier config", "edit_policy": "Edit Insurance Policy", "edit_policy_description": "Add or edit patient's insurance details", "edit_prescriptions": "Edit Prescriptions", "edit_product": "Edit Product", "edit_product_description": "Update information for this product", "edit_product_knowledge": "Edit Product Knowledge", "edit_profile": "Edit Profile", "edit_purchase_order": "Edit Purchase Order", "edit_role": "Edit Role", "edit_schedule_template": "Edit Schedule Template", "edit_stock_request": "Edit Stock Request", "edit_supply_delivery": "Edit Supply Delivery", "edit_supply_request": "Edit Supply Request", "edit_tag": "Edit tag", "edit_tag_config": "Edit tag config", "edit_template": "Edit Template", "edit_user": "Edit User", "edit_user_description": "Update personal details and contact information", "edit_user_profile": "Edit Profile", "edit_user_role": "Edit User Role", "edit_value_set": "Edit ValueSet", "edited_by": "Edited by", "edited_on": "Edited on", "education": "Education", "eg_abc": "Eg. ABC", "eg_default_value": "e.g. #Patient{patient_count + 100}{current_year_yy}", "eg_details_on_functionality_service_etc": "Eg. Details on functionality, service, etc.", "eg_mail_example_com": "Eg. <EMAIL>", "eg_national_id": "e.g. National ID", "eg_national_id_card": "e.g. National ID Card", "eg_regex_pattern": "e.g. ^[A-Z0-9]{8,12}$", "eg_xyz": "Eg. XYZ", "either_amount_or_factor_required": "Either amount or factor is required", "eligible": "Eligible", "email": "Email", "email_address": "Email Address", "email_discharge_summary_description": "Enter your valid email address to receive the discharge summary", "email_success": "We will be sending an email shortly. Please check your inbox.", "emergency": "Emergency", "emergency_contact": "Emergency Contact", "emergency_contact_number": "Emergency Contact Number", "emergency_contact_person_name": "Emergency Contact Person Name", "emergency_contact_person_name_details": "Emergency contact person (Father, Mother, Spouse, <PERSON><PERSON>, Friend)", "emergency_contact_person_name_volunteer": "Emergency Contact Person Name (Volunteer)", "emergency_contact_volunteer": "Emergency Contact (Volunteer)", "emergency_description": "Mark as emergency if immediate attention is required.", "emergency_phone_number": "Emergency Phone Number", "empty_date_time": "--:-- --; --/--/----", "empty_drawing": "Empty drawing", "enable_behavior": "Enable behaviour", "enable_when__all": "All the conditions must be met", "enable_when__any": "Any one or more of the conditions must be met", "enable_when_conditions": "Enable when conditions", "enabled": "Enabled", "enabled_only_for_partially_dispensed": "Enabled only for partially dispensed medications", "encounter": "Encounter", "encounter_admit_sources__born": "Born in hospital", "encounter_admit_sources__emd": "From accident/emergency department", "encounter_admit_sources__gp": "General Practitioner referral", "encounter_admit_sources__hosp_trans": "Transferred from other hospital", "encounter_admit_sources__mp": "Medical Practitioner/physician referral", "encounter_admit_sources__nursing": "From nursing home", "encounter_admit_sources__other": "Other", "encounter_admit_sources__outp": "From outpatient department", "encounter_admit_sources__psych": "From psychiatric hospital", "encounter_admit_sources__rehab": "From rehabilitation facility", "encounter_class": "Encounter Class", "encounter_class__amb": "Ambulatory", "encounter_class__completed": "Completed", "encounter_class__emer": "Emergency", "encounter_class__hh": "Home Health", "encounter_class__imp": "Inpatient", "encounter_class__in_progress": "In Progress", "encounter_class__obsenc": "Observation", "encounter_class__planned": "Planned", "encounter_class__vr": "Virtual", "encounter_class_description__amb": "Patient visits for outpatient care", "encounter_class_description__emer": "Mark as emergency if immediate attention is required.", "encounter_class_description__hh": "Care provided at patient's home", "encounter_class_description__imp": "Patient is admitted to the hospital", "encounter_class_description__obsenc": "Patient is under observation", "encounter_class_description__vr": "Virtual/telehealth consultation", "encounter_created": "Encounter created successfully", "encounter_date": "Encounter Date", "encounter_date_field_label__A": "Date & Time of Admission to the Facility", "encounter_date_field_label__DC": "Date & Time of Domiciliary Care commencement", "encounter_date_field_label__DD": "Date & Time of Consultation", "encounter_date_field_label__HI": "Date & Time of Consultation", "encounter_date_field_label__OP": "Date & Time of Out-patient visit", "encounter_date_field_label__R": "Date & Time of Consultation", "encounter_diet_preference__dairy_free": "Diary free", "encounter_diet_preference__gluten_free": "Gluten free", "encounter_diet_preference__halal": "<PERSON><PERSON>", "encounter_diet_preference__kosher": "<PERSON><PERSON>", "encounter_diet_preference__none": "None", "encounter_diet_preference__nut_free": "Nut free", "encounter_diet_preference__vegan": "Vegan", "encounter_diet_preference__vegetarian": "Vegetarian", "encounter_discharge_disposition__aadvice": "Left against advice", "encounter_discharge_disposition__alt_home": "Alternate home", "encounter_discharge_disposition__exp": "Expired", "encounter_discharge_disposition__home": "Home", "encounter_discharge_disposition__hosp": "Hospice", "encounter_discharge_disposition__long": "Long term care", "encounter_discharge_disposition__oth": "Other", "encounter_discharge_disposition__other_hcf": "Other health care facility", "encounter_discharge_disposition__psy": "Psychiatric hospital", "encounter_discharge_disposition__rehab": "Rehabilitation", "encounter_discharge_disposition__snf": "Skilled nursing facility", "encounter_duration_confirmation": "The duration of this encounter would be", "encounter_id": "Encounter ID", "encounter_locations": "Encounter Locations", "encounter_manage_organization_description": "Add or remove organizations from this encouter", "encounter_marked_as_complete": "Encounter Completed", "encounter_priority__ASAP": "ASAP", "encounter_priority__as_needed": "As needed", "encounter_priority__asap": "ASAP", "encounter_priority__callback_for_scheduling": "Callback for scheduling", "encounter_priority__callback_results": "Callback results", "encounter_priority__elective": "Elective", "encounter_priority__emergency": "Emergency", "encounter_priority__preop": "Pre-op", "encounter_priority__routine": "Routine", "encounter_priority__rush_reporting": "Rush reporting", "encounter_priority__stat": "Stat", "encounter_priority__timing_critical": "Timing critical", "encounter_priority__urgent": "<PERSON><PERSON>", "encounter_priority__use_as_directed": "Use as directed", "encounter_questionnaire_logs": "Encounter Questionnaire Logs", "encounter_re_admission__false": "No", "encounter_re_admission__true": "Yes", "encounter_settings": "Encount<PERSON>", "encounter_status": "Encounter Status", "encounter_status__cancelled": "Cancelled", "encounter_status__completed": "Completed", "encounter_status__discharged": "Discharged", "encounter_status__discontinued": "Discontinued", "encounter_status__entered_in_error": "Entered in error", "encounter_status__in_progress": "In Progress", "encounter_status__on_hold": "On Hold", "encounter_status__planned": "Planned", "encounter_status__unknown": "Unknown", "encounter_suggestion__A": "Admission", "encounter_suggestion__DC": "Domiciliary Care", "encounter_suggestion__DD": "Consultation", "encounter_suggestion__HI": "Consultation", "encounter_suggestion__OP": "Out-patient visit", "encounter_suggestion__R": "Consultation", "encounter_suggestion_edit_disallowed": "Not allowed to switch to this option in edit consultation", "encounter_type": "Encounter Type", "encounters": "Encounters", "end_date": "End date", "end_date_after_start": "End date cannot be before start date", "end_datetime": "End Date/Time", "end_dose": "<PERSON>", "end_time": "End Time", "end_time_before_start_error": "End time cannot be before start time", "end_time_future_error": "End time cannot be in the future", "end_time_required": "End time is required", "ended": "Ended", "ensure_conditions_are_valid": "Please ensure that conditions are still valid (or delete the condition) when modifying this question.", "enter": "Enter", "enter_2fa_code": "Enter the code generated by your 2-factor auth app", "enter_address": "Enter complete address", "enter_capacity": "Enter Capacity", "enter_code": "Enter code", "enter_conclusion": "Enter conclusion", "enter_contact_value": "Enter contact value", "enter_custom_text": "Enter custom text", "enter_department_team_description": "Enter department/team description (optional)", "enter_department_team_name": "Enter department/team name", "enter_description": "Enter Description", "enter_discharge_summary_advice": "Enter the discharge summary advice", "enter_display_name": "Enter display name", "enter_dosage_instructions": "Enter Dosage Instructions", "enter_drawing_name": "Enter name for the drawing", "enter_facility_name": "Enter Facility Name", "enter_file_name": "Enter File Name", "enter_identifier": "Enter device identifier", "enter_identifier_value": "Enter identifier value", "enter_latitude": "Enter latitude", "enter_longitude": "Enter longitude", "enter_lot_number": "Enter lot number", "enter_manufacturer": "Enter manufacturer name", "enter_message": "Start typing...", "enter_minimum_volume": "Enter Minimum Volume", "enter_mobile_number": "Enter Mobile Number", "enter_mobile_otp": "Enter OTP sent to the given mobile number", "enter_model_number": "Enter model number", "enter_otp": "Enter OTP sent to the registered mobile with the respective ID", "enter_part_number": "Enter part number", "enter_patient_instructions": "Enter Patient Instruction", "enter_phone_number": "Enter phone number", "enter_phone_number_to_login_register": "Enter phone number to login/register", "enter_pincode": "Enter pincode", "enter_position": "Enter position", "enter_priority": "Enter the priority", "enter_quantity": "Enter quantity", "enter_recovery_code": "Enter your 8 digit backup code", "enter_registered_name": "Enter the registered name of the device", "enter_retention_time": "Enter Retention Time", "enter_role_description": "Enter role description", "enter_role_name": "Enter role name", "enter_section_title": "Enter section title", "enter_serial_number": "Enter serial number", "enter_specimen_id": "Enter the Specimen ID", "enter_tag_name": "Enter tag name", "enter_tag_slug": "Enter tag slug", "enter_the_file_name": "Enter the file name", "enter_the_verification_code": "Enter the verification code sent to your phone", "enter_title": "Enter Title", "enter_user_friendly_name": "Enter a user friendly name for the device", "enter_valid_age": "Please Enter Valid Age", "enter_valid_amount": "Please enter a valid amount", "enter_valid_dob": "Enter a valid date of birth", "enter_valid_dob_age": "Please enter an age greater than 15 years", "enter_verification_code": "Enter the verification code", "enter_year_of_birth_to_verify": "Enter year of birth to verify", "enter_your_valid_email_address_to_receive_the_discharge_summary": "Enter your valid email address to receive the discharge summary", "entered": "Entered", "entered_in_error": "Entered in Error", "entered_in_error_warning": "This action cannot be undone. The appointment will be marked as entered in error and removed from the system.", "entity_count_one": "{{count}} {{entity}}", "entity_count_other": "{{count}} {{entity}}s", "entity_deleted_successfully": "{{name}} deleted successfully", "entry_date": "Entry Date", "environment": "Environment", "equals": "Equals", "error": "Error", "error_404": "Error 404", "error_approving_deliveries": "Error approving deliveries", "error_calculating_price": "Error calculating price", "error_creating_consent": "Error creating consent", "error_creating_supply_delivery": "Error creating supply delivery", "error_deleting_shifting": "Error while deleting Shifting record", "error_dispensing_medications": "Failed to dispense medications. Please try again.", "error_fetching_charge_item_definition": "Error fetching charge item definition", "error_fetching_facility_data": "Error while fetching facility data", "error_fetching_slots_data": "Error while fetching slots data", "error_fetching_user_data": "Error while fetching user data", "error_fetching_user_details": "Error while fetching user details: ", "error_fetching_users_data": "Failed to load user data. Please try again later.", "error_generating_discharge_summary": "Error generating discharge summary", "error_generating_svg": "Error generating SVG", "error_in_createUpload": "Error in createUpload", "error_loading_definitions": "Error loading the definitions", "error_loading_observation_definition": "Error loading observation definition", "error_loading_pdf": "Error loading PDF", "error_loading_product": "Error Loading Product", "error_loading_product_knowledge": "Failed to load product knowledge. Please try again later.", "error_loading_purchase_order": "Error loading purchase order", "error_loading_questionnaire_response": "Error loading questionnaire response", "error_loading_sq_or_ad": "Error loading service request or activity definition details.", "error_receiving_stock": "Error receiving stock", "error_recording_payment": "Error recording payment", "error_removing_bed_assignment": "Error while removing the bed assignment. Please try again later.", "error_retiring_specimen_definition": "Error retiring specimen definition", "error_saving_file": "Error saving file", "error_updating_charge_item": "Error updating the charge item", "error_updating_consent": "Error updating consent", "error_updating_delivery": "Error updating delivery", "error_updating_encounter": "Error to Updating Encounter", "error_updating_location": "Error updating location", "error_updating_status": "Failed to update status", "error_updating_supply_request": "Failed to update supply request", "error_uploading_files": "Error uploading files", "error_validating_form": "An error occurred while validating the form", "error_verifying_otp": "Error while verifying OTP, Please request a new OTP", "error_while_deleting_record": "Error while deleting record", "escape": "Escape", "estimated_contact_date": "Estimated contact date", "etc": "etc...", "etiology_identified": "Etiology identified", "evening_slots": "Evening Slots", "events": "Events", "exam": "Exam", "example_email_address": "<EMAIL>", "exception": "Exception", "exception_created": "Exception created successfully", "exception_deleted": "Exception deleted", "exception_for_non_schedulable_resource_warning": "Exceptions can be created only after creating a schedule", "exceptions": "Exceptions", "exclude_rules": "Exclude rules", "exists": "Exists", "expand_all": "Expand All", "expand_sidebar": "Expand Sidebar", "expected_burn_rate": "Expected Burn Rate", "expiration_date": "Expiration Date", "expiration_date_description": "The date after which this product should not be used", "expiration_date_must_be_after_manufacture_date": "Expiration date must be after manufacture date", "expired": "Expired", "expires": "Expires", "expires_on": "Expires On", "expiry": "Expiry", "expiry_date": "Expiry Date", "export": "Export", "export_live_patients": "Export Live Patients", "exporting": "Exporting", "external": "External", "external_id": "External ID", "external_identifier": "External Identifier", "external_prescriptions": "External prescriptions", "external_supply": "External Supply", "extra_details": "Extra Details", "facilities": "Facilities", "facility": "Facility", "facility_actions_menu": "Facility action menu", "facility_added_successfully": "Facility created successfully", "facility_assign_request": "What facility would you like to assign the request to?", "facility_consent_requests_page_title": "Patient Consent List", "facility_count_one": "{{count}} Facility", "facility_count_other": "{{count}} Facilities ", "facility_deleted_successfully": "{{name}} has been deleted successfully.", "facility_district_name": "Facility/District Name", "facility_district_pincode": "Facility/District/Pincode", "facility_for_care_support": "Facility for Care Support", "facility_linked_success": "Facility linked successfully", "facility_name": "Facility Name", "facility_not_found": "Facility Not Found", "facility_organization_type__dept": "Department", "facility_organization_type__other": "Other", "facility_organization_type__root": "Root", "facility_organization_type__team": "Team", "facility_organizations": "Facility Organizations", "facility_preference": "Facility preference", "facility_type": "Facility Type", "facility_type_required": "Facility type is required", "facility_updated_success": "Facility updated successfully", "facility_updated_successfully": "Facility updated successfully", "factor": "Factor", "failed_to_archive_child_tag": "Failed to archive child tag", "failed_to_cancel_invoice": "Failed to cancel invoice", "failed_to_capture_image": "Failed to capture image", "failed_to_create_appointment": "Failed to create an appointment", "failed_to_create_invoice": "Failed to create invoice", "failed_to_create_questionnaire": "Failed to create Questionnaire", "failed_to_crop_image_using_original_image": "Failed to crop image. Using original image", "failed_to_dispatch_some_items": "Failed to dispatch {{failed}} of {{total}} items.", "failed_to_import_questionnaire": "Failed to import questionnaire", "failed_to_link_abha_number": "Failed to link ABHA Number. Please try again later.", "failed_to_remove_charge_item": "Failed to remove charge item", "failed_to_remove_tags": "Failed to remove the tag", "failed_to_send_message": "Failed to send message", "failed_to_stop_camera": "Failed to stop camera", "failed_to_update_conclusion": "Failed to update conclusion", "failed_to_update_invoice": "Failed to update invoice", "failed_to_update_order": "Failed to update location order", "failed_to_update_questionnaire": "Failed to update Questionnaire", "failed_to_update_tags": "Failed to update tags", "failed_to_verify_code": "Failed to verify code", "false": "False", "fast_track_testing_reason": "Fast track testing reason", "fasting_duration": "Fasting Duration (optional)", "fasting_duration_placeholder": "Duration value (e.g., 8)", "fasting_status": "Fasting Status", "fasting_status_placeholder": "Select status", "features": "Features", "feed_configurations": "Feed Configurations", "feed_is_currently_not_live": "Feed is currently not live", "feed_optimal_experience_for_apple_phones": "For optimal viewing experience, consider rotating your device. Ensure auto-rotate is enabled in your device settings.", "feed_optimal_experience_for_phones": "For optimal viewing experience, consider rotating your device.", "female": "Female", "fetched_attachments_successfully": "Fetched attachments successfully", "fetching": "Fetching", "field_required": "This field is required", "fields": "Fields", "fields_columns": "Fields & Columns", "fields_required_for_custom_sections": "Fields are required for custom sections", "fields_required_for_structured_sections": "Fields are required for structured sections", "file_archived_successfully": "File archived successfully", "file_conversion_in_progress": "File conversion in progress", "file_conversion_success": "File conversion successful", "file_download_completed": "File download completed", "file_download_failed": "Failed to download file", "file_download_started": "Downloading file...", "file_error__choose_file": "Please choose a file to upload", "file_error__dynamic": "Error Uploading File: {{statusText}}", "file_error__file_name": "Please give a name for all files!", "file_error__file_size": "Maximum size of files is 100 MB", "file_error__file_type": "Invalid file type \".{{extension}}\" Allowed types: {{allowedExtensions}}", "file_error__generate_pdf": "Failed to generate PDF", "file_error__mark_complete_failed": "Error while marking file upload as complete", "file_error__network": "Error Uploading File: Network Error", "file_error__single_file_name": "Please give a name for the file", "file_list_headings__consultation": "Consultation Files", "file_list_headings__patient": "Patient Files", "file_list_headings__sample_report": "Sample Report", "file_list_headings__supporting_info": "Supporting Info", "file_name": "File Name", "file_name_changed_successfully": "File name changed successfully", "file_preview": "File Preview", "file_preview_not_supported": "Can't preview this file. Try downloading it.", "file_saved_successfully": "File saved successfully", "file_success__upload_complete": "File upload complete", "file_type": "File Type", "file_upload_error": "Error uploading file", "file_upload_success": "File uploaded successfully", "file_uploaded": "File Uploaded Successfully", "filed": "<strong>{{title}}</strong> filed", "files": "Files", "fill_color": "Fill Color", "fill_my_details": "Fill My Details", "filler_order": "Filler Order", "filter": "Filter", "filter_by": "Filter <PERSON>", "filter_by_category": "Filter by category", "filter_by_date": "Filter by Date", "filter_by_delivery": "Filter by delivery", "filter_by_department_or_team_name": "Filter by department or team name", "filter_by_locations": "Filter by Locations", "filter_by_patient": "Filter by Patient", "filter_by_priority": "Filter by Priority", "filter_by_status": "Filter by Status", "filter_by_tags": "Filter by Tags", "filter_by_type": "Filter by Type", "filter_deliveries": "Filter deliveries", "filter_selected_count_one": "{{count}} filter selected", "filter_selected_count_other": "{{count}} filters selected", "filtered_by": "Filtered by", "filtered_by_patient": "Filtered by <PERSON>ient", "filtering_by": "Filtering by {{name}}", "filters": "Filters", "final": "Final", "final_price": "Final price", "financial_overview": "Financial Overview", "first_name": "First Name", "fixed_amount": "Fixed Amount", "font_family": "Font Family", "font_size": "Font Size", "food": "Food", "footer_body": "Open Healthcare Network is an open-source public utility designed by a multi-disciplinary team of innovators and volunteers. Open Healthcare Network CARE is a Digital Public Good recognised by the United Nations.", "forget_password": "Forgot password?", "forget_password_instruction": "Enter your username, and if it exists, we will send you a link to reset your password.", "form_preview": "Form Preview", "format": "Format", "forms": "Forms", "frequency": "Frequency", "from": "from", "from_user": "from User", "fulfill_request_confirmation_message": "Dispatching this quantity is/already sufficient to complete the request. How would you like to proceed?", "fulfill_request_title": "Complete the Supply Request?", "fulfilled": "Fulfilled", "full_day_unavailable": "Full Day Unavailable", "full_history": "Full History", "full_name": "Full Name", "full_screen": "Full Screen", "gender": "Gender", "gender_is_required": "Gender is required", "general": "General", "general_info_detail": "Provide the patient's personal details, including name, date of birth, gender, and contact information for accurate identification and communication.", "generate": "Generate", "generate_discharge_summary": "Generate Discharge Summary", "generate_link_abha": "Generate/Link ABHA Number", "generate_qr": "Generate QR Code", "generate_qr_failed": "Failed to generate QR Code", "generate_report": "Generate Report", "generate_report_other": "Generate Reports", "generated_by": "Generated by:", "generated_on": "Generated on:", "generated_summary_caution": "This is a computer generated summary using the information captured in the CARE system.", "generating": "Generating", "generating_discharge_summary": "Generating discharge summary", "generating_qr": "Generating QR Code...", "geo_organization_is_required": "Geo organization is required when nationality is India", "geo_organization_required": "Geo organization is required", "geolocation_is_not_supported_by_this_browser": "Geolocation is not supported by this browser", "get_current_location": "Get Current Location", "get_tests": "Get Tests", "getting_location": "Getting Location...", "go_back": "Go Back", "go_to_account": "Go to account", "goal": "Our goal is to continuously improve the quality and accessibility of public healthcare services using digital tools.", "got_it": "Got it!", "govt": "Goverence", "granted_on": "Granted On", "graph": "Graph", "greater": "Greater Than", "greater_or_equals": "Greater Than or Equal", "group": "Group", "group_layout_options": "Group layout options", "gst": "GST", "handed_over_on": "Handed over on", "has_allergies": "Has Allergies", "has_child_locations": "Has child locations", "has_child_organizations": "Has child organizations", "has_children": "Has children", "has_domestic_healthcare_support": "Has domestic healthcare support?", "has_sari": "Has SARI (Severe Acute Respiratory illness)?", "has_sub_departments": "Has sub-departments", "header": "Header", "header_description": "Configure header elements and layout", "header_error": "There are errors in the Header tab. Please fix the errors before saving.", "health-profile": "Health Profile", "health_profile": "Health Profile", "healthcare_service_created_successfully": "Healthcare service created successfully", "healthcare_service_details": "Healthcare Service Details", "healthcare_service_not_found": "Healthcare service not found", "healthcare_service_updated_successfully": "Healthcare service updated successfully", "healthcare_services": "Healthcare Services", "hearing": "We are hearing you...", "help_confirmed": "There is sufficient diagnostic and/or clinical evidence to treat this as a confirmed condition.", "help_differential": "One of a set of potential (and typically mutually exclusive) diagnoses asserted to further guide the diagnostic process and preliminary treatment.", "help_entered_in_error": "The statement was entered in error and is not valid.", "help_provisional": "This is a tentative diagnosis - still a candidate that is under consideration.", "help_refuted": "This condition has been ruled out by subsequent diagnostic and clinical evidence.", "help_unconfirmed": "There is not sufficient diagnostic and/or clinical evidence to treat this as a confirmed condition.", "hey_user": "Hey {{user}} 👋", "hide": "<PERSON>de", "hide_notes": "Hide notes", "high": "High", "history": "History", "home": "Home", "home_facility": "Home Facility", "home_facility_cleared_success": "Home Facility cleared successfully", "home_facility_updated_error": "Error while updating Home Facility", "home_facility_updated_success": "Home Facility updated successfully", "hospital_identifier": "Hospital Identifier", "hospitalisation_details": "Hospitalization Details", "hospitalization_details": "Hospitalization Details", "hour": "hour", "hours": "Hours", "hover_focus_reveal": "Hover or focus to reveal", "how_does_this_work": "How does this work?", "hubs": "Hub Facilities", "i_declare": "I hereby declare that:", "icd11_as_recommended": "As per ICD-11 recommended by WHO", "icmr_specimen_referral_form": "ICMR Specimen Referral Form", "icon": "Icon", "id": "ID", "identifier": "Identifier", "identifier_config": "Identifier config", "identifier_details": "Identifier Details", "identifier_details_help": "Configure the core details for this patient identifier type.", "identifier_value": "Identifier value", "identifier_value_autogenerated": "Auto-generated on Save", "identifiers": "Identifiers", "igst": "IGST", "image": "Image", "image_cropped_successfully": "Image cropped successfully!", "image_size_error": "File size must be less than {{size}}MB", "image_uploaded_successfully": "Image uploaded successfully!", "imaging": "Imaging", "immunisation-records": "Immunisation", "import": "Import", "import_form": "Import Form", "import_from_file": "Import from File", "import_from_url": "Import from URL", "import_questionnaire": "Import Questionnaire", "importing": "Importing", "in": "in", "in_consultation": "In-Consultation", "in_progress": "In Progress", "in_stock": "In Stock", "inactive": "Inactive", "include_rules": "Include rules", "incoming": "Incoming", "incomplete_patient_details_warning": "Patient details are incomplete. Please update the details before proceeding.", "inconsistent_dosage_units_error": "Dosage units must be same", "indian_mobile": "Indian Mobile", "indicator": "Indicator", "individual_bed_names": "Individual Bed Names", "info": "Info", "information_source": "Information Source", "information_source_patient": "Patient", "information_source_related_person": "Related Person", "information_source_user": "Hospital Staff", "informational": "Informational", "informational_codes": "Informational Codes", "informational_explanation": "Display-only components that don't affect the final price", "inidcator_event": "Indicator Event", "initiate_encounter": "Initiate <PERSON><PERSON> Encounter", "inr": "₹", "instance": "Instance", "instance_order": "Instance Order", "instruction_on_titration": "Instruction on titration", "instructions": "Instructions", "insurance__insurer_id": "Insurer ID", "insurance__insurer_name": "Insurer Name", "insurance__member_id": "Member ID", "insurance__policy_name": "Policy ID / Policy Name", "insurance_details_detail": "Include details of all the Insurance Policies held by the Patient for smooth insurance processing", "insurer_name_required": "Insurer Name is required", "integer": "Integer", "intended_routes": "Intended Routes", "intent": "Intent", "internal_transfers": "Internal Transfers", "internal_type": "Internal Type", "international_mobile": "International Mobile", "interpretation": "Interpretation", "invalid_age": "Invalid age", "invalid_asset_id_msg": "Oops! The asset ID you entered does not appear to be valid.", "invalid_date": "Invalid date", "invalid_date_format": "Invalid date format, expected {{format}}", "invalid_email": "Please enter a valid email address", "invalid_email_address": "Invalid email address", "invalid_ip_address": "Invalid IP Address", "invalid_latitude": "Invalid latitude", "invalid_link_msg": "It appears that the password reset link you have used is either invalid or expired. Please request a new password reset link.", "invalid_longitude": "Invalid longitude", "invalid_otp": "Invalid OTP, Please check the OTP and try again", "invalid_password": "Password doesn't meet the requirements", "invalid_password_reset_link": "Invalid password reset link", "invalid_patient_data": "Invalid Patient Data", "invalid_pincode": "<PERSON><PERSON><PERSON>", "invalid_pincode_msg": "Please enter valid pincode", "invalid_quantity": "Please enter a valid quantity", "invalid_regex": "Invalid regular expression pattern.", "invalid_reset": "Invalid <PERSON>set", "invalid_url": "Please enter a valid url", "invalid_url_format": "Invalid URL format", "invalid_url_http_https": "URL should start with http:// or https://", "invalid_url_javascript": "URL should not include javascript, please enter a valid URL.", "invalid_username": "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.", "invalid_value": "Invalid value", "inventory": "Inventory", "inventory_management": "Inventory Management", "investigation_report": "Investigation Report", "investigation_report_for_{{name}}": "Investigation Report for {{name}}", "investigation_report_of_{{name}}": "Investigation Report of : {{name}}", "investigation_reports": "Investigation Reports", "investigations": "Investigations", "investigations__date": "Date", "investigations__ideal_value": "Ideal Value", "investigations__name": "Name", "investigations__range": "Value Range", "investigations__result": "Result", "investigations__unit": "Unit", "investigations_suggested": "Investigations Suggested", "investigations_summary": "Investigations Summary", "invoice": "Invoice", "invoice title": "Invoice Title", "invoice_actions": "Invoice Actions", "invoice_activity": "Invoice Activity", "invoice_alert_title": "Invoice Created", "invoice_amount": "Invoice Amount", "invoice_balanced": "Invoice Balanced", "invoice_cancelled": "Invoice Cancelled", "invoice_cancelled_successfully": "Invoice cancelled successfully", "invoice_created": "Invoice Created", "invoice_created_successfully": "Invoice Created Successfully", "invoice_date": "Invoice Date", "invoice_details": "Invoice Details", "invoice_entered_in_error": "Invoice entered in error", "invoice_issued": "Invoice Issued", "invoice_management": "Invoice Management", "invoice_may_not_exist": "Invoice may not exist", "invoice_not_found": "Invoice not found", "invoice_note_placeholder": "Enter Invoice Note", "invoice_number": "Invoice Number", "invoice_number_expression": "Invoice Number Expression", "invoice_number_expression_current_year_yy": "{current_year_yy}: Current year (2 digits)", "invoice_number_expression_current_year_yyyy": "{current_year_yyyy}: Current year (4 digits)", "invoice_number_expression_example_label": "Example:", "invoice_number_expression_example_value": "f'#INV-{invoice_count + 100}-{current_year_yy}'", "invoice_number_expression_invoice_count": "{invoice_count}: Auto-incremented Invoice count", "invoice_number_expression_other_characters": "Other Characters", "invoice_number_expression_supported_variables": "Supported variables:", "invoice_status": "Invoice Status", "invoice_timeline": "Invoice Timeline", "invoice_title": "Invoice Title", "invoice_title_placeholder": "Enter Invoice Title", "invoice_updated_successfully": "Invoice updated successfully", "invoices": "Invoices", "inward_entry": "Inward Purchase Entries", "ip_encounter": "IP Encounter", "ip_op_obs_emr_number": "Ip/op/obs/emr number", "is": "Is", "is_already_selected": "is already selected", "is_antenatal": "Is Antenatal", "is_atypical_presentation": "Is Atypical presentation", "is_component": "Is component", "is_declared_positive": "Whether declared positive", "is_derived": "Is Derived", "is_emergency": "Is emergency", "is_emergency_case": "Is emergency case", "is_fully_dispensed": "Is Fully Dispensed", "is_it_upshift": "is it upshift", "is_phone_a_whatsapp_number": "Is the phone number a WhatsApp number?", "is_pregnant": "Is pregnant", "is_required": "is required", "is_substituted_with": "is substituted with", "is_the_patient_deceased": "Is the patient deceased", "is_this_administration_for_a_past_time": "Is this administration for a past time", "is_this_an_emergency": "Is this an Emergency?", "is_this_an_emergency_request": "Is this an emergency request?", "is_this_an_upshift": "Is this an upshift?", "is_unusual_course": "Is unusual course", "is_up_shift": "Is up shift", "is_upshift_case": "Is upshift case", "is_vaccinated": "Whether vaccinated", "issue_date": "Issue Date", "issue_invoice": "Issue Invoice", "issued": "Issued", "issuer_type": "Issuer Type", "item": "<PERSON><PERSON>", "item_condition": "Item Condition?", "item_marked_as_abandoned": "Item marked as abandoned successfully", "item_marked_as_entered_in_error": "<PERSON><PERSON> marked as entered in error successfully", "item_marked_as_received": "Item marked as received successfully", "item_name": "Item Name", "item_received": "<PERSON><PERSON>", "item_requested": "<PERSON>em Requested", "items": "Items", "items_section": "Items", "items_selected": "items selected", "items_to_request": "Items to Request", "json_files_only": "Please select a JSON file", "keep_code_safe": "Keep these codes safe. Each code can only be used once.", "kind": "Kind", "lab_tests": "Lab Tests", "label": "Label", "labels": "Labels", "laboratory": "Laboratory", "landing_search_placeholder": "Enter {{orgType}} name to find facilities.", "landline": "Indian landline", "landmark": "Landmark", "language_selection": "Language Selection", "languages": "Languages", "last_administered": "Last administered", "last_discharge_reason": "Last Discharge Reason", "last_edited": "Last Edited", "last_login": "Last Login", "last_modified": "Last Modified", "last_modified_by": "Last Modified By", "last_name": "Last Name", "last_occurrence": "Last Occurrence", "last_online": "Last Online", "last_serviced_on": "Last Serviced On", "last_updated": "Last Updated", "last_updated_by": "Last updated by", "last_vaccinated_on": "Last Vaccinated on", "last_week_short": "Last wk", "latitude": "Latitude", "latitude_invalid": "Latitude must be between -90 and 90", "layout": "Layout", "layout_description": "Configure page size, margins, numbering and text settings", "layout_error": "There are errors in the Layout tab. Please fix the errors before saving.", "left": "Left", "length": "Length ({{unit}})", "less": "Less Than", "less_or_equals": "Less Than or Equal", "level_inside": "Level Inside", "license": "License", "licenses_description": "Third-party software is used in Care, including the respective licenses         and versions.", "licenses_title": "Third-Party Software and Licenses", "link_abha_number": "Link ABHA Number", "link_abha_profile": "Link ABHA Profile", "link_camera_and_bed": "Link bed to Camera", "link_consent": "Link <PERSON>", "link_consent_description": "Create a new consent for this encounter", "link_existing_abha_profile": "Already have an ABHA number", "link_facility_error": "Error while linking facility. Try again later.", "link_id": "Link ID", "link_id_placeholder": "Unique identifier for this question", "link_to_organization": "Link to Organization", "link_user": "Link User", "link_user_to_facility": "Link User to Facility", "link_user_to_facility_description": "Search for a user and assign a role to add them to the facility.", "link_user_to_organization": "Link User to Organization", "link_user_to_organization_description": "Search for an existing user and assign a role to link them to the organization.", "linked_facilities": "Linked Facilities", "linked_facilities_note": "Add or remove facilities and set or change the Home Facility", "linked_patient": "Linked Patient", "linked_patient_details": "Linked Patient Details", "linked_skills": "Linked Skills", "linked_skills_note": "Search and select skills to add to the skill set", "liquid_oxygen_capacity": "Liquid Oxygen Capacity", "list": "List", "list_view": "List View", "litres": "Litres", "litres_per_day": "Litres/day", "live": "Live", "live_monitoring": "Live Monitoring", "live_patients_total_beds": "Live Patients / Total beds", "load_more": "Load More", "loading": "Loading...", "loading_encounter": "Loading encounter...", "loading_healthcare_service": "Loading healthcare service...", "loading_more": "Loading more...", "loading_observation_definition": "Loading observation definition...", "loading_organizations": "Loading organizations...", "local_body": "Local body", "local_ip_address": "Local IP Address", "local_ip_address_example": "e.g. *************", "location": "Location", "location_associated_successfully": "Location associated successfully", "location_association_created_successfully": "Location association created successfully", "location_association_updated_successfully": "Location association updated successfully", "location_beds_empty": "No beds available in this location", "location_created": "Location Created", "location_description": "Location Description", "location_details": "Location Details", "location_disassociated_successfully": "Location disassociated successfully", "location_form": "Location Form", "location_form__area": "Area", "location_form__bd": "Bed", "location_form__bu": "Building", "location_form__ca": "Cabinet", "location_form__co": "Corridor", "location_form__ho": "House", "location_form__jdn": "Juris<PERSON>", "location_form__lvl": "Level", "location_form__rd": "Road", "location_form__ro": "Room", "location_form__si": "Site", "location_form__ve": "Vehicle", "location_form__vi": "Virtual", "location_form__wa": "Ward", "location_form__wi": "Wing", "location_history": "Location History", "location_info": "Location Information", "location_info_note": "View or update user's location information", "location_info_note_self": "View or update your location information", "location_info_note_view": "View user's location information", "location_management": "Location Management", "location_name": "Location Name", "location_order_updated": "Location order updated successfully", "location_removed_successfully": "Location removed successfully", "location_requirements": "Locations", "location_requirements_description": "Location where the the activity is being performed.", "location_status": "Location Status", "location_status__active": "Active", "location_status__inactive": "Inactive", "location_status__unknown": "Unknown", "location_updated": "Location Updated", "location_updated_successfully": "Location updated successfully", "locations": "Locations", "locations_selected": "locations selected", "locations_under_my_care_for_immediate_transfer": "Locations under my care for immediate transfer", "lock_ratios": "Lock Ratios", "log_in_again": "Log in again", "log_lab_results": "Log Lab Results", "log_report": "Log Report", "log_update": "Log Update", "log_updates": "Log Updates", "logged_by": "Logged by", "logged_in_as": "Logged in as", "login": "<PERSON><PERSON>", "login_already_registered": "Already registered? Log in to your account.", "logout": "Log Out", "loinc_code": "LOINC Code", "longitude": "Longitude", "longitude_invalid": "Longitude must be between -180 and 180", "lot": "Lot", "lot_batch_no": "Lot/Batch No.", "lot_number": "Lot Number", "lot_number_description": "Manufacturing lot or batch number", "low": "Low", "low_stock": "Low Stock", "lsg": "Lsg", "make_facility_public": "Make this facility public", "make_facility_public_description": "When enabled, this facility will be visible to the public and can be discovered by anyone using the platform", "make_multiple_beds_label": "Do you want to make multiple beds?", "male": "Male", "manage_activity_definitions": "Manage Activity Definitions", "manage_and_view_questionnaires": "Manage and View Questionnaires", "manage_and_view_roles": "Manage and view roles", "manage_and_view_valuesets": "Manage and View ValueSets", "manage_bed_presets": "Manage Presets of Bed", "manage_care_team": "Manage Care Team", "manage_charge_item_definitions": "Manage pricing definitions for services and items", "manage_consents": "Manage Consents", "manage_consents_description": "View and manage consents for the patient", "manage_facility_users": "Manage encounters", "manage_healthcare_services": "Manage healthcare services for your facility", "manage_instance_patient_identifier_config": "Manage instance patient identifier config", "manage_my_schedule": "Manage my schedule", "manage_observation_definitions": "Manage and view observation definitions", "manage_organization_description_one": "Add or remove organization from this {{entityType}}", "manage_organization_description_other": "Add or remove organizations from this {{entityType}}", "manage_organization_one": "Manage Organization", "manage_organization_other": "Manage Organizations", "manage_organizations_description": "Add or remove organizations from this questionnaire", "manage_patient_identifier_config": "Manage Patient Identifier Config", "manage_patient_identifiers": "Manage patient identifiers", "manage_patient_location_and_transfers": "Manage patient location and transfers", "manage_prescriptions": "Manage Prescriptions", "manage_preset": "Manage preset {{ name }}", "manage_product_knowledge": "Manage Product Knowledge", "manage_products": "Manage your product inventory", "manage_roles_and_permissions": "Manage Roles and Permissions", "manage_service_requests": "View and manage service requests", "manage_specimen_definitions": "Manage and view Specimen Definitions", "manage_tag_config_description": "Manage tag configuration", "manage_tags": "Manage Tags", "manage_tags_description": "Add or remove tags for this questionnaire", "manage_tags_for_entity": "Manage tags for {{entity}}", "manage_user": "Manage User", "managing_organization": "Managing Organization", "manufacture_date": "Manufacture Date", "manufacture_date_cannot_be_in_future": "Manufacture date cannot be in future", "manufactured": "Manufactured", "manufacturer": "Manufacturer", "map": "Map", "map_acronym": "M.A.P.", "marital_status": "Marital Status", "mark": "<PERSON>", "mark_active": "<PERSON>", "mark_all_as_read": "<PERSON> all as <PERSON>", "mark_as_abandoned": "<PERSON> as Abandoned", "mark_as_active": "<PERSON> as Active", "mark_as_already_given": "<PERSON> as <PERSON> Given", "mark_as_balanced": "<PERSON> as balanced", "mark_as_cancelled": "Mark as cancelled", "mark_as_complete": "Mark as Complete", "mark_as_completed": "<PERSON> as Completed", "mark_as_damaged": "<PERSON> as Damaged", "mark_as_entered_in_error": "<PERSON> as entered in error", "mark_as_error": "Mark as error", "mark_as_fulfilled": "<PERSON> as Fullfilled", "mark_as_fully_dispatched": "<PERSON> as fully dispatched", "mark_as_fully_received": "<PERSON> as <PERSON><PERSON>", "mark_as_invalid": "<PERSON> as <PERSON><PERSON><PERSON>", "mark_as_noshow": "<PERSON> as no-show", "mark_as_noshow_warning": "This action cannot be undone. The appointment will be marked No-Show and the patient will be notified.", "mark_as_not_received": "<PERSON> as not Received", "mark_as_ordered": "<PERSON> as Ordered", "mark_as_primary": "<PERSON> as Primary", "mark_as_read": "<PERSON> <PERSON>", "mark_as_received": "<PERSON> as Received", "mark_as_unread": "<PERSON> as Unread", "mark_encounter_as_complete_confirmation": "Are you sure you would like to mark this encounter as complete?", "mark_for_discharge": "Mark for discharge", "mark_inactive": "<PERSON>", "mark_resolved": "<PERSON>solved", "mark_this_as_error": "mark this as an error", "mark_this_transfer_as_complete_question": "Are you sure you want to mark this transfer as complete? The Origin facility will no longer have access to this patient", "mark_transfer_complete_confirmation": "Are you sure you want to mark this transfer as complete? The Origin facility will no longer have access to this patient", "markdown_supported": "You can use markdown to format your facility description", "max_dosage_24_hrs": "Max. dosage in 24 hrs.", "max_dosage_in_24hrs_gte_base_dosage_error": "Max. dosage in 24 hours must be greater than or equal to base dosage", "max_size_for_image_uploaded_should_be": "Max size for image uploaded should be {{maxSize}}.", "measured_after": "Measured after", "measured_before": "Measured before", "medical": "Medical", "medical_council_registration": "Medical Council Registration", "medical_council_registration_required": "Medical Council Registration is required", "medical_records": "Medical Records", "medical_worker": "Medical Worker", "medication": "Medication", "medication_administration_saved": "Medicine Administration saved", "medication_already_marked_as_error": "Medication already marked as entered in error", "medication_dispense": "Medication Dispense", "medication_dispense_details": "Medication Dispense Details", "medication_dispense_invoice_alert": "Dispense Medicine now", "medication_history": "Medication History", "medication_list": "Medication List", "medication_request_status_updated_successfully": "Medication request status updated successfully", "medication_requests": "Medication Requests", "medication_statements": "Medication Statements", "medication_status__active": "Active", "medication_status__cancelled": "Cancelled", "medication_status__completed": "Completed", "medication_status__draft": "Draft", "medication_status__ended": "Ended", "medication_status__entered_in_error": "Entered in Error", "medication_status__intended": "Intended", "medication_status__not_taken": "Not Taken", "medication_status__on_hold": "On Hold", "medication_status__stopped": "Stopped", "medication_status__unknown": "Unknown", "medication_taken_between": "Medication Taken Between", "medications": "Medications", "medications_billed_successfully": "Medications billed successfully", "medications_dispense": "Medications Dispense", "medications_with_product": "Medications with Product", "medicine": "Medicine", "medicine_administration": "Medicine Administration", "medicine_administration_history": "Medicine Administration History", "medicine_dispensed": "Medicine Dispensed", "medicine_prescription": "Medicine Prescription", "medicines": "Medicines", "medicines_administered": "Medicine(s) administered", "medicines_administered_error": "Error administering medicine(s)", "member_added_successfully": "Member added successfully", "member_already_added": "Member already added", "member_id_required": "Member Id is required", "member_removed_successfully": "Member removed successfully", "messages": "Messages", "metadata": "<PERSON><PERSON><PERSON>", "method": "Method", "method_placeholder": "Select Method", "microphone_permission_denied": "Microphone permission denied", "middleware_hostname": "Middleware Hostname", "middleware_hostname_example": "e.g. example.ohc.network", "middleware_hostname_sourced_from": "Middleware hostname sourced from {{ source }}", "mild": "Mild", "min_char_length_error": "Must be at least {{ min_length }} characters", "min_password_len_8": "Minimum password length 8", "min_time_bw_doses": "Min. time b/w doses", "minimal_patient_registration_geo_organization_required": "Atleast one level geo-organization is required", "minimize": "Minimize", "minimum_volume": "Minimum Volume", "missing_required_params_for_patient_verification": "Missing required parameters for patient verification", "mobile": "Mobile", "mobile_number": "Mobile Number", "mobile_number_validation_error": "Enter a valid mobile number", "mobile_otp_send_error": "Failed to send <PERSON><PERSON>. Please try again later.", "mobile_otp_send_success": "OTP has been sent to the given mobile number.", "mobile_otp_verify_error": "Failed to verify mobile number. Please try again later.", "mobile_otp_verify_success": "Mobile number has been verified successfully.", "model_number": "Model Number", "moderate": "Moderate", "modification_caution_note": "No modifications possible once added", "modified": "Modified", "modified_date": "Modified Date", "modified_on": "Modified On", "monitor": "Monitor", "month": "Month", "more": "More", "more_details": "More details", "more_files_count_one": "{{count}} more file", "more_files_count_other": "{{count}} more files", "more_info": "More Info", "morning_slots": "Morning Slots", "mov_file_not_supported": "MOV file not supported in this browser", "mov_file_safari_only": "MOV files are only supported in Safari browser. Please use Safari to view this file or download it to play locally.", "move": "Move", "move_down": "Move Down", "move_item": "Move Item", "move_left": "Move Left", "move_questions": "Move Questions", "move_questions_count": "Move <strong>{{count}}</strong> questions", "move_right": "Move Right", "move_this_to_draft": "move this to draft", "move_to_another_bed": "Move to Another Bed", "move_to_draft": "Move to Draft", "move_to_next_page": "Move to next page", "move_to_onvif_preset": "Move to an ONVIF Preset", "move_to_previous_page": "Move to previous page", "move_to_specific_position": "Move to specific position", "move_up": "Move Up", "moving": "Moving", "moving_camera": "Moving Camera", "mrp": "MRP", "my_doctors": "My Doctors", "my_organizations": "My Organizations", "my_profile": "My Profile", "my_schedules": "My Schedules", "na": "N/A", "name": "Name", "name_and_slug_are_required": "Name and slug are required", "name_is_required": "Name is required", "name_of_hospital": "Name of Hospital", "name_of_shifting_approving_facility": "Name of shifting approving facility", "name_type": "Name Type", "names": "Names", "nationality": "Nationality", "nationality_is_required": "Nationality is required", "navigation": "Navigation", "nearby_facilities": "Nearby Facilities", "net_amount": "Net Amount", "net_content": "Net Content", "network_failure": "Network Failure. Please check your internet connectivity.", "never": "Never", "never_logged_in": "Never Logged In", "new_backup_codes": "New Backup Codes", "new_drawing": "New Drawing", "new_password": "New Password", "new_password_confirmation": "Confirm New Password", "new_password_same_as_old": "Your new password <strong>must not match the old password </strong> ", "new_password_validation": "New password is not valid.", "new_section": "New Section", "new_service_request": "New Request", "new_session": "New Session", "new_text": "New Text", "next": "Next", "next_file": "Next file", "next_month": "Next month", "next_sessions": "Next Sessions", "next_week_short": "Next wk", "no": "No", "no_accounts_found": "No accounts found", "no_active_account_found": "No active account found", "no_active_beds_assigned": "No active beds assigned", "no_active_encounters_found": "No active encounters found", "no_active_medication_recorded": "No Active Medication Recorded", "no_activity_definitions_found": "No activity definitions found", "no_activity_recorded": "No activity recorded", "no_address": "No address provided", "no_address_provided": "No address provided", "no_allergies": "No Allergies", "no_allergies_recorded": "No allergies recorded", "no_allergies_recorded_description": "No Allergies have been recorded", "no_alternative_names_added": "No alternative names added", "no_appointments": "No appointments found", "no_attachments_found": "This communication has no attachments.", "no_availabilities_yet": "No availabilities yet", "no_available_beds_found": "No available beds found", "no_bed_asset_linked_allocated": "No bed/asset linked allocated", "no_bed_types_found": "No bed types found", "no_beds_available": "No beds available", "no_beds_found": "No beds found", "no_billable_items": "No Billable Items Found", "no_care_team_members": "No care team members", "no_changes": "No changes", "no_changes_made": "No changes made", "no_changes_to_save": "No changes to save", "no_charge_definitions_found": "No charge definitions found", "no_charge_item_definitions_found": "No charge item definitions found", "no_charge_items": "No charge items found", "no_child_locations": "No Child Locations", "no_child_locations_found": "No child locations found", "no_child_tags_found": "No child tags found", "no_child_tags_found_description": "Looks like you haven't created a child Tag", "no_columns": "No columns selected.", "no_comments_available": "No comments available", "no_completed_encounters_found": "No completed encounters found", "no_conclusion_entered": "No conclusion entered", "no_configs_found": "No configs found", "no_consent_description": "Add a new consent by clicking on + Add Consent", "no_consent_found": "No Consents found", "no_consultation_filed": "No consultation filed", "no_consultation_updates": "No consultation updates", "no_contact_points_added": "No contact points added", "no_country_found": "No country found", "no_data_found": "No data found", "no_definitions_found": "No definitions found", "no_deleted_observations": "No Deleted Observation", "no_deleted_observations_description": "No observations were deleted / No observation history found.", "no_deliveries_dispatched_for_this_supply_request": "No deliveries dispatched for this {{action}}, Please check again later", "no_deliveries_found": "No delivery found", "no_deliveries_found_description": "No deliveries found for selected status or product.", "no_departments_assigned": "No departments assigned", "no_departments_teams_found": "No Departments or Teams found", "no_description": "No Description", "no_devices_available": "No devices available", "no_devices_found": "No devices found", "no_devices_matching_filters": "No devices match your search criteria", "no_diagnoses": "No Diagnoses", "no_diagnoses_recorded": "No diagnoses recorded", "no_diagnoses_recorded_description": "No Diagnoses have been recorded", "no_diagnostic_reports_found": "No Diagnostic Reports Found", "no_discharge_summaries_found": "No Discharge Summaries found", "no_discount_codes": "No discount codes found", "no_discount_components": "No discount components available for this facility", "no_dispenses_found": "No dispenses made", "no_dispenses_found_description": "No medication dispenses has been made in this location! You'll see them here once dispensed.", "no_doctors_found": "No Doctors Found", "no_dosage_instrctions_available": "No dosage instructions available", "no_drawings_so_far": "No drawings so far", "no_duplicate_facility": "You should not create duplicate facilities", "no_encounter_associated": "No encounter associated", "no_encounter_history": "No encounter history available", "no_encounters_found": "No encounters found", "no_extra_details": "No additional details provided", "no_facilities": "No Facilities found", "no_facilities_found": "No facilities found", "no_fields": "No fields selected.", "no_file_available": "No file available for this consent", "no_files_attached": "No files attached", "no_files_found": "No {{type}} files found", "no_filters_selected": "No filters selected", "no_healthcare_services_found": "No healthcare services found", "no_home_facility": "No home facility", "no_home_facility_found": "No home facility found", "no_icons_found": "No icons found", "no_image_found": "No image found", "no_informational_codes": "No informational codes", "no_instructions_selected": "No instructions selected", "no_inventory": "No inventory items found", "no_inventory_description": "There are no inventory items in this location yet or try adjusting your filters", "no_investigation": "No investigation Reports found", "no_investigation_suggestions": "No Investigation Suggestions", "no_invoices": "No invoices", "no_invoices_found": "No invoices found", "no_items_added_yet": "No items added yet.", "no_items_dispensed_yet": "No items have been dispensed yet.", "no_items_found": "No items found", "no_items_selected": "No items selected", "no_linked_facilities": "No Linked Facilities", "no_location": "No location set", "no_location_associated": "No location associated", "no_location_description": "This device is not currently assigned to any location", "no_location_found": "No location found", "no_location_history_available": "No location history available", "no_locations": "No locations", "no_locations_assigned": "No locations assigned to this service", "no_locations_available": "No locations available", "no_locations_found": "No locations found", "no_log_update_delta": "No changes since previous log update", "no_log_updates": "No log updates found", "no_lots_found": "No lots found", "no_matches_found": "No matches found", "no_matching_discount_codes": "No discount codes matches this search", "no_matching_discount_components": "No discount components matches this search", "no_matching_informational_codes": "No matching informational codes", "no_matching_tax_codes": "No matching tax codes found", "no_matching_tax_components": "No matching tax components found", "no_medical_history_available": "No Medical History Available", "no_medication_recorded": "No Medication Recorded", "no_medication_statements": "No Medication Statements", "no_medication_statements_recorded_description": "No Medication Statements have been recorded", "no_medications": "No Medications", "no_medications_found": "No Medications Found", "no_medications_found_description": "No medications have been dispensed yet", "no_medications_found_for_this_encounter": "No medications found for this encounter.", "no_medications_match_query": "No medications match {{ searchQuery }}", "no_medications_prescribed": "No medications have been prescribed yet", "no_medications_recorded_description": "No Medications have been recorded", "no_medications_to_administer": "No medications to administer", "no_note_provided": "No note provided", "no_notes": "No notes available", "no_notices_for_you": "No notices for you.", "no_observation_definition_found": "No observation definition found", "no_observation_definitions_found": "No observation definitions found", "no_observations": "No Observations", "no_observations_entered": "No observations entered", "no_occurrence_set": "No occurrence set", "no_organization_added_yet_one": "No organization added yet", "no_organization_added_yet_other": "No organizations added yet", "no_organization_associated": "No organization associated", "no_organization_found": "No organization found", "no_organizations_found": "No Organizations Found", "no_organizations_selected": "No organizations selected", "no_patient_record_found": "No Patient Records Found", "no_patient_record_text": "No existing records found with this phone number. Would you like to register a new patient?", "no_patients": "No patients found", "no_patients_found": "No Patients Found", "no_patients_found_phone_number": "No patients found with this phone number. Please create a new patient to proceed with booking appointment.", "no_patients_to_show": "No patients to show.", "no_payments": "No payments", "no_payments_recorded": "No payments recorded", "no_permission_to_view_page": "You do not have permissions to view this page", "no_plots_configured": "No plots configured", "no_policy_added": "No Insurance Policy Added", "no_policy_found": "No Insurance Policy Found for this Patient", "no_practitioners_found": "No practitioners found", "no_prescriptions": "No prescriptions", "no_prescriptions_found": "No prescriptions found", "no_presets": "No Presets", "no_preview_available": "No preview available", "no_price_components": "No Price Components", "no_product_definition_added": "No product definition added", "no_product_knowledge_found": "No product knowledges found!", "no_products_added": "No products added", "no_products_found": "No products found", "no_purchase_orders_found": "No purchase orders found", "no_purchase_orders_found_description": "No purchase order found for selected status or priority.", "no_questionnaire_responses": "No Questionnaire Responses", "no_questionnaires_found": "No questionnaires found", "no_questions_selected": "No questions selected", "no_reason_provided": "No reason provided", "no_records_found": "No Records Found", "no_remarks": "No remarks", "no_requested_questionnaires_found": "The requested questionnaire could not be found.", "no_requests_found": "No requests found", "no_requests_found_description": "There are no supply requests matching your filters", "no_resource_requests_found": "No requests found", "no_resources_found": "No resources found", "no_results": "No results", "no_results_found": "No Results Found", "no_results_found_for": "No results found for {{term}}", "no_roles_found": "No roles found", "no_routes_added": "No routes added", "no_schedule_templates_found": "No schedule templates found for this month.", "no_scheduled_exceptions_found": "No scheduled exceptions found", "no_sections": "No sections added. Click \"Add Section\" to begin configuring your report sections.", "no_service_requests_found": "No service requests found", "no_services_found": "No services found", "no_slots": "No slots", "no_slots_available": "No slots available", "no_slots_available_for_this_date": "No slots available for this date", "no_slots_found": "No slots found", "no_social_profile_details_available": "No Social Profile Details Available", "no_specimens_available": "No Specimens Available", "no_specimens_available_description": "There are no specimens available to print QR codes for.", "no_staff": "No staff found", "no_starred": "Use the {{star}} icon to add to starred", "no_status_found": "No status found", "no_stock": "No Stock", "no_storage_guidelines_added": "No storage guidelines added", "no_sub_organizations_found": "No sub-organizations found", "no_suppliers_found": "No suppliers found", "no_supply_deliveries_found": "No Supply Deliveries Found", "no_supply_deliveries_found_description": "There are no supply deliveries matching your search criteria.", "no_supply_request_found_for_delivery": "No supply request found for this delivery", "no_supply_requests_found": "No supply requests found", "no_supply_requests_found_description": "No supply requests found for this location", "no_symptoms": "No Symptoms", "no_symptoms_recorded": "No symptoms recorded", "no_symptoms_recorded_description": "No symptoms have been recorded", "no_tag_configs_found": "No tag configs found", "no_tags": "No tags", "no_tags_assigned": "No tags assigned", "no_tags_found": "No tags found", "no_tags_group": "No tags or groups found", "no_tags_selected": "No tags selected", "no_tax_codes": "No tax codes found", "no_tax_components": "No tax components found", "no_templates_found": "No templates found", "no_test_results_recorded": "No test results have been recorded yet. Click 'Create Report' to add test results", "no_tests_taken": "No tests taken", "no_text": "No text added", "no_treating_physicians_available": "This facility does not have any home facility doctors. Please contact your admin.", "no_update_available": "No update available", "no_updates_found": "No updates found", "no_user_assigned": "No User Assigned to this patient", "no_users_found": "No Users Found", "no_valuesets_found": "No value sets found", "no_vendor_found": "No vendor found", "no_vitals_present": "No Vitals Monitor present in this location or facility", "non_fulfilled": "Non Fulfilled", "none": "None", "nonstock": "Non Stock", "normal": "Normal", "noshow": "No-show", "not_billable": "Not Billable", "not_done": "Not Done", "not_eligible": "Not Eligible", "not_equals": "Not Equals", "not_found": "Not Found", "not_included_in_actual_calculation": "Not included in the actual calculation", "not_performed_reason": "Not performed reason", "not_specified": "Not Specified", "not_started": "Not started", "note": "Note", "notes": "Notes", "notes__all_discussions": "All Discussions", "notes__all_discussions_description": "View and manage encounternotes discussion threads", "notes__be_first_to_send": "Be the first to send a message", "notes__choose_template": "Choose a template or enter a custom title", "notes__create_discussion": "Create a new discussion thread to organize your conversation topics.", "notes__discussions": "Discussions", "notes__enter_discussion_title": "Enter discussion title...", "notes__failed_create_thread": "Failed to create thread", "notes__failed_send_message": "Failed to send message", "notes__inactive_encounter": "You cannot start a new discussion for this encounter as it is {{encounterStatus}}.", "notes__new": "New", "notes__no_discussions": "No discussions yet", "notes__no_unused_threads": "Please enter a custom title for thread", "notes__select_create_thread": "Select or create a thread to start messaging", "notes__start_conversation": "Start the Conversation", "notes__start_new_discussion": "Start New Discussion", "notes__thread_created": "Thread created successfully", "notes__type_message": "Type your message...", "notes__welcome": "Welcome to Discussions", "notes__welcome_description": "Start a new discussion or select an existing thread to begin messaging", "notes_placeholder": "Type your Notes", "notice_board": "Notice Board", "notification_cancelled": "Notification cancelled", "notification_permission_denied": "Notification permission denied", "notification_permission_granted": "Notification permission granted", "notify": "Notify", "now": "now", "now_using_the_latest_version_of_care": "Now using the latest version of CARE", "number_min_error": "Must be greater than {{min}}", "number_of_aged_dependents": "Number of Aged Dependents (Above 60)", "number_of_beds": "Number of beds", "number_of_beds_out_of_range_error": "Number of beds cannot be greater than 100", "number_of_chronic_diseased_dependents": "Number Of Chronic Diseased Dependents", "number_of_covid_vaccine_doses": "Number of Covid vaccine doses", "number_of_slots": "Number of slots:", "nurse": "Nurse", "nurses_form": "Nurse's Form", "nursing_care": "Nursing Care", "nursing_home": "Nursing Home", "nursing_information": "Nursing Information", "nutrition": "Nutrition", "nutrition_product": "Nutrition Product", "nutritional_product": "Nutritional Product", "observation": "Observation", "observation_additional_details": "Specify additional details about how and where this observation is performed", "observation_basic_information": "Enter the basic details and type of the observation", "observation_components_description": "Add components if this observation needs to collect multiple related values", "observation_definition_components_description": "List of components that make up this observation definition", "observation_definition_created": "Observation definition created successfully", "observation_definition_not_found": "Observation defination not found", "observation_definition_updated": "Observation definition updated successfully", "observation_definitions": "Observation Definitions", "observation_delete_confirmation": "Are you sure you want to delete this observation? This action cannot be undone.", "observation_deleted": "Observation has been deleted successfully", "observation_history": "Observation Histroy", "observation_requirements": "Observation Requirements", "observation_result_requirements": "Observation Result Requirements", "observation_result_requirements_description": "Observation that are the requird to be collected.", "observations": "Observations", "occupancy": "Occupancy", "occupation": "Occupation", "occupied": "Occupied", "occupied_beds": "Occupied beds", "occupied_selected": "Occupied Selected", "occurrence": "Occurrence", "old_password": "Current Password", "older": "Older", "on": "on", "on_emergency_basis": " on emergency basis", "on_hold": "On Hold", "once_delivery_is_completed_you_can_not_change_the_status": "Once delivery is completed, you can not change the status.", "ongoing": "Ongoing", "ongoing_medicines": "Ongoing Medicines", "online": "Online", "only_indian_mobile_numbers_supported": "Currently only Indian numbers are supported", "only_mark_if_applicable": "Only mark if applicable", "onset": "Onset", "onset_date": "Onset Date", "op_encounter": "OP Encounter", "op_file_closed": "OP file closed", "op_token": "OP TOKEN", "open": "Open", "open_camera": "Open Camera", "open_in_browser": "Open in browser", "open_live_monitoring": "Open Live Monitoring", "operational_status": "Operational Status", "operator": "Operator", "option_value": "Option Value", "optional": "Optional", "or": "or", "or_enter_manually_below": "or enter the ID manually", "oral_issue_for_non_oral_nutrition_route_error": "Can be specified only if nutrition route is set to Oral", "order": "Order", "ordered": "Ordered", "ordering": "Ordering", "organization": "Organization", "organization_access_help": "Organizations help you manage facilities, users, and resources efficiently. Contact your administrator to get access.", "organization_added_successfully": "Organization added successfully", "organization_created_successfully": "Organization created successfully", "organization_deleted_successfully": "Organization deleted successfully", "organization_for_care_support": "Organization for Care Support", "organization_forbidden": "You don't have access to any organizations yet.", "organization_not_found": "No Organizations Found", "organization_permission_info": "The selected department will be granted access to view and manage this encounter", "organization_removed_successfully": "Organization removed successfully", "organization_required": "Organization is required", "organization_selection_required": "At least one organization must be selected", "organization_updated_successfully": "Organization updated successfully", "organizations": "Organizations", "organizations_fetch_error": "Error while fetching organizations", "origin": "Origin", "origin_facility": "Current facility", "original_medication": "Original Medication", "original_name": "Original Name", "original_order": "Original Order", "other": "Other", "other_consultants": "Other Consultants", "other_details": "Other details", "other_medications": "Other Medications", "otp_verification_error": "Failed to verify <PERSON><PERSON>. Please try again later.", "otp_verification_success": "OTP has been verified successfully.", "out": "out", "out_of_range_error": "Value must be between {{ start }} and {{ end }}.", "outcome": "Outcome", "outgoing": "Outgoing", "outstanding": "Outstanding", "overpaid_amount": "Overpaid amount to be refunded", "overview": "Overview", "oxygen_information": "Oxygen Information", "packages": "Packages", "page_load_error": "Couldn't Load the Page", "page_margin": "<PERSON>", "page_not_found": "Page Not Found", "page_number_format": "Page number format", "page_number_format_help": "Use {page} for current page number and {total} for total pages", "page_numbering": "Page Numbering", "page_size": "<PERSON>", "paid": "Paid", "paid_via": "Paid via", "pain": "Pain", "pain_chart_description": "Mark region and intensity of pain", "parent_tag": "Parent Tag", "part_number": "Part Number", "partially_billed": "Partially Billed", "partially_dispensed": "Partially Dispensed", "participants": "Participants", "passport_number": "Passport Number", "password": "Password", "password_length_validation": "Use at least <strong>8 characters</strong>", "password_lowercase_validation": "Include at least <strong>one lowercase letter</strong> (a-z)", "password_mismatch": "Passwords do not match", "password_number_validation": "Include at least <strong>one number</strong> (0-9)", "password_required": "Password is required", "password_reset_failure": "Password Reset Failed", "password_reset_success": "Password Reset successfully", "password_sent": "Password Reset Email <PERSON>", "password_setup_method": "Password Setup Method", "password_success_message": "All set! Your password is strong", "password_update_error": "Error while updating password. Try again later.", "password_updated": "Password updated successfully", "password_uppercase_validation": "Include at least <strong>one uppercase letter</strong> (A-Z).", "passwords_match": "Passwords match.", "past_accounts": "Past Accounts", "past_allergies": "Past Allergies", "past_diagnoses": "Past Diagnoses", "past_encounters": "Past Encounters", "past_medications": "Past Medications", "past_prescriptions": "Past Prescriptions", "past_symptoms": "Past Symptoms", "patient": "Patient", "patient-notes": "Notes", "patient__general-info": "General Info", "patient__identifiers": "Identifiers", "patient__insurance-details": "Insurance Details", "patient__social-profile": "Social Profile", "patient__tags": "Patient Tags", "patient__volunteer-contact": "Volunteer Contact", "patient_address": "Patient Address", "patient_and_billing_details": "Patient and billing details", "patient_birth_year_for_identity": "Please enter the patient's year of birth to verify their identity", "patient_body": "Patient Body", "patient_care": "Patient Care", "patient_category": "Patient Category", "patient_clinical_history_page_title": "{{ name }}'s clinical history", "patient_consultation__admission": "Date of admission", "patient_consultation__consultation_notes": "General Instructions", "patient_consultation__dc_admission": "Date of domiciliary care commenced", "patient_consultation__ip": "IP", "patient_consultation__op": "OP", "patient_consultation__special_instruction": "Special Instructions", "patient_consultation__treatment__plan": "Plan", "patient_consultation__treatment__summary": "Summary", "patient_consultation__treatment__summary__date": "Date", "patient_consultation__treatment__summary__spo2": "SpO2", "patient_consultation__treatment__summary__temperature": "Temperature", "patient_count_help": "Auto-incremented patient count", "patient_created": "Patient Created", "patient_created_successfully": "Patient created successfully", "patient_dashboard": "Patient Dashboard", "patient_details": "Patient Details", "patient_details_incomplete": "Patient Details Incomplete", "patient_encounters": "Patient Encounters", "patient_face": "Patient Face", "patient_files": "Patient Files", "patient_id": "Patient ID", "patient_identifier_config": "Patient Identifier Config", "patient_information": "Patient Information", "patient_instruction": "Patient Instruction", "patient_is_deceased": "Patient is deceased", "patient_login": "Log in as Patient", "patient_login_description": "For patients to book and manage their appointments.", "patient_name": "Patient Name", "patient_name_uhid": "Patient Name/UHID", "patient_no": "OP/IP No", "patient_not_found": "Patient Not Found", "patient_notes_thread__Doctors": "Doctor's Discussions", "patient_notes_thread__Nurses": "Nurse's Discussions", "patient_phone_number": "Patient Phone Number", "patient_preparation": "Patient Preparation", "patient_profile": "Patient Profile", "patient_profile_created_by": "Patient profile created by", "patient_records_found": "Patient Records Found", "patient_records_found_description": "It appears that there are patient records that contain the same             phone number as the one you just entered. ", "patient_registration": "Patient Registration", "patient_registration__address": "Address", "patient_registration__age": "Age", "patient_registration__comorbidities": "Comorbidities", "patient_registration__comorbidities__details": "Details", "patient_registration__comorbidities__disease": "Disease", "patient_registration__contact": "Emergency Contact", "patient_registration__gender": "Gender", "patient_registration__name": "Name", "patient_registration_error": "Could not register patient", "patient_registration_success": "Patient Registered Successfully", "patient_state": "Patient State", "patient_status": "Patient Status", "patient_tags": "Patient Tags", "patient_transfer_birth_match_note": "Note: Year of birth must match the patient to process the transfer request.", "patient_update_error": "Could not update patient", "patient_update_success": "Patient Updated Successfully", "patients": "Patients", "patients_per_slot": "Patients per Slot", "pause_request": "Pause Request", "payment": "Payment", "payment_amount": "Payment Amount", "payment_cancelled": "Payment Cancelled", "payment_date": "Payment Date", "payment_details": "Payment Details", "payment_due": "Payment Due", "payment_due_message": "A payment of {{amount}} is due by {{date}}", "payment_history": "Payment History", "payment_id": "Payment ID", "payment_may_not_exist": "The payment you are looking for may not exist.", "payment_method": "Payment Method", "payment_not_found": "Payment Not Found", "payment_receipt": "Payment Receipt", "payment_reconciliation_management": "Payment Reconciliation Management", "payment_reconciliations": "Payment Reconciliations", "payment_recorded": "Payment Recorded", "payment_recorded_successfully": "Payment recorded successfully", "payment_status": "Payment status", "payment_status_updated": "Payment status updated", "payment_terms": "Payment Terms", "payment_terms_placeholder": "Enter Payment Terms", "payment_timeline": "Payment Timeline", "payment_type": "Payment Type", "payments": "Payments", "payments_received": "Payments Received", "pdf": "PDF", "pending": "Pending", "pending_deliveries": "Pending Deliveries", "pending_from_patient": "Pending from Patient", "pending_pos": "Pending POs", "percentage_of_base": "Percentage of Base", "percentage_of_subtotal": "Percentage of Subtotal", "percentage_of_total": "Percentage of Total", "performed_by": "Performed By", "performed_on": "Performed On", "period": "Period", "permanant_address_is_required": "Permanant address is required", "permanent_address": "Permanent Address", "permission": "Permission", "permission_denied": "You do not have permission to perform this action", "permission_denied_encounter": "You do not have permission to view this encounter", "permissions": "Permissions", "permitted": "Permitted", "permitted_data_type": "Permitted Data Type", "permitted_unit": "Permitted Unit", "personal_details": "Personal Details", "personal_information": "Personal Information", "personal_information_note": "View or update user's personal information", "personal_information_note_self": "View or update your personal information", "personal_information_note_view": "View user's personal information", "pharmacy": "Pharmacy", "pharmacy_medications": "Pharmacy Medications", "phone": "Phone", "phone_no": "Phone no.", "phone_number": "Phone Number", "phone_number_at_current_facility": "Phone Number of Contact person at current Facility", "phone_number_min_error": "Phone number must be at least 10 characters long", "phone_number_not_found": "Phone number not found", "phone_number_validation_error": "Entered phone number is not valid", "phone_number_verified": "Phone Number Verified", "pick_a_date": "Pick a date", "pincode": "Pincode", "pincode_autofill": "State and District auto-filled from Pincode", "pincode_district_auto_fill_error": "Failed to auto-fill district information", "pincode_must_be_6_digits": "Pincode must be 6 digits", "pincode_state_auto_fill_error": "Failed to auto-fill state and district information", "pk_form_basic_information_description": "Enter the basic details of the product", "pk_form_definitional_description": "Specify definitional details about the product - dosage, route, etc.", "pk_form_intended_routes_description": "Add intended routes of administration", "pk_form_storage_guidelines_description": "Add storage guidelines for this product", "plan": "Plan", "planned": "Planned", "planned_location": "Planned Location", "planned_reserved_cannot_be_in_past": "Planned/Reserved cannot be in the past", "play": "Play", "play_audio": "Play Audio", "please enter a valid url": "Please enter a valid URL", "please_assign_bed_to_patient": "Please assign a bed to this patient", "please_check_your_messages": "Please check your messages", "please_confirm_password": "Please confirm your new password.", "please_enter_a_name": "Please enter a name!", "please_enter_a_name_for_the_drawing": "Please enter a name for the drawing.", "please_enter_a_reason_for_the_shift": "Please enter a reason for the shift.", "please_enter_a_valid_reason": "Please enter a valid reason!", "please_enter_confirm_password": "Please confirm your new password", "please_enter_correct_birth_year": "Please enter the correct birth year to verify the patient details.", "please_enter_current_password": "Please enter your current password.", "please_enter_email_address": "Please enter email address", "please_enter_new_password": "Please enter your new password.", "please_enter_organization_name": "Please enter an organization name", "please_enter_username": "Please enter the username", "please_enter_valid_days_supply_for_medications": "Days supply should be greater than 0 for medications: {{medications}}", "please_fill_all_required_fields": "Please fill all the required fields", "please_fill_all_results": "Please fill all results", "please_fix_errors": "Please fix the errors in the highlighted fields and try submitting again.", "please_select_a_consent": "Please select a consent", "please_select_a_facility": "Please select a facility", "please_select_at_least_one_item": "Please select at least one item", "please_select_blood_group": "Please select the blood group", "please_select_breathlessness_level": "Please select Breathlessness Level", "please_select_district": "Please select the district", "please_select_facility_type": "Please select Facility Type", "please_select_gender": "Please select the Gender", "please_select_inventory_for_medications": "Please select inventory for medications: {{medications}}", "please_select_inventory_item": "Please select inventory item", "please_select_localbody": "Please select the local body", "please_select_medications": "Please select medications to dispense", "please_select_patient_category": "Please select Patient Category", "please_select_preferred_vehicle_type": "Please select Preferred Vehicle Type", "please_select_quantity_for_medications": "Quantity cannot be zero for medications: {{medications}}", "please_select_state": "Please select the state", "please_select_status": "Please select Status", "please_select_system_and_code": "Please select system and code", "please_select_user_type": "Please select the User Type", "please_upload_a_csv_file": "Please Upload A CSV file", "please_upload_a_file": "Please upload a file", "please_upload_an_image_file": "Please upload an image file!", "po_number": "PO Number", "policy": "Policy", "policy__insurer": "Insurer", "policy__insurer__example": "GICOFINDIA", "policy__insurer_id": "Insurer ID", "policy__insurer_id__example": "GICOFINDIA", "policy__insurer_name": "Insurer Name", "policy__insurer_name__example": "GIC OF INDIA", "policy__policy_id": "Policy ID / Policy Name", "policy__policy_id__example": "POL001", "policy__subscriber_id": "Member ID", "policy__subscriber_id__example": "SUB001", "policy_id_required": "Policy Id or Policy Name is required", "position": "Position", "post_partum": "Post-partum", "post_your_comment": "Post Your Comment", "powered_by": "Powered By", "practitioner_information": "Practitioner Information", "practitioner_one": "Practitioner", "practitioner_other": "Practitioners", "preference": "Preference", "preferred": "Preferred", "preferred_facility_type": "Preferred Facility Type", "preferred_vehicle": "Preferred Vehicle", "prefix": "Prefix", "preliminary": "Preliminary", "preparation": "Preparation", "prepared_date": "Prepared date", "prepared_on": "Prepared on", "preparing": "Preparing...", "prescribed": "Prescribed", "prescribed_by": "Prescribed by", "prescription": "Prescription", "prescription_details": "Prescription Details", "prescription_discontinued": "Prescription discontinued", "prescription_logs": "Prescription Logs", "prescription_medication": "Prescription Medication", "prescription_medications": "Prescription Medications", "prescription_queue": "Prescription Queue", "prescriptions": "Prescriptions", "prescriptions__dosage_frequency": "Dosage & Frequency", "prescriptions__medicine": "Medicine", "prescriptions__route": "Route", "prescriptions__start_date": "Prescribed On", "present_health": "Present Health", "preset_deleted": "Preset deleted", "preset_name_placeholder": "Specify an identifiable name for the new preset", "preset_updated": "Preset updated", "prev_sessions": "Prev Sessions", "preview": "Preview", "preview_bed_names": "Preview Bed Names", "preview_cropped_image_hint": "Preview of cropped image. Click upload to save.", "preview_form": "Preview form", "preview_of_proportions": "Preview of proportions", "preview_proportions_error": "The sum of the ratios does not equal the total ratio. Please adjust either the total ratio or the ratios of the elements to enable preview.", "preview_proportions_zero_error": "One or more of the elements have a size ratio of 0. Please adjust the ratios of the elements to enable preview.", "preview_value_set": "Preview ValueSet", "previous": "Previous", "price": "Price", "price_calculation_preview": "Price Calculation Preview", "price_component_calculation_rules": "Price Component Calculation Rules", "price_components": "Price Components", "price_per_unit": "Price/Unit", "price_summary": "Price Summary", "pricing_components": "Pricing Components", "pricing_details": "Pricing Details", "primary": "Primary", "primary_member_updated": "Primary member updated", "primary_ph_no": "Primary Ph No.", "primary_phone_no": "Primary ph. no.", "primary_treating_consultant": "Primary Treating Consultant", "primary_treating_doctor": "Primary Treating Doctor", "principal": "Principal", "principal_diagnosis": "Principal diagnosis", "print": "Print", "print_all_qr_codes": "Print All QR Codes", "print_all_responses": "Print all {{title}} responses", "print_invoice": "Print Invoice", "print_prescriptions": "Print Prescriptions", "print_qr_codes": "Print QR Codes", "print_receipt": "Print Receipt", "print_referral_letter": "Print Referral Letter", "print_selected": "Print Selected", "print_this_response": "Print this response", "priority": "Priority", "prn_prescription": "PRN Prescription", "prn_prescriptions": "PRN Prescriptions", "prn_reason": "PRN Reason", "procedure": "Procedure", "procedure_suggestions": "Procedure Suggestions", "procedures_select_placeholder": "Select procedures to add details", "proceed": "Proceed", "process": "Process", "process_specimen": "Process Specimen", "process_specimen__dialog_action_title": "{{action}} Processing Step", "process_specimen__dialog_text_area_description": "Add specific details about how this processing step was performed", "process_specimen__dialog_text_area_placeholder": "Describe the processing step in detail...", "process_specimen__step_heading": "Choose Processing Steps Performed on the Specimen", "process_specimen__textarea_placeholder": "Enter the specimen processing step details here", "process_specimen__valusetselect_placeholder": "Select processing step...", "processed": "Processed", "processing": "Processing", "processing_method": "Processing Method", "processing_with_dots": "Processing...", "product": "Product", "product_charge_item_definition_selection_description": "Select a charge item definition to associate with this product for billing purposes. This will be used when generating charges for this product.", "product_created_successfully": "Product created successfully", "product_definition": "Product Definition", "product_details": "Product Details", "product_is_already_in_the_list": "Product is already added in the list", "product_knowledge": "Product Knowledge", "product_knowledge_created_successfully": "Product knowledge created successfully", "product_knowledge_description": "Base information about this product", "product_knowledge_names_description": "Alternative names for the product", "product_knowledge_not_found": "Product Knowledge Not Found", "product_knowledge_selection_description": "Select the product knowledge this product is an instance of", "product_knowledge_updated_successfully": "Product knowledge updated successfully", "product_name": "Product Name", "product_not_found": "The requested product could not be found", "product_status": "Product Status", "product_supplier": "Product Supplier", "product_type": "Product Type", "product_updated_successfully": "Product updated successfully", "products": "Products", "professional_info": "Professional Information", "professional_info_note": "View or update user's professional information", "professional_info_note_self": "View or update your professional information", "professional_info_note_view": "View user's professional information", "profile": "Profile", "profile_picture_deleted": "Profile picture deleted", "properties": "Properties", "property": "Property", "proposal": "Proposal", "proposed": "Proposed", "provisional": "Provisional", "purchase_order": "Purchase Order", "purchase_order_details": "Purchase Order Details", "purchase_order_id": "Purchase Order ID", "purchase_order_not_found": "Purchase order not found", "purchase_order_updated": "Purchase order updated", "purchase_orders": "Purchase Orders", "purchase_orders_by_supplier": "Purchase Orders by Supplier", "purchase_orders_created": "Purchase order created", "purpose": "Purpose", "qr_code": "QR Code", "qr_code_scanned_successfully": "QR Code Scanned Successfully", "qr_codes": "QR Codes", "qr_success": "QR code generated successfully", "qty": "Qty", "qty_requested": "Qty Requested", "qualification": "Qualification", "qualification_required": "Qualification is required", "quantity": "Quantity", "quantity_approved": "Quantity Approved", "quantity_cannot_be_zero": "Quantity cannot be zero", "quantity_for_medication_selected_exceeds_available_stock": "For {{medication}} (Lot #{{lot}}), requested quantity ({{requested}}) exceeds available stock ({{available}}).", "quantity_question_description": "Select the valueset of options for this quantity question", "quantity_received": "Quantity Received", "quantity_requested": "Quantity Requested", "quantity_required": "Quantity Required", "question": "Question", "question_actions": "Question Actions", "question_description_placeholder": "Additional context or instructions for this question", "question_settings": "Question Settings", "question_settings_description": "Configure the basic behavior: mark as required, allow multiple entries, or set as read only.", "question_structured_type_placeholder": "Select structured type", "question_text": "Question Text", "question_type_boolean_description": "A simple yes/no question that users can answer with a single click.", "question_type_choice_description": "Presents a list of predefined options for users to choose from.", "question_type_date_description": "Lets users select a specific date from a calendar.", "question_type_date_time_description": "Lets users select both a date and time.", "question_type_decimal_description": "Collects numbers with decimal points (like 3.14 or 2.5).", "question_type_display_description": "Shows text or instructions to users without collecting any answers.", "question_type_group_description": "A container for organizing related questions together. Must contain at least one question.", "question_type_integer_description": "Collects whole numbers (like 1, 2, 3).", "question_type_placeholder": "Select question type", "question_type_quantity_description": "Collects a number with its unit of measurement (like 5 kg or 2 hours).", "question_type_string_description": "A short text field for brief answers like names or single-line responses.", "question_type_structured_description": "A specialized question type for collecting specific types of medical data.", "question_type_text_description": "A larger text area for longer responses like descriptions or detailed explanations.", "question_type_time_description": "Lets users select a specific time of day.", "question_type_url_description": "Collects website addresses or links.", "questionnaire": "Questionnaire", "questionnaire_allergy_intolerance_no_encounter": "Allergy Intolerances cannot be recorded without an active encounter", "questionnaire_appointment_no_encounter": "Appointment cannot be recorded without an active encounter", "questionnaire_charge_item_no_encounter": "Charge Items cannot be recorded without an active encounter", "questionnaire_created_successfully": "Questionnaire created successfully!", "questionnaire_diagnosis_no_encounter": "Diagnosis cannot be recorded without an active encounter", "questionnaire_error_loading": "Failed to load questionnaire. Please try again later.", "questionnaire_files_no_encounter": "Create an encounter first to upload files", "questionnaire_imported_successfully": "Questionnaire Imported Successfully", "questionnaire_json_url": "Questionnaire JSON URL", "questionnaire_json_url_placeholder": "https://example.com/questionnaire.json", "questionnaire_medication_request_no_encounter": "Medication requests cannot be recorded without an active encounter", "questionnaire_medication_statement_no_encounter": "Medication statements cannot be recorded without an active encounter", "questionnaire_no_encounter": "Create an encounter first in order to update it", "questionnaire_not_exist": "The questionnaire you tried to access does not exist.", "questionnaire_one": "Questionnaire", "questionnaire_other": "Questionnaires", "questionnaire_question_dependent": "This question is used in following question(s) as an enable when condition: ", "questionnaire_response_logs": "Questionnaire Response Logs", "questionnaire_service_request_no_encounter": "Service Requests cannot be recorded without an active encounter", "questionnaire_submission_failed": "Failed to submit questionnaire", "questionnaire_submitted_successfully": "Questionnaire submitted successfully", "questionnaire_symptom_no_encounter": "Symptoms cannot be recorded without an active encounter", "questionnaire_updated_successfully": "Questionnaire updated successfully", "questionniare_load_error": "Failed to load questionnaire. Please try again later.", "questions": "Questions", "questions_copied": "Questions successfully copied", "questions_count": "Question count", "questions_moved": "Questions successfully moved", "questions_removed": "Questions successfully removed", "quick_access": "Quick Access", "quick_actions": "Quick Actions", "quick_actions_description": "Schedule an appointment or create a new encounter", "quick_actions_description_create_appointment": "Schedule an appointment", "quick_actions_description_create_encounter": "Create a new encounter", "quick_finder": "Quick Finder", "raise_dispatch_request": "All requests below are to be dispatched from <strong>{{location}}</strong>  to other units.", "raise_stock_request": "Raise Stock Request", "raised": "Raised", "range": "Range", "ration_card__APL": "APL", "ration_card__BPL": "BPL", "ration_card__NO_CARD": "Non-card holder", "ration_card_category": "Ration Card Category", "re_admission": "Re-Admission", "read_more": "Read more", "read_only": "Read only", "readmission": "Re-admission", "ready_for_admission": "Ready For Admission", "reason": "Reason", "reason_for_discontinuation": "Reason for discontinuation", "reason_for_edit": "Reason for edit", "reason_for_medication": "Enter reason for Medication", "reason_for_referral": "Reason for referral", "reason_for_request": "Reason for Request", "reason_for_reschedule_placeholder": "Enter reason for rescheduling the appointment", "reason_for_shift": "Reason for shift", "rebalance": "Rebalance", "rebalancing": "Rebalancing", "receive_item": "Receive Item", "receive_items": "Receive Items", "receive_stock": "Receive Stock", "receive_stock_description": "Receive stock from external vendors", "receive_stock_empty_hint": "If a Purchase Order has been sent to a distributor, select the distributor from the dropdown above. If not, start adding items manually by clicking the Add Item button to match the received stock.", "received": "Received", "received_date": "Received Date", "received_item": "Received Item", "received_quantity": "Received Quantity:", "received_quantity_is_different_from_requested_quantity": "Received quantity is different from requested quantity", "receiving_status": "Receiving Status", "recent_data": "Recent Data", "recommend_discharge": "Recommend Discharge", "recommended_aspect_ratio_for": "Recommended aspect ratio for  the image is {{aspectRatio}}.", "reconciliation_type": "Reconciliation Type", "record": "Record Audio", "record_delete_confirm": "Are you sure you want to delete this record?", "record_has_been_deleted_successfully": "Record has been deleted successfully.", "record_payment": "Record Payment", "record_updates": "Record Updates", "recorded_by": "Recorded by", "recording": "Recording", "recording_payment": "Recording a payment", "recording_payment_for_invoice": "Recording payment for invoice #{{id}}", "recurrence": "Recurrence", "redirected_to_create_consultation": "Note: You will be redirected to create consultation form. Please complete the form to finish the transfer process", "reference": "Reference", "reference_details": "Reference Details", "reference_no": "Reference No", "reference_number": "Reference Number", "reference_number_description": "Transaction ID, check number, or other reference", "reference_number_description_cash": "Reference number for cash transactions", "reference_range": "Reference Range", "referral_letter": "Referral Letter", "referred_to": "Referred to", "reflex_order": "Reflex Order", "refresh": "Refresh", "refresh_list": "Refresh List", "refreshed": "Refreshed", "refuted": "Refuted", "regenerate": "Regenerate", "regenerate_backup_codes": "Regenerate Backup Codes", "regenerate_backup_codes_warning": "You are going to generate new backup codes. Your old backup codes will no longer work.", "regenerate_discharge_summary": "Regenerate Discharge <PERSON>mmary", "regenerating": "Regenerating", "regex": "Regex", "regex_help": "A regular expression pattern to validate the identifier format (e.g., ^[A-Z0-9]{8,12}$).", "regex_help_short": "Pattern for valid identifier values.", "register_hospital": "Register Hospital", "register_page_title": "Register As Hospital Administrator", "register_patient": "Register Patient", "registered": "Registered", "registered_name": "Registered Name", "reject": "Reject", "rejected": "Rejected", "relapse": "Relapse", "related_invoice": "Related Invoice", "related_person": "Related Person", "reload": "Reload", "remarks": "Remarks", "remarks_placeholder": "Enter remarks", "remission": "Remission", "remove": "Remove", "remove_allergy": "Remove Allergy", "remove_charge_item": "Remove Charge Item", "remove_charge_item_confirmation": "Are you sure you want to remove this charge item from the invoice?", "remove_coding": "Remove Coding", "remove_datetime": "Remove Date/Time", "remove_definition": "Remove Definition", "remove_diagnosis": "Remove Diagnosis", "remove_from_selection": "Remove from selection", "remove_image": "Remove Image", "remove_medication": "Remove Medication", "remove_medication_confirmation": "Are you sure you want to remove {{medication}}?", "remove_name": "Remove {{name}}", "remove_organization": "Remove organization", "remove_questions": "Remove Questions", "remove_questions_confirmation": "Are you sure do you want to remove the selected questions ?", "remove_questions_count": "Remove {{count}} questions", "remove_row": "Remove Row", "remove_row_confirmation": "Are you sure you want to remove this row?", "remove_rule": "Remove Separator", "remove_symptom": "Remove Symptom", "remove_text": "Remove Text", "remove_user": "Remove User", "remove_user_organization": "Remove User from Organization", "remove_user_warn": "Are you sure you want to remove {{firstName}} {{lastName}} from this organization? This action cannot be undone.", "removing_with_dots": "Removing...", "rename": "<PERSON><PERSON>", "rename_file": "Rename File", "repeatable": "Repeatable", "replace_home_facility": "Replace Home Facility", "replace_home_facility_confirm": "Are you sure you want to replace", "replace_home_facility_confirm_as": "as home facility for user", "reply": "Reply", "report": "Report", "report_builder": "Report Builder", "report_builder_title": "Report Template Builder", "report_builder_will_be_generated": "Report for template: {{reportSlug}} will be generated shortly.", "report_created_by": "Report created by", "report_details": "Report Details", "report_template_description": "Report templates help you create standardized formats for patient reports and clinical documentation.", "req_atleast_one_digit": "Require at least one digit", "req_atleast_one_lowercase": "Require at least one lower case letter", "req_atleast_one_symbol": "Require at least one symbol", "req_atleast_one_uppercase": "Require at least one upper case", "request": "Request", "request id": "Request id", "request-sample-test": "Service Request", "request_consent": "Request Consent", "request_details": "Request Details", "request_for": "Request for", "request_id": "Request ID", "request_letter": "Request Letter", "request_raised": "Request Raised", "request_raised_by": "Request raised by", "request_reason": "Reason of Request", "request_reason_description": "Provide a detailed explanation of why this resource is needed.", "request_reason_placeholder": "Type your description here", "request_sample_test": "Request Sample Test", "request_stock_from_another_store_or_pharmacy_within_the_facility": "Request stock from another store or pharmacy within the facility", "request_stock_from_vendor": "Request stock from external vendor", "request_the_following_resource": "This is to request the following resource", "request_title": "Request Title", "request_title_description": "A brief title that describes what resource is needed.", "request_title_placeholder": "Enter a clear, concise title for your request", "request_updated_successfully": "Request updated successfully", "requested": "Requested", "requested by": "Requested by", "requested_by": "Requested By", "requested_item": "Requested <PERSON>em", "requested_items": "Requested Items", "requested_qty": "Requested <PERSON><PERSON>.", "requested_quantity": "Requested Quantity", "requesting_camera_access": "Requesting Camera Access", "requests_raised": "Requests Raised", "requests_to_dispatch": "Requests to Dispatch", "required": "Required", "required_help": "If enabled, this identifier must be provided for every patient.", "required_quantity": "Required Quantity", "requirement": "Requirement", "requirements": "Requirements", "reschedule": "Reschedule", "reschedule_appointment": "Reschedule Appointment", "reschedule_appointment_with": "Reschedule Appointment with", "rescheduled": "Rescheduled", "rescheduling": "Rescheduling...", "resend_otp": "Resend OTP", "resend_otp_timer": "Resend O<PERSON> in {{time}} seconds", "reserved": "Reserved", "reset": "Reset", "reset_password": "Reset Password", "reset_password_note_self": "Enter your current password, then create and confirm your new password", "reset_to_default": "Reset to De<PERSON>ult", "resolved": "Resolved", "resource": "Resource", "resource_approving_facility": "Resource approving facility", "resource_created_successfully": "Request created successfully", "resource_details": "Resource details", "resource_origin_facility": "Origin Facility", "resource_request": "Request", "resource_request_basic_info_description": "Provide the basic details about the resource request including the facility and urgency.", "resource_request_details_description": "Provide detailed information about what resource is needed and why.", "resource_requests": "Requests", "resource_status": "Request Status", "resource_status__approved": "Approved", "resource_status__cancelled": "Cancelled", "resource_status__completed": "Completed", "resource_status__pending": "Pending", "resource_status__rejected": "Rejected", "resource_status__transfer_in_progress": "Transfer in progress", "resource_status__transportation_to_be_arranged": "Transportation to be arranged", "resource_title": "Resource Title", "resource_type": "Request Type", "resource_updated_successfully": "Resource updated successfully", "respiratory_status": "Respiratory Status", "result": "Result", "result_date": "Result Date", "result_details": "Result details", "result_on": "Result on", "result_review": "Result Review", "result_value": "Result value", "resume": "Resume", "retake": "Retake", "retake_recording": "Retake Recording", "retention": "Retention", "retention_time": "Retention time", "retired": "Retired", "retrieval_options": "Retrieval Options", "retrieval_options_help": "Configure how this identifier can be used to retrieve patient records.", "retrieve_config": "Retrieve config", "retrieve_with_dob": "Retrieve with DOB", "retrieve_with_dob_help": "Allow searching for a patient using this identifier and their date of birth.", "retrieve_with_otp": "Retrieve with OTP", "retrieve_with_otp_help": "Allow searching for a patient using this identifier and a one-time password (OTP).", "retrieve_with_year_of_birth": "Retrieve with year of birth", "retrieve_with_year_of_birth_help": "Allow searching for a patient using this identifier and their year of birth.", "return_to_care": "Return to Care", "return_to_list": "Return to List", "return_to_login": "Return to Login", "return_to_password_reset": "Return to Password Reset", "return_to_patient_dashboard": "Return to Patient Dashboard", "returned": "Returned", "review_before": "Review Before", "review_missed": "Review Missed", "revoked": "Revoked", "revoked_on": "Revoked On", "right": "Right", "role": "Role", "roles": "Roles", "room_apt": "Room/Apt & Time", "rotate_left": "Rotate Left", "rotate_right": "Rotate Right", "route": "Route", "routine": "Routine", "row": "Row", "rule": "Separator", "sample_base_amount": "Sample Base Amount", "sample_collection_date": "Sample Collection Date", "sample_format": "Sample Format", "sample_identification": "Sample Identification", "sample_test": "Sample Test", "sample_test_details": "Sample Test Details", "sample_test_history": "Sample Test History", "sample_type": "Sample Type", "sample_type_description": "Sample Type Description", "sari": "SARI - Severe Acute Respiratory illness", "save": "Save", "save_and_continue": "Save and Continue", "save_and_exit": "Save & Exit", "save_and_go_back": "Save & Go Back", "save_as_received": "Save as Received", "save_investigation": "Save Investigation", "save_results": "Save Results", "save_template": "Save", "save_valueset": "Save ValueSet", "saved_successfully": "Saved successfully", "saving": "Saving...", "saving_is_disabled_for_system_valuesets": "Saving is disabled for system value sets", "saving_with_dots": "Saving...", "scan_asset_qr": "<PERSON>an Asset QR!", "scan_existing": "<PERSON><PERSON> Existing", "scan_qr": "Scan the QR code", "scan_with_camera": "<PERSON>an with Camera", "schedule": "Schedule", "schedule_an_appointment_or_create_a_new_encounter": "Schedule an appointment or create a new encounter", "schedule_appointment": "Schedule Appointment", "schedule_availability_created_successfully": "Availability created successfully", "schedule_availability_deleted_successfully": "Schedule availability deleted successfully", "schedule_bed": "Schedule Bed", "schedule_calendar": "Schedule Calendar", "schedule_creation_for_past_validation_error": "Schedule cannot be created for the past", "schedule_end_time": "End Time", "schedule_exception_creation_for_past_validation_error": "Schedule exception cannot be created for the past", "schedule_for": "Scheduled for", "schedule_for_later": "Schedule for later", "schedule_information": "Schedule Information", "schedule_remarks": "Remarks", "schedule_remarks_placeholder": "Any additional notes about this session", "schedule_session_time": "Session Time", "schedule_session_type": "Session Type", "schedule_sessions": "Sessions", "schedule_sessions_min_error": "Add at least one session", "schedule_slot_size_label": "Slot duration (mins.)", "schedule_slots_allocation_callout": "Allocating <strong>{{slots}} slots</strong> in this session provides approximately <strong>{{token_duration}} mins.</strong> for each patient.", "schedule_start_time": "Start Time", "schedule_template": "Schedule Template", "schedule_template_name": "Template Name", "schedule_template_name_placeholder": "Regular OP Day", "schedule_valid_from_till_range": "Valid from <strong>{{from_date}}</strong> till <strong>{{to_date}}</strong>", "schedule_weekdays": "Weekdays", "schedule_weekdays_description": "Select the weekdays applicable for the template", "schedule_weekdays_min_error": "Select at least one weekday", "scheduled": "Scheduled", "scheduled_for": "Schedule for:", "scribe__reviewing_field": "Reviewing field {{currentField}} / {{totalFields}}", "scribe_error": "Could not autofill fields", "scroll_to_question": "Scroll to question", "scroll_to_top": "Scroll to top", "search": "Search", "search_accounts": "Search accounts", "search_activity_definitions": "Search activity definitions", "search_and_select_product": "Search and select product", "search_button": "Search Facilities", "search_by": "Search by", "search_by_department_team_name": "Search by department/team name", "search_by_emergency_contact_phone_number": "Search by Emergency Contact Phone Number", "search_by_emergency_phone_number": "Search by Emergency Phone Number", "search_by_encounter_id": "Search by encounter ID", "search_by_external_id": "Search by external ID", "search_by_facility_name": "Search by facility name", "search_by_identifier": "Search by {{name}}", "search_by_item": "Search by item", "search_by_name": "Search by name", "search_by_patient_name": "Search by patient name", "search_by_patient_name_id_or_prescription": "Search by patient name, ID, or prescription...", "search_by_patient_name_or_id_or_pn": "Search by patient name/℞ ID/phone number", "search_by_patient_no": "Search by patient number", "search_by_phone_number": "Search by phone number", "search_by_resource_title": "Search by resource title", "search_by_supplier": "Search by supplier", "search_by_username": "Search by username", "search_charge_item_definitions": "Search charge item definitions", "search_charge_items": "Search Charge Items", "search_concept": "Search for a concept in this valueset", "search_configs": "Search configs", "search_country": "Search country...", "search_definitions": "Search definitions", "search_devices": "Search devices...", "search_drawings": "Search drawings...", "search_encounters": "Search Encounters", "search_existing_consent": "Search existing consent", "search_facilities": "Search for available facilities to book an appointment <strong>(for new patients)</strong>", "search_facility_type": "Search facility type", "search_files": "Search files", "search_for_a_user": "Search for a user", "search_for_activity_codes": "Search for activity codes", "search_for_allergies_to_add": "Search for allergies to add", "search_for_diagnoses_to_add": "Search for diagnoses to add", "search_for_diagnostic_codes": "Search for Diagnostic codes", "search_for_facility": "Search for Facility", "search_for_location": "Search for a location", "search_for_medications_to_add": "Search for medications to add", "search_for_observation_codes": "Search for observation codes", "search_for_product_codes": "Search for product codes", "search_for_service_types": "Search for service types...", "search_for_units": "Search for units", "search_healthcare_services": "Search healthcare services...", "search_icd11_placeholder": "Search for ICD-11 Diagnoses", "search_icons": "Search icons...", "search_informational_codes": "Search informational codes", "search_investigation_placeholder": "Search Investigation & Groups", "search_invoices": "Search Invoices", "search_items": "Search Items", "search_location": "Search location", "search_locations": "Search locations...", "search_medication": "Search Medication", "search_medications": "Search Medications", "search_medicine": "Search Medicine", "search_no_results": "Unable to find anything based on your search.", "search_observation_definitions": "Search definitions...", "search_only_patient_page_text": "Search for existing patients using their phone number", "search_organization": "Search organization", "search_organizations": "Search Organizations", "search_page_size": "Search page sizes...", "search_patient_page_text": "Search for existing patients using their phone number or create a new patient record", "search_patients": "Search patients", "search_payments": "Search payments", "search_product": "Search Product", "search_product_knowledge": "Search Product Knowledge", "search_products": "Search Products", "search_products_to_add": "Search products to add", "search_purchase_orders": "Search purchase orders", "search_questionnaires": "Search Questionnaires", "search_resource": "Search Resource", "search_roles": "Search Roles", "search_service_requests": "Search requests...", "search_services": "Search services...", "search_substitute_medications": "Search Substitute Medications", "search_suppliers": "Search suppliers...", "search_supply_deliveries": "Search Supply Deliveries", "search_supply_requests": "Search Supply Requests", "search_symptom": "Search Symptom", "search_tag_configs": "Search tag configs", "search_tags": "Search tags...", "search_tax_codes": "Search tax codes...", "search_tax_components": "Search tax components...", "search_user": "Search User", "search_user_description": "Search for a user and assign a role to add them to the patient.", "search_users": "Search users...", "search_valuesets": "Search ValueSets", "search_vendor": "Search vendor", "searching": "Searching...", "secret_key_copied": "Secret key copied to clipboard", "section_allergy": "Allergies", "section_care_team": "Care Team", "section_custom": "Custom Section", "section_diagnosis": "Active Diagnoses", "section_discharge_advice": "Discharge Summary Advice", "section_encounter": "Encounter Details", "section_file_upload": "Files & Documents", "section_medication": "Medications", "section_observation": "Observations", "section_patient_info": "Patient Information", "section_symptom": "Symptoms Reported", "section_title": "Section Title", "sections": "Sections", "sections_description": "Configure, reorder and customize report sections", "sections_error": "There are errors in the Sections tab. Please fix the errors before saving.", "secure_connection": "Secure connection • Your privacy is protected", "see_attachments": "See Attachments", "see_clinical_history": "See Clinical History", "see_details": "See Details", "see_invoice": "See Invoice", "see_less": "See less...", "see_more": "See more...", "see_note": "See Note", "see_prescription": "See Prescription", "select": "Select", "select_a_status": "Select a status", "select_a_value_set": "Select a Value Set", "select_activity_definition": "Select Activity Definition", "select_additional_instructions": "Select additional instructions", "select_admit_source": "Select Admit Source", "select_alignment": "Select alignment", "select_all": "Select All", "select_allergy": "Select Allergy", "select_alternative_medication_and_provide_details": "Select an alternative medication and provide substitution details", "select_an_option": "Select an option", "select_applicable_discounts": "Select applicable discounts", "select_applicable_taxes": "Select applicable taxes", "select_appointment_slot": "Select appointment slot", "select_availability_status": "Select availability status", "select_body_site": "Select Body Site", "select_camera": "Select Camera", "select_cap": "Select Cap", "select_category": "Select a category", "select_charge_item_definition": "Select charge item definition", "select_charge_item_definitions": "Select Charge Item Definitions", "select_class": "Select Class", "select_collection": "Select Collection", "select_column": "Select Column", "select_component_type": "Select Component Type", "select_condition": "Select Condition", "select_config": "Select config", "select_consent": "Select Consent", "select_consent_decision": "Select consent decision", "select_contact_system": "Select contact type", "select_contact_use": "Select contact use", "select_data_source": "Select data source", "select_data_type": "Select Data Type", "select_date": "Select date", "select_department": "Select Department", "select_department_description": "Select Department Description", "select_destination": "Select Destination", "select_destination_question": "Select Destination Question", "select_device": "Select device...", "select_diagnosis": "Select Diagnosis", "select_diagnostic_report_type": "Select Diagnostic Report Type", "select_diet_preference": "Select diet preference", "select_diff_role": "Please select a different role", "select_discharge_disposition": "Select discharge disposition", "select_display_style": "Select Display Style", "select_doctor": "Select Doctor", "select_eligible_policy": "Select an Eligible Insurance Policy", "select_existing_consent": "Select an existing consent", "select_facility": "Select Facility", "select_facility_description": "Select the healthcare facility that will provide the requested resource.", "select_facility_feature": "Select Facility Features", "select_facility_for_discharged_patients_warning": "Facility needs to be selected to view discharged patients.", "select_facility_type": "Select Facility Type", "select_field": "Select Field", "select_files": "Select files", "select_font": "Select font", "select_for_administration": "Select for Administration", "select_format": "Select format", "select_frequency": "Select frequency", "select_from_list": "Select From List", "select_gender": "Select gender", "select_groups": "Select Groups", "select_icon": "Select an icon", "select_intent": "Select intent", "select_internal_type": "Select Internal Type", "select_investigation": "Select Investigations (all investigations will be selected by default)", "select_investigation_groups": "Select Investigation Groups", "select_investigations": "Select Investigations", "select_item": "Select Item", "select_item_from_lot": "Select Item from Lot", "select_kind": "Select kind", "select_language": "Select Language", "select_local_body": "Select Local Body", "select_location": "Select Location", "select_location_first": "Select Location First", "select_location_requirements": "Select Location Requirements", "select_location_to_request": "Select a location to request this service", "select_locations": "Select locations", "select_lot": "Select Lot", "select_lots": "Select Lots", "select_lots_first": "Select lots first", "select_medication": "Select Medication", "select_medications_to_dispense": "Select medications to dispense", "select_member": "Select Member", "select_method": "Select method", "select_name_type": "Select the name type", "select_new_role": "Select New Role", "select_number_of_beds": "Select Number of Beds", "select_observation_requirements": "Select Observation Requirements", "select_or_create_charge_item_definitions": "Select or Create Charge Item Definitions", "select_or_create_observation_requirements": "Select or create observation", "select_or_create_specimen_requirements": "Select or create specimen", "select_or_type": "Select or type", "select_organization": "Select Organization", "select_organization_placeholder": "Select Organization", "select_organization_type": "Select organization type", "select_organizations": "Select Organizations", "select_origin": "Select Origin", "select_page_size": "Select Page Size", "select_patient": "Select Patient", "select_patient_preparation": "Select Patient Preparation", "select_payment_method": "Select a payment method", "select_policy": "Select an Insurance Policy", "select_policy_to_add_items": "Select a Policy to Add Items", "select_practitioner": "Select Practitioner", "select_practitioner_first": "Select practicioner first", "select_preference": "Select Preference", "select_previous": "Select Previous Fields", "select_priority": "Select Priority", "select_prn_reason": "Select reason for PRN", "select_product": "Select a product", "select_product_knowledge": "Select product knowledge", "select_product_type": "Select the product type", "select_purchase_order": "Select Purchase Order", "select_reconciliation_type": "Select reconciliation type", "select_register_patient": "Select/Register Patient", "select_resource": "Select the resource", "select_role": "Select Role", "select_route": "Select Route", "select_seven_day_period": "Select a seven day period", "select_site": "Select site", "select_size": "Select Size", "select_skills": "Select and add some skills", "select_specimen_requirements": "Select Specimen Requirements", "select_specimen_type": "Select Specimen Type", "select_status": "Select Status", "select_stock": "Select stock", "select_stroke": "Select Stroke", "select_sub_department": "Select sub-department", "select_subject_type": "Select Subject Type", "select_substitute_product": "Select Substitute Product", "select_supplier": "Select a supplier", "select_supply_request": "Select Supply Request", "select_symptom": "Select Symptom", "select_system": "Select System", "select_system_first": "Please select a system and enter a code first", "select_tags": "Select Tags", "select_tags_browse_group": "Select tags or browse groups...", "select_time": "Select time", "select_time_slot": "Select time slot", "select_type": "Select Type", "select_type_collected": "Select Type Collected", "select_use": "Select Use", "select_user": "Select user", "select_user_type": "Select user type", "select_valid_date": "Please select a valid date", "select_vendor": "Select Vendor/Distributor", "select_verification_type": "Select verification type", "select_wards": "Select wards", "select_weight": "Select Weight", "selected": "Selected", "selected_bed": "Selected bed", "selected_encounter": "Selected Encounter", "selected_for_approval": "Selected for Approval", "selected_items": "Selected items", "selected_items_count_one": "Selected {{count}} item.", "selected_items_count_other": "Selected {{count}} items.", "selected_organizations": "Selected Organizations", "selected_slot_not_found": "Selected Slot Not Found", "selected_substitute": "Selected Substitute", "selected_tags": "Selected Tags", "selected_token_slot_display": "on <strong>{{date}}</strong> between <strong>{{startTime}}</strong> & <strong>{{endTime}}</strong>", "self_booked": "Self-booked", "send": "Send", "send_email": "Send Email", "send_email_invitation": "Send email invitation", "send_email_invitation_description": "The user will receive an email with a link to set their password", "send_message": "Send Message", "send_otp": "Send OTP", "send_otp_error": "Failed to send <PERSON><PERSON>. Please try again later.", "send_otp_success": "OTP has been sent to the respective mobile number", "send_reset_link": "Send Reset Link", "send_sample_to_collection_centre_description": "Are you sure you want to send the sample to Collection Centre?", "send_sample_to_collection_centre_title": "Send sample to collection centre", "sending": "Sending", "seq": "Seq", "serial_number": "Serial Number", "serial_number_mode": "Serial Number Mode", "serial_number_mode_auto": "Auto-generated", "serial_number_mode_help": "Choose how serial numbers are assigned for this identifier type.", "serial_number_mode_user": "User entered", "service_available_locations": "Locations where this service is available", "service_date": "Service Date", "service_date_min_date": "Service date must be set to today or a past date", "service_details": "Service Details", "service_history": "Service History", "service_locations": "Service Locations", "service_not_found": "Service Not Found", "service_notes": "Notes", "service_notes_placeholder": "Enter service notes", "service_record_add": "Add Service Record", "service_record_added_successfully": "Service record added successfully", "service_record_description": "Add or update device maintenance and service information.", "service_record_edit": "Edit Service Record", "service_record_updated_successfully": "Service record updated successfully", "service_records_none": "No service history available", "service_request": "Service Request", "service_request_completed": "Service request marked as completed", "service_request_completion_confirmation": "Are you sure you want to mark this service request as complete? This action cannot be undone.", "service_request_detail_page_description_prefix": "{{count}} deliveries have been received", "service_request_details": "Service Request Details", "service_request_invoice_alert": "Back to Service Request", "service_request_not_found": "Service Request Not Found", "service_requests": "Service Requests", "service_type": "Service Type", "serviced_on": "Serviced on", "services": "Services", "session_capacity": "Session Capacity", "session_expired": "Session Expired", "session_expired_message": "It looks like your session timed out for a moment. Take a quick breather, then log in again to continue.", "session_expired_msg": "It appears that your session has expired. This could be due to inactivity. Please login again to continue.", "session_slots_info": "{{slots}} slots of {{minutes}} mins.", "session_slots_info_striked": " {{intended_slots}} slotsăr  {{actual_slots}} slots of {{minutes}} mins.", "session_title": "Session Title", "session_title_placeholder": "IP Rounds", "session_type": "Session Type", "set_average_weekly_working_hours_for": "Set Average weekly working hours for", "set_home_facility": "Set as home facility", "set_password_now": "Set password now", "set_password_now_description": "Create the user with a password you define. The user can log in immediately.", "set_your_local_language": "Set your local language", "settings": "Settings", "settings_and_filters": "Settings and Filters", "settle_close": "Settle & Close", "severe": "Severe", "severity": "Severity", "severity_of_breathlessness": "Severity of Breathlessness", "sex": "Sex", "sgst": "SGST", "shared_by": "Shared By", "shift": "Shift Patient", "shifting": "Shifting", "shifting_approval_facility": "Shifting approval facility", "shifting_approving_facility": "Shifting approving facility", "shifting_approving_facility_can_not_be_empty": "Shifting approving facility can not be empty.", "shifting_deleted": "Shifting record has been deleted successfully.", "shifting_details": "Shifting details", "shifting_history": "Shifting History", "shifting_status": "Shifting status", "show": "Show", "show_abha_profile": "Show ABHA Profile", "show_all": "Show all", "show_all_notifications": "Show All", "show_all_slots": "Show all slots", "show_available_beds_only": "Show Available Beds Only", "show_default_presets": "Show Default Presets", "show_notes": "Show notes", "show_on_map": "Show on Map", "show_patient_details_and_specimen_id": "Show patient details and specimen ID?", "show_patient_presets": "Show Patient Presets", "show_unread_notifications": "Show Unread", "showing_all_appointments": "Showing all appointments", "showing_x_of_y": "Showing <strong>{{x}}</strong> of <strong>{{y}}</strong>", "sidebar": "sidebar", "sidebar_description": "sidebar provides navigation to different sections", "sign_in": "Sign in", "sign_out": "Sign out", "since": "Since", "single_use": "Single Use", "site": "Site", "size": "Size", "size_large": "Large", "size_medium": "Medium", "size_ratio": "<PERSON><PERSON>", "size_ratio_help": "Size ratio relative to other elements in this row", "size_small": "Small", "skill_add_error": "Error while adding skill", "skill_added_successfully": "<PERSON>ll added successfully", "skills": "Skills", "slot_configuration": "Slot Configuration", "slug": "Slug", "slug_format_message": "Slug must only contain letters, numbers, underscores, and hyphens", "slug_input_placeholder": "Enter a unique slug", "social_history": "Social History", "social_profile": "Social Profile", "social_profile_detail": "Include occupation, ration card category, socioeconomic status, and domestic healthcare support for a complete profile.", "socioeconomic_status": "Socioeconomic status", "software_update": "Software Update", "something_went_wrong": "Something went wrong..!", "something_wrong": "Something went wrong! Try again later!", "sort_alphabetically": "Sort Alphabetically", "sort_by": "Sort By", "sort_by_latest_created": "Newest Entry First", "sort_by_latest_payment": "Newest Payment First", "sort_by_locations": "Number of Locations", "sort_by_name": "Name", "sort_by_oldest_created": "Oldest Entry First", "sort_by_oldest_payment": "Oldest Payment First", "source": "Source", "source_file": "Source Files", "spdx_sbom_version": "SPDX SBOM Version", "specify_diagnostic_report_codes": "Specify the diagnostic report codes for this activity", "specimen": "Specimen", "specimen_collect_error": "Failed to collect specimen", "specimen_collect_success": "Specimen collected successfully", "specimen_collected": "Spec<PERSON>n Collected", "specimen_collection_info": "Collection Information", "specimen_collection_required": "Please collect specimen before creating a report", "specimen_collector_unavailable": "Collector information not available. Please try again.", "specimen_definition": "Specimen Definition", "specimen_definition_created": "Specimen definition created", "specimen_definition_not_found": "Specimen Definition not found", "specimen_definition_retired_successfully": "Specimen definition retired successfully", "specimen_definition_updated": "Specimen Definition updated", "specimen_definitions": "Specimen Definitions", "specimen_details": "Specimen Details", "specimen_discard_dialog_description": "Please select the reason for discarding this specimen:", "specimen_draft_create_error": "Failed to create draft specimen", "specimen_draft_create_success": "Draft specimen created successfully", "specimen_draft_missing": "Draft specimen not created. Please try again.", "specimen_generate_failed": "Failed to generate barcode", "specimen_generating": "Generating barcode...", "specimen_history": "Specimen History", "specimen_id": "Specimen ID", "specimen_identification": "Sample Identification", "specimen_not_found": "Specimen not found", "specimen_qrcode_scan_info": "QR code scanning to be implemented", "specimen_requirements": "Specimen Requirements", "specimen_requirements_description": "Specimen that are the requird to be collected.", "specimen_scan_placeholder": "Scan or enter specimen identifier", "specimen_type": "Specimen Type", "specimen_update_error": "Failed to update specimen", "specimen_update_success": "Specimen updated successfully", "specimens": "Specimens", "spokes": "Spoke Facilities", "srf_id": "SRF ID", "stability_duration": "Stability Duration", "staff": "Staff", "staff_list": "Staff List", "staff_login": "Log in as Staff", "staff_login_description": "For hospital staff: doctors, nurses, and administrators.", "staff_not_found": "Staff Not Found", "staff_username_not_found": "Staff username not found", "starred": "Starred", "start_a_new_clinical_encounter": "Start a new clinical encounter", "start_billing": "Start Billing", "start_consultation": "Start Consultation", "start_date": "Start date", "start_date_required": "Start date is required", "start_datetime": "Start Date/Time", "start_dosage": "Start Dosage", "start_dose": "Start Dose", "start_new_clinical_encounter": "Start a new clinical encounter", "start_review": "Start Review", "start_time": "Start Time", "start_time_before_authored_error": "Start time cannot be before the medication was prescribed", "start_time_future_error": "Start time cannot be in the future", "start_time_must_be_before_end_time": "Start time must be before end time", "start_time_must_be_in_the_future": "Start time must be a future time", "start_time_required": "Start time is required", "start_typing_to_search": "Start typing to search...", "stat": "STAT", "state": "State", "state_reason_for_archiving": "State reason for archiving <strong>{{name}}</strong> file?", "status": "Status", "status_help": "Set the status of this identifier configuration (e.g., draft, active, retired).", "status_history": "Status History", "status_reason": "Status Reason", "status_updated_successfully": "Status updated successfully", "stock_received": "Stock received", "stock_request": "Stock Request", "stop": "Stop", "stop_recording": "Stop Recording", "stopped": "Stopped", "storage": "Storage", "storage_guidelines": "Storage Guidelines", "stream_stop_due_to_inativity": "The live feed will stop streaming due to inactivity", "stream_stopped_due_to_inativity": "The live feed has stopped streaming due to inactivity", "stream_uuid": "Stream UUID", "string": "String", "stroke": "Stroke", "structured_type": "Structured Type", "style_list": "List", "style_text": "Text", "styling": "Styl<PERSON>", "sub_category": "Sub Category", "sub_questions": "sub-questions", "sub_questions_count": "{{ count }} Sub-questions", "sub_questions_for_group": "Sub-questions (for the \"{{ group }}\" group)", "subject": "Subject", "subject_id": "Subject ID", "subject_type": "Subject Type", "submit": "Submit", "submit_request": "Submit Request", "submitting": "Submitting", "subscribe": "Subscribe", "subscribe_on_this_device": "Subscribe on this device", "subscribed_successfully": "Subscribed Successfully", "subscription_error": "Subscription Error", "subscription_failed": "Subscription Failed", "substance": "Substance", "substitute": "Substitute", "substitute_medication": "Substitute Medication", "substituted": "Substituted", "substituted_with": "Substituted With", "substituting_for": "Substituting for", "substitution_details": "Substitution Details", "substitution_reason": "Substitution Reason", "substitution_reason_ct": "Continuing Therapy", "substitution_reason_ct_description": "Decision driven by desire to maintain consistency with pre-existing therapy. Same item/service provided as previously rather than exact order or lower-cost equivalent.", "substitution_reason_fp": "Formulary Policy", "substitution_reason_fp_description": "Decision driven by policy expressed within the formulary.", "substitution_reason_os": "Out of Stock", "substitution_reason_os_description": "For substitution: ordered item was not in stock. For no substitution: cheaper equivalent was not in stock.", "substitution_reason_rr": "Regulatory Requirement", "substitution_reason_rr_description": "Decision driven by jurisdictional regulatory requirement mandating or prohibiting substitution.", "substitution_type": "Substitution Type", "substitution_type_bc_brand_change": "Brand Composition", "substitution_type_bc_description": "Substitution occurred or is permitted between equivalent Brands but not Generics.", "substitution_type_e_description": "Substitution occurred or is permitted with another bioequivalent and therapeutically equivalent product.", "substitution_type_e_equivalence": "Equivalent", "substitution_type_ec_description": "Substitution occurred or is permitted with another product that is a: pharmaceutical alternative containing the same active ingredient but is formulated with different salt, ester OR pharmaceutical equivalent that has the same active ingredient, strength, dosage form and route of administration.", "substitution_type_ec_equivalence_clinical": "Equivalent Composition", "substitution_type_f_description": "This substitution was performed or is permitted based on formulary guidelines.", "substitution_type_f_formulary": "Formulary", "substitution_type_g_description": "Substitution occurred or is permitted between equivalent Generics but not Brands.", "substitution_type_g_generic": "Generic Composition", "substitution_type_n_description": "No substitution occurred or is permitted.", "substitution_type_n_none": "None", "substitution_type_tb_description": "Substitution occurred or is permitted between therapeutically equivalent Brands but not Generics.", "substitution_type_tb_therapeutic_brand": "Therapeutic Brand", "substitution_type_te_description": "Substitution occurred or is permitted with another product having the same therapeutic objective and safety profile.", "substitution_type_te_therapeutic_equivalence": "Therapeutic Alternative", "substitution_type_tg_description": "Substitution occurred or is permitted between therapeutically equivalent Generics but not Brands.", "substitution_type_tg_therapeutic_generic": "Therapeutic Generic", "subtotal": "Subtotal", "subtotal_after_discounts": "Subtotal After Discounts", "subtotal_before_discounts": "Subtotal Before Discounts", "success": "Success", "suffix": "Suffix", "suggested_investigations": "Suggested Investigations", "suggestion": "Suggestion", "summary": "Summary", "supplied_item": "<PERSON><PERSON>", "supplied_item_condition": "Condition", "supplied_item_quantity": "Quantity", "supplied_item_type": "Type", "supplier": "Supplier", "supply_deliveries": "Supply Deliveries", "supply_deliveries_created": "Supply deliveries created", "supply_delivery": "Supply Delivery", "supply_delivery_created": "Supply Delivery Created", "supply_delivery_deleted": "Supply Delivery Deleted", "supply_delivery_details": "Supply Delivery Details", "supply_delivery_id": "Supply Delivery ID", "supply_delivery_updated": "Supply Delivery Updated", "supply_request": "Supply Request", "supply_request_details": "Supply Request Details", "supply_request_not_loaded": "Supply request not loaded", "supply_request_updated": "Supply request updated", "supply_requests": "Supply Requests", "supply_requests_created": "Supply request created", "support": "Support", "supported_variables": "Supported variables:", "supporting_documents": "Supporting Documents", "surcharge": "Surcharge", "surcharges": "Surcharges", "surgical_procedure": "Surgical Procedure", "survey": "Survey", "suspend": "Suspend", "suspend_this_request": "suspend this request", "suspended": "Suspended", "switch": "Switch", "switch_bed": "Switch Bed", "switch_camera": "Switch Camera", "switch_camera_is_not_available": "Switch camera is not available.", "symptom": "Symptom", "symptom_already_exist_warning": "Symptom already exists!", "symptom_history": "Symptom History", "symptoms": "Symptoms", "symptoms_empty_message": "No symptoms recorded", "system": "System", "system_generated": "System generated", "system_help": "The URI that defines the namespace or system for this identifier (e.g., https://example.org/identifier-system).", "system_help_short": "A globally unique URI for the identifier system.", "system_information": "System Information", "systolic": "Systolic", "tachycardia": "Ta<PERSON>cardia", "tag_config": "Tag config", "tag_config_created_successfully": "Tag Config created successfully", "tag_config_details": "Tag Config Details", "tag_config_not_found": "Tag Config Not Found", "tag_config_not_found_description": "The tag config you are looking for does not exist. Please check the tag config id.", "tag_config_updated_successfully": "Tag Config updated successfully", "tag_created_successfully": "Tag created successfully", "tag_details": "Tag details", "tag_name": "Tag Name", "tag_slug": "Tag Slug", "tag_updated_successfully": "Tag updated successfully", "tags": "Tags", "tags_removed_successfully": "Tags removed successfully", "tags_updated_successfully": "Tags updated successfully", "taken": "Taken", "taper_titrate_dosage": "Taper & Titrate Dosage", "target_dosage": "Target Dosage", "tax": "Tax", "tax_codes": "Tax Codes", "tax_components": "Tax Components", "tax_explanation": "Taxes applied to the price after discounts", "tax_factor_applies_to_subtotal_after_discounts": "Factor applies to subtotal after discounts", "tax_invoice": "Tax Invoice", "taxes": "Taxes", "team": "Team", "technical_details": "Technical Details", "template_deleted": "Template has been deleted", "template_permission_notice": "You don't have permission to create templates. Please contact your administrator.", "template_saved": "Report template saved successfully", "template_updated": "Report template updated successfully", "tender_amount": "Tender Amount", "tender_amount_tooltip": "Amount given by the customer", "tendered": "Tendered", "test": "Test", "test_results": "Test Results", "test_results_entry": "Test Results Entry", "test_type": "Type of test done", "tested_on": "Tested on", "tests": "Tests", "text": "Text", "text_or_fields_required_for_non_table_sections": "Text or fields are required for non-table sections", "text_required_for_custom_sections": "Text is required for custom sections", "text_settings": "Text Settings", "thank_you_for_choosing": "Thank you for choosing our care service", "thank_you_for_your_payment": "Thank you for your payment", "the_request_for_resources_placed_by_yourself_is": "The request for resource (details below) placed by yourself is", "therapy": "Therapy", "third_party_software_licenses": "Third Party Software Licenses", "this_action_cannot_be_undone": "This action cannot be undone.", "this_action_is_irreversible": "This action is irreversible. Once a file is archived it cannot be unarchived.", "this_bed_is_currently_occupied": "This bed is currently occupied. Please choose another bed.", "this_field_will_be_auto_generated": "This field will be auto-generated", "this_file_has_been_archived": "This file has been archived and cannot be unarchived.", "this_month": "This Month", "this_week": "This Week", "this_will_permanently_cancel_the_appointment_and_cannot_be_undone": "This will permanently cancel the appointment scheduled for {{date}} with {{practitioner}} at {{facility}} and cannot be undone", "this_will_permanently_remove_the_exception_and_cannot_be_undone": "This will permanently remove the exception and cannot be undone", "this_will_permanently_remove_the_scheduled_template_and_cannot_be_undone": "This will permanently remove the scheduled template and cannot be undone", "this_will_permanently_remove_the_session_and_cannot_be_undone": "This will permanently remove the session and cannot be undone", "this_year": "This Year", "thread_already_exists": "Thread with this title already exists", "tick_if_all_items_are_received_the_request_will_be_cleared_from_the_pending_list": "Tick if all items are received. The request will be cleared from the pending list.", "time": "Time", "time_of_death": "Time of death", "time_slot": "Time Slot", "title": "Title", "title_of_request": "Title of Request", "titrate_dosage": "Titrate Dosage", "to": "to", "to_be_conducted": "To be conducted", "to_bottom": "To Bottom", "to_date_equal_or_after_from_date": "To date must be equal to or after from date", "to_dispatch": "To Dispatch", "to_edit": "to edit", "to_proceed_with_registration": "To proceed with registration, please create a new patient.", "to_receive": "To Receive", "to_receive_description": "These items have been requested from other stores and are pending dispatch.", "to_top": "To Top", "to_view_available_slots_select_resource_and_date": "To view available slots, select a preferred resource and date.", "today": "Today", "token": "Token", "token_booking": "Token Booking", "token_no": "Token No.", "tokens_left": "{{ count }} left", "tomorrow": "Tomorrow", "top": "Top", "total": "Total", "total_amount": "Total Amount", "total_balance": "Total Balance", "total_base_price": "Total Base price", "total_beds": "Total Beds", "total_billed_before_adjustments": "Total Billed Before Adjustments", "total_discounts": "Total discounts", "total_gross": "Total Gross", "total_locations": "Total locations", "total_medicines": "Total Medicines", "total_net": "Total Net", "total_number_of_beds": "Total number of beds", "total_paid": "Total Paid", "total_patients": "Total Patients", "total_price": "Total Price", "total_qr_codes": "Total QR Codes", "total_ratio": "Total Ratio", "total_slots": "Total Slots", "total_staff": "Total Staff", "total_taxes": "Total taxes", "total_units": "Total Units", "total_users": "Total Users", "trade_name": "Trade Name", "transfer_allowed": "Transfer Allowed", "transfer_blocked": "Transfer Blocked", "transfer_status_updated": "Transfer Status Updated", "transfer_to_receiving_facility": "Transfer to receiving facility", "travel_within_last_28_days": "Domestic/international Travel (within last 28 days)", "treating_doctor": "Treating Doctor", "treatment_summary": "Treatment Summary", "treatment_summary__head_title": "Treatment Summary", "treatment_summary__heading": "INTERIM TREATMENT SUMMARY", "treatment_summary__print": "Print Treatment Summary", "triage_category": "Triage Category", "true": "True", "try_again": "Try Again", "try_again_later": "Try again later!", "try_different_abha_linking_option": "Want to try a different linking option, here are some more:", "try_different_search": "Try a different search term", "try_different_search_terms": "Try different search terms", "tube": "<PERSON><PERSON>", "two_factor_authentication": "Two Factor Authentication", "two_factor_authentication_active": "Two-factor authentication is currently active on your account.", "two_factor_authentication_backup_codes": "Two-Factor Authentication Backup Codes", "two_factor_authentication_backup_codes_error": "Failed to generate new backup codes.", "two_factor_authentication_backup_codes_regenerated": "New backup codes generated successfully.", "two_factor_authentication_disable": "Disable 2FA", "two_factor_authentication_disable_error": "Failed to disable two-factor authentication.", "two_factor_authentication_disabled_success": "Two-factor authentication disabled successfully", "two_factor_authentication_enable": "Enable two-factor authentication", "two_factor_authentication_enabled": "Two factor authentication is now enabled for your account", "two_factor_authentication_not_active": "Two-factor authentication is not currently active on your account.", "two_factor_authentication_note": "Two-factor authentication enhances your account security by requiring more than just a password, making it harder for unauthorised access.", "two_factor_authentication_regenerate_codes_tooltip": "Generate new backup codes. Your old backup codes will no longer work.", "two_factor_authentication_regenerating_codes": "Regenerate backup codes", "two_factor_authentication_setup_description": "Once configured, you will be required to enter a code generated by the two factor authenticator apps like, Duo mobile, Google Authenticator, Microsoft Authenticator app in order to sign into your account.", "two_factor_authentication_setup_error": "Failed to setup two-factor authentication", "two_factor_authentication_verify_error": "Failed to verify code", "type": "Type", "type_any_extra_comments_here": "type any extra comments here", "type_at_least_3_characters_to_search": "Type at least 3 characters to search", "type_b_cylinders": "B Type Cylinders", "type_c_cylinders": "C Type Cylinders", "type_collected": "Type Collected", "type_d_cylinders": "D Type Cylinders", "type_of_encounter": "Type of Encounter", "type_patient_age": "Type Patient Age", "type_patient_name": "Type Patient Name", "type_tested_information": "Type Tested Information", "type_to_search": "Type to search", "type_your_comment": "Type your comment", "type_your_reason_here": "Type your reason here", "uhid": "UHID", "unable_to_assess": "Unable to <PERSON><PERSON><PERSON>", "unable_to_get_current_location": "Unable to get current location", "unable_to_get_current_position": "Unable to get current position.", "unable_to_get_location: ": "Unable to get location: ", "unassign": "Unassign", "unavailable": "Unavailable", "unconfirmed": "Unconfirmed", "uniform": "Uniform", "unique": "Unique", "unique_help": "If enabled, this identifier must be unique across all patients.", "unique_id": "Unique Id", "unique_identifier": "Unique Identifier", "unique_url_for_questionnaire": "A unique URL-friendly identifier for this questionnaire", "unit": "Unit", "unit_a": "Years", "unit_ampule(s)": "Ampu<PERSON>(s)", "unit_d": "Days", "unit_drop(s)": "Drop(s)", "unit_g": "g", "unit_h": "Hours", "unit_mcg": "mcg", "unit_mg": "mg", "unit_min": "Minutes", "unit_ml": "ml", "unit_mo": "Months", "unit_ms": "Milliseconds", "unit_placeholder": "Select Unit", "unit_price": "Unit Price", "unit_price_components": "Unit Price Components", "unit_s": "Seconds", "unit_taper": "Taper", "unit_titrate": "Titrate", "unit_total": "Unit Total", "unit_tsp": "Tsp", "unit_unit(s)": "Unit(s)", "unit_wk": "Weeks", "units": "Units", "unknown": "Unknown", "unknown_question": "Unknown Question", "unlink_asset_bed_and_presets": "Delete linked presets and unlink bed", "unlink_asset_bed_caution": "This action will also delete all presets that are associated to this camera and bed.", "unlink_camera_and_bed": "Unlink this bed from this camera", "unlink_facility": "Unlink Facility", "unlink_facility_access": "The user will lose access to the facility", "unlink_facility_confirm": "Are you sure you want to unlink the facility", "unlink_facility_error": "Error while unlinking facility. Try again later.", "unlink_facility_success": "Facility unlinked successfully", "unlink_home_facility_error": "Error while unlinking home facility. Try again later.", "unlink_home_facility_success": "Home Facility cleared successfully", "unlink_skill": "Unlink Skill", "unlink_skill_access": "The user will not have the skill associated anymore.", "unlink_skill_confirm": "Are you sure you want to unlink the skill", "unlink_skill_error": "Error while unlinking skill. Try again later.", "unlink_skill_success": "Skill unlinked successfully", "unlink_this_facility": "Unlink Facility", "unpaid": "Unpaid", "unsaved_changes": "You have unsaved changes. Are you sure you want to leave?", "unsubscribe": "Unsubscribe", "unsubscribe_failed": "Unsubscribe failed.", "unsubscribed_successfully": "Unsubscribed Successfully.", "unsupported_browser": "Unsupported Browser", "unsupported_browser_description": "Your browser ({{name}} version {{version}}) is not supported. Please update your browser to the latest version or switch to a supported browser for the best experience.", "untitled_question": "Untitled Question", "unverified": "Unverified", "up": "Up", "up_shift": "Up Shift", "update": "Update", "update_asset": "Update Asset", "update_asset_service_record": "Update Asset Service Record", "update_available": "Update Available", "update_bed": "Update Bed", "update_charge_item_definition": "Update Charge Item Definition", "update_charge_item_definition_description": "Modify pricing components for services or items", "update_delivery": "Update Delivery", "update_department": "Update Department", "update_device": "Update Device", "update_device_with_type": "Update {{type}} Device", "update_discharge_details": "Update Discharge Details", "update_encounter": "Update Encounter", "update_encounter_details": "Update Encounter Details", "update_existing_facility": "Update the details of the existing facility.", "update_facility": "Update Facility", "update_facility_middleware_success": "Facility middleware updated successfully", "update_hospitalisation_details": "Update Hospitalisation Details", "update_identifier": "Update identifier", "update_location": "Update Location", "update_log": "Update Log", "update_organization": "Update Organization", "update_password": "Update Password", "update_patient_details": "Update Patient Details", "update_preset": "Update Preset", "update_preset_position_to_current": "Update preset's position to camera's current position", "update_record": "Update Record", "update_record_for_asset": "Update record for asset", "update_request": "Update Request", "update_resource_request": "Update Resource Request", "update_role": "Update Role", "update_shift_request": "Update Shift Request", "update_specimen_definition": "Update Specimen Definition", "update_status": "Update Status", "update_status_details": "Update Status/Details", "update_tag_config": "Update tag config", "update_time": "Update time", "update_user": "Update User", "update_user_role_organization": "Update the role for this user in the organization", "update_volunteer": "Reassign Volunteer", "updated": "Updated", "updated_on": "Updated On", "updated_successfully": "Updated successfully", "updates": "Updates", "updating": "Updating...", "updating_application": "Updating application...", "updating_facility": "Updating Facility...", "upload": "Upload", "upload_an_image": "Upload an image", "upload_file": "Upload File", "upload_files": "Upload Files", "upload_headings__consultation": "Upload New Consultation File", "upload_headings__patient": "Upload New Patient File", "upload_headings__sample_report": "Upload Sample Report", "upload_headings__supporting_info": "Upload Supporting Info", "upload_report": "Upload Report", "uploaded": "Uploaded", "uploaded_files": "Uploaded files", "uploading": "Uploading", "uploading_indicator": "Uploading...", "urgent": "<PERSON><PERSON>", "uri": "URI", "url": "URL", "url_required": "URL required", "usage": "Usage", "use": "Use", "use_address_as_permanent": "Use this address for permanent address", "use_auth_app": "Use authenticator app", "use_authenticator_app": "Use an authenticator app or browser extension to scan.", "use_backup_code": "Use a backup code", "use_existing_consent": "Use Existing Consent", "use_help": "Select the context in which this identifier is used (e.g., usual, official, temp, etc.)", "use_phone_number_for_emergency": "Use this phone number for emergency contact", "useful_for_reference_only": "Useful for reference only", "user_add_error": "Error while adding User", "user_added_successfully": "User added successfully", "user_delete_error": "Error while deleting User", "user_deleted_successfully": "User Deleted Successfully", "user_deleted_successfuly": "User Deleted Successfully", "user_details": "User Details", "user_details_update_error": "Error while updating user details", "user_details_update_success": "User details updated successfully", "user_friendly_name": "User Friendly Name", "user_not_available_for_appointments": "This user is not available for appointments", "user_qualifications": "Qualifications", "user_qualifications_note": "Enter appropriate qualifications for this user", "user_removed_success": "User removed from organization successfully", "user_role_update_success": "User role updated successfully", "user_type": "User Type", "user_updated_successfully": "User updated successfully", "username": "Username", "username_already_exists": "This username already exists", "username_available": "Username is available", "username_characters_validation": "Only <strong>lowercase letters, numbers, and . _ -  </strong>are allowed", "username_consecutive_validation": "Cannot contain <strong>consecutive special characters</strong>", "username_max_length_validation": "Use at most <strong>16 characters</strong>", "username_min_length_validation": "Use at least <strong>4 characters</strong>", "username_not_available": "Username is not available", "username_not_valid": "username is not valid", "username_start_end_validation": "Must start and end with a <strong>letter</strong> or <strong>number</strong>", "username_success_message": "All set! Your username is strong", "username_userdetails_not_found": "Unable to fetch details as username or user details not found", "username_valid": "Username is valid", "users": "Users", "users_management": "Users Management", "vacant": "Vacant", "vaccinated": "Vaccinated", "vaccine_name": "Vaccine name", "valid_from": "<PERSON><PERSON>", "valid_from_after_valid_untill": "Valid From must be before <PERSON><PERSON>l", "valid_integer_required": "Please enter a valid integer", "valid_otp_found": "Valid OTP found, Navigating to Appointments", "valid_period": "Valid Period", "valid_till_equal_or_after_valid_from": "Valid till must be equal to or after valid from date", "valid_to": "<PERSON><PERSON>", "valid_year_of_birth": "Please enter a valid year of birth (YYYY)", "validation_and_uniqueness": "Validation & Uniqueness", "validation_and_uniqueness_help": "Set whether this identifier is required and/or must be unique for each patient.", "validation_failed": "Validation failed", "value": "Value", "value_already_selected": "Value already selected", "value_set": "Value Set", "value_set_search_placeholder": "Type to search and select from the list", "valueset_created": "ValueSet created successfully", "valueset_preview": "Valueset Preview", "valueset_preview_description": "Preview the valueset before saving it", "valueset_updated": "ValueSet updated successfully", "valuesets": "ValueSets", "vehicle_preference": "Vehicle preference", "vendor": "Vendor/Distributor", "vendor_name": "Vendor Name", "ventilator_interface": "Respiratory Support Type", "ventilator_log": "Ventilator Log", "ventilator_modality": "Modality", "ventilator_mode": "Ventilator Mode", "ventilator_oxygen_modality": "Oxygen Modality", "ventilator_oxygen_modality_oxygen_rate": "Oxygen Flow Rate", "ventilator_spo2": "SpO₂", "verification": "Verification", "verification_details": "Verification Details", "verification_failed": "Verification Failed", "verification_status": "Verification Status", "verified_by": "Verified by", "verify": "Verify", "verify_and_link": "Verify and Link", "verify_code": "Verify Code", "verify_otp": "Verify OTP", "verify_otp_error": "Failed to verify <PERSON><PERSON>. Please try again later.", "verify_otp_success": "OTP has been verified successfully.", "verify_otp_success_login": "OTP has been verified successfully. Logging in.", "verify_patient": "<PERSON><PERSON><PERSON>", "verify_patient_identifier": "Please verify the patient identifier", "verify_patient_identity": "Verify Patient Identity", "verify_received_items": "Verify Received Items", "verify_using": "Verify Using", "verifying": "Verifying", "version": "Version", "video_call": "Video Call", "video_conference_link": "Video Conference Link", "video_not_supported": "Your browser does not support video playback.", "view": "View", "view_abdm_records": "View ABDM Records", "view_account": "View Account", "view_all": "View All", "view_all_details": "View All Details", "view_and_manage_account_invoices": "View and manage account invoices", "view_and_manage_account_payments": "View and manage account payments", "view_and_manage_accounts": "View and manage patient accounts", "view_and_manage_invoices": "View and manage invoices", "view_and_manage_patient_encounters": "View and manage patient encounters", "view_and_manage_payments": "View and manage payments", "view_asset": "View Assets", "view_associated_encounter": "View associated encounter", "view_care_team": "View Care Team", "view_cns": "View CNS", "view_completed_encounters": "View completed encounters", "view_consent": "View Consent", "view_consultation": "View Latest Encounter", "view_consultation_and_log_updates": "View Consultation / Log Updates", "view_dashboard": "View Dashboard", "view_details": "View Details", "view_dispensed": "View Dispensed", "view_encounter": "View Encounter", "view_facility": "View Facility", "view_facility_details": "View facility details", "view_files": "View Files", "view_history": "View History", "view_invoice": "View Invoice", "view_observation_history": "View Observation History", "view_patient": "View Patient", "view_patients": "View Patients", "view_payments": "View Payments", "view_prescriptions": "View Prescriptions", "view_report": "View Report", "view_request": "View Request", "view_requests": "View Requests", "view_service_details": "View Details", "view_specimen_history": "View Specimen History", "view_statement": "View Statement", "view_sub_locations": "View Sub-Locations", "view_update_patient_files": "View/Update patient files", "view_updates": "View Updates", "view_user": "View User", "view_user_profile": "View Profile", "view_users": "View Users", "village": "Village", "virtual_nursing_assistant": "Virtual Nursing Assistant", "visibility_settings": "Visibility Settings", "vital_signs": "Vital Signs", "vitals": "<PERSON><PERSON>", "vitals_monitor": "Vitals Monitor", "vitals_present": "Vitals Monitor present", "volunteer": "Volunteer", "volunteer_assigned": "Volunteer assigned successfully", "volunteer_contact": "Volunteer Contact", "volunteer_contact_detail": "Provide the name and contact details of a volunteer who can assist the patient in emergencies. This should be someone outside the family.", "volunteer_unassigned": "Volunteer unassigned successfully", "volunteer_update": "Volunteer updated successfully", "waitlist": "Waitlist", "ward": "Ward", "ward_stock": "<PERSON>", "warning": "Warning", "warranty_amc_expiry": "Warranty / AMC Expiry", "was_substituted": "Was Subsistuted?", "we_ve_sent_you_a_code_to": "We've sent you a code to", "weekly_schedule": "Weekly Schedule", "weekly_working_hours_error": "Average weekly working hours must be a number between 0 and 168", "weight": "Weight", "welcome_back": "Welcome back!", "welcome_back_to_hospital_dashboard": "Welcome back to the overview ", "what_facility_assign_the_patient_to": "What facility would you like to assign the patient to", "whatsapp_number": "Whatsapp Number", "whatsapp_number_same_as_phone_number": "WhatsApp number is same as phone number", "why_the_asset_is_not_working": "Why the asset is not working?", "width": "Width ({{unit}})", "with": "with", "working_status": "Working Status", "x": "X", "year": "Year", "year_of_birth": "Year of Birth", "year_of_birth_format": "Year of birth must be in YYYY format", "year_of_birth_must_be_present": "Year of birth must be present", "years": "years", "years_of_experience": "Years of Experience", "years_of_experience_of_the_doctor": "Years of Experience of the Doctor", "yes": "Yes", "yesterday": "Yesterday", "yet_to_be_decided": "Yet to be decided", "you_cannot_change_once_submitted": "You cannot change once submitted.", "you_have_unsaved_changes_what_would_you_like_to_do": "You have unsaved changes. What would you like to do?", "you_need_at_least_a_location_to_create_an_assest": "You need at least a location to create an assest.", "zoom_in": "Zoom In", "zoom_out": "Zoom Out"}