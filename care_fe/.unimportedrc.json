{"ignorePatterns": ["node_modules/**", "**/*.d.ts"], "ignoreUnimported": ["src/Locale/update_locale.js", "src/PluginRegistry.ts", "src/pages/Encounters/ReportBuilder/ReportBuilderSheet.tsx", "src/pluginTypes.ts", "src/service-worker.ts", "src/components/ui/definition-list.tsx", "src/pages/Admin/organizations/components/AdminOrganizationSelector.tsx"], "ignoreUnused": ["@fontsource/figtree", "@originjs/vite-plugin-federation", "@tailwindcss/vite", "@vitejs/plugin-react", "browserslist-useragent-regexp", "cross-env", "normalize-wheel"], "ignoreUnresolved": [["./pluginMap", ["src/pluginTypes.ts"]], ["@/supportedBrowsers", ["src/components/ErrorPages/BrowserWarning.tsx"]], ["tailwindcss/colors", ["node_modules/@tailwindcss/forms/src/index.js", "node_modules/@tailwindcss/typography/src/styles.js"]], ["tailwindcss/defaultTheme", ["node_modules/@tailwindcss/forms/src/index.js"]], ["tailwindcss/plugin", ["node_modules/@tailwindcss/forms/src/index.js", "node_modules/@tailwindcss/typography/src/index.js", "node_modules/@tailwindcss/container-queries/dist/index.js"]], ["tsx/cjs", ["node_modules/find-cypress-specs/src/index.js"]], ["virtual:pwa-register", ["src/index.tsx"]]], "respectGitignore": true, "entry": ["src/index.tsx", "vite.config.mts", "tailwind.config.js", "cypress.config.ts", "scripts/**"]}