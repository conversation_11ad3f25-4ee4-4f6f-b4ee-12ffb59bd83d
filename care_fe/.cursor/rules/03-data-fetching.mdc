---
description: 
globs: 
alwaysApply: false
---
# Data Fetching and API Guidelines

## TanStack Query Usage
- Use TanStack Query with the `query` and `mutate` utilities from `@/Utils/request/`
- Use appropriate query keys following the resource pattern
- Leverage built-in features for pagination and debouncing
- Implement proper error handling using the global error handler

## API Route Definitions

### Modern Route Pattern
Routes are defined in dedicated API files (`*Api.ts`) within the corresponding type directory:

```typescript
// src/types/user/userApi.ts
export default {
  list: {
    path: "/api/v1/users/",
    method: HttpMethod.GET,
    TRes: Type<PaginatedResponse<UserBase>>(),
  },
  create: {
    path: "/api/v1/users/",
    method: HttpMethod.POST,
    TRes: Type<UserBase>(),
    TBody: Type<CreateUserModel>(),
  }
} as const;

// src/types/facility/facilityApi.ts
export default {
  getFacility: {
    path: "/api/v1/facility/{id}/",
    method: HttpMethod.GET,
    TRes: Type<FacilityData>(),
  },
  updateMonetaryComponents: {
    path: "/api/v1/facility/{facilityId}/set_monetary_codes/",
    method: HttpMethod.POST,
    TRes: Type<FacilityData>(),
    TBody: Type<{
      discount_codes: Code[];
      discount_monetary_components: MonetaryComponentRead[];
    }>(),
  }
} as const;
```

### Legacy Route Pattern
Legacy routes are defined in `src/Utils/api.tsx`:

```typescript
export const routes = {
  auth: {
    login: {
      path: "/api/v1/auth/login/",
      method: "POST",
    } as ApiRoute<LoginResponse, never, LoginData>,
    logout: {
      path: "/api/v1/auth/logout/",
      method: "POST",
    } as ApiRoute<void>
  }
} as const;
```

## Query Patterns

### Basic Queries
```typescript
// Using modern routes
const { data } = useQuery({
  queryKey: ['users'],
  queryFn: query(userApi.list)
});

// Using legacy routes
const { data } = useQuery({
  queryKey: [routes.auth.me.path],
  queryFn: query(routes.auth.me)
});
```

### With Parameters
```typescript
// Path parameters
const { data } = useQuery({
  queryKey: ['user', username],
  queryFn: query(userApi.get, {
    pathParams: { username }
  })
});

// Query parameters
const { data } = useQuery({
  queryKey: ['facilities', searchTerm],
  queryFn: query(facilityApi.getAllFacilities, {
    queryParams: { search: searchTerm }
  })
});
```

## Mutation Patterns
```typescript
// Using modern routes
const { mutate: createUser } = useMutation({
  mutationFn: mutate(userApi.create),
  onSuccess: () => {
    queryClient.invalidateQueries(['users'])
  }
});

// Using legacy routes
const { mutate: login } = useMutation({
  mutationFn: mutate(routes.auth.login)
});
```

## Error Handling
- Errors are handled globally by default
- Common scenarios are automatically handled:
  - Session expiry → Redirects to /session-expired
  - Bad requests (400/406) → Shows error notification
- Use `silent: true` option to suppress error notifications
- Custom error handling can be implemented using `onError` callbacks
