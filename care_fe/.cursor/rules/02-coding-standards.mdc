---
description: 
globs: 
alwaysApply: false
---
# Coding Standards

## TypeScript Guidelines
- Use TypeScript for all new code
- Prefer interfaces over types for object definitions
- Use functional components with proper type definitions
- Avoid using `any` type; use proper type definitions
- Use type inference where possible

## Component Structure
- One component per file is preferred. 
- Prefer default exports for components
- Follow the component naming pattern: `ComponentName.tsx`
- Place components in appropriate feature directories
- Keep components small and focused

## Styling Guidelines
- Use Tailwind CSS for styling
- Follow mobile-first responsive design
- Use Shadcn UI components when available

## State Management
- Use TanStack Query for API data management
- Prefer React Context for global state
- Use local state for component-specific data

## File Naming
- Use kebab-case for directories: `auth-wizard/`
- Use PascalCase for component files: `AuthWizard.tsx`
- Use camelCase for utility files: `useAuth.ts`

## Testing
- Write tests in Cypress for E2E testing
- Follow testing guidelines in `cypress/docs/`
- Test components in isolation
- Write meaningful test descriptions
