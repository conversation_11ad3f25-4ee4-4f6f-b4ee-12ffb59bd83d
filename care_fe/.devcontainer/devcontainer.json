// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/javascript-node
{
  "name": "care_fe",
  "image": "mcr.microsoft.com/devcontainers/javascript-node:1-22-bookworm",
  "features": {
    "ghcr.io/devcontainers/features/docker-outside-of-docker:1": {
      "moby": true,
      "installDockerBuildx": true,
      "version": "latest",
      "dockerDashComposeVersion": "v2"
    }
  },
  "customizations": {
    "vscode": {
      "settings": {},
      "extensions": [
        "esbenp.prettier-vscode",
        "bradlc.vscode-tailwindcss",
        "yoavbls.pretty-ts-errors"
      ]
    }
  },
  "hostRequirements": {
    "cpus": 4
  },
  "waitFor": "onCreateCommand",
  "postCreateCommand": "npm install",
  "postAttachCommand": {
    "server": "npm run dev"
  },
  "portsAttributes": {
    "4000": {
      "label": "Application",
      "onAutoForward": "openPreview"
    }
  },
  "forwardPorts": [4000]
}
