# Backend URL
REACT_CARE_API_URL=https://careapi.ohc.network

# Dashboard URL
REACT_DASHBOARD_URL=

# GitHub URL (default: https://github.com/ohcnetwork)
REACT_GITHUB_URL=

# OHCN URL (default: https://ohc.network?ref=care)
REACT_OHCN_URL=

# Care Apps. repo@branch seperated by commas
REACT_ENABLED_APPS="ohcnetwork/care_livekit_fe@main,ohcnetwork/care_scribe"

# Main logo (JSON string with light and dark properties)
REACT_HEADER_LOGO=

# Main logo (JSON string with light and dark properties)
# Example: REACT_MAIN_LOGO="{\"light\": \"https://cdn.ohc.network/light-logo.svg\", \"dark\": \"https://cdn.ohc.network/dark-logo.svg\"}"
REACT_MAIN_LOGO=

# State logo (JSON string with light and dark properties)
REACT_STATE_LOGO=

# Custom logo (JSON string with light and dark properties)
REACT_CUSTOM_LOGO=

# Custom alternative logo (JSON string with light and dark properties)
REACT_CUSTOM_LOGO_ALT=

# Custom description
REACT_CUSTOM_DESCRIPTION=

# reCAPTCHA site key
REACT_RECAPTCHA_SITE_KEY=

# Sentry DSN
REACT_SENTRY_DSN=

# Sentry environment (default: staging)
REACT_SENTRY_ENVIRONMENT=

# Flag to allow some fields in patient registration to be non-mandatory
REACT_ENABLE_MINIMAL_PATIENT_REGISTRATION=true

# JWT token refresh interval (in milliseconds) (default: 5 minutes)
REACT_JWT_TOKEN_REFRESH_INTERVAL=

# Minimum encounter date (default: 2020-01-01)
REACT_MIN_ENCOUNTER_DATE=

# Default Encounter Type (default: "hh" - Home Health)
REACT_DEFAULT_ENCOUNTER_TYPE=

# Available languages to switch between (2 Digit language code seperated by comas. See src->Locale->config.ts for available codes)
REACT_ALLOWED_LOCALES="en,hi,ta,ml,mr,kn"

# ISO 3166-1 Alpha-2 code for the default country code (default: "IN")
REACT_DEFAULT_COUNTRY=

# Maps fallback URL template (default:"https://www.openstreetmap.org/?mlat={lat}&mlon={long}&zoom=15")
REACT_MAPS_FALLBACK_URL_TEMPLATE=

# OTP resend timeout in seconds (eg. 90 seconds) (default : 120 seconds)
REACT_APP_RESEND_OTP_TIMEOUT=

# Maximum image upload size allowed (in megabytes)
REACT_APP_MAX_IMAGE_UPLOAD_SIZE_MB=