<!doctype html>
<html lang="en" translate="no">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="minimum-scale=1, initial-scale=1, width=device-width, shrink-to-fit=no, interactive-widget=resizes-content" />
    <title>%REACT_APP_TITLE%</title>
    <meta name="description" content="%REACT_APP_META_DESCRIPTION%" />
    <meta property="og:title" content="%REACT_APP_TITLE%" />
    <meta property="og:description" content="%REACT_APP_META_DESCRIPTION%" />
    <meta property="og:image" content="%REACT_APP_COVER_IMAGE%" />
    <meta property="og:url" content="%REACT_PUBLIC_URL%" />
    <meta property="og:site_name" content="%REACT_APP_TITLE%" />
    <meta name="twitter:title" content="%REACT_APP_TITLE%" />
    <meta name="twitter:description" content="%REACT_APP_META_DESCRIPTION%" />
    <meta name="twitter:image" content="%REACT_APP_COVER_IMAGE%" />
    <meta name="twitter:card" content="summary" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Care" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
    <link
      rel="apple-touch-icon"
      href="/images/icons/apple-touch-icon-180x180.png" />
    %REACT_INJECTED_HEAD%
  </head>

  <body>
    <noscript>
      <div style="
        font-family: sans-serif;
        color: #d32f2f;
        font-size: 16px;
        text-align: center;
        padding: 20px;
        background-color: #ffebee;
        border: 1px solid #d32f2f;
        border-radius: 8px;
        margin: 20px;
        max-width: 500px;
        margin: 40px auto;
      ">
        <h2 style="margin-bottom: 10px;">🚫 JavaScript is Disabled</h2>
        <p>
          This application requires JavaScript to function properly. It looks like JavaScript is either 
          <strong>disabled</strong> or <strong>blocked</strong> in your browser.
        </p>
        <p style="margin-top: 10px;">
          🔧 <strong>How to Enable JavaScript:</strong>
        </p>
          <ul style="text-align: left; display: inline-block; margin: 10px auto;">
            <li>Go to your browser settings.</li>
            <li>Find the "JavaScript" section.</li>
            <li>Enable JavaScript and refresh this page.</li>
          </ul>
        <p>If you are using a script blocker, allow scripts from this site.</p>
      </div>
    </noscript>
    <div id="root">
      <style nonce="7e14cf80">
        .App-logo {
          height: 10vmin;
          pointer-events: none;
          filter: brightness(80%);
        }
        @media (prefers-reduced-motion: no-preference) {
          .App-logo {
            animation: App-logo-blink 1s linear infinite;
          }
        }
        @keyframes App-logo-blink {
          0% {
            opacity: 0;
          }
          25% {
            opacity: 0.5;
          }
          50% {
            opacity: 1;
          }
          75% {
            opacity: 0.5;
          }
          100% {
            opacity: 0;
          }
        }
        .temp-loading {
          display: grid;
          place-items: center;
          height: 100vh;
          text-align: center;
        }
      </style>
      <div class="temp-loading">
        <img
          class="App-logo"
          src="/images/care_logo_gray.svg"
          alt="Care is loading"
        />
      </div>
    </div>
    <script type="module" src="/src/index.tsx"></script>
    <!-- Script injected by Vite from environment variable -->
    %REACT_INJECTED_HTML%
  </body>
</html>
